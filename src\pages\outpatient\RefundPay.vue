<template>
<!--清单详情页-->
    <div class="about-time">

        <dingdanxinxi></dingdanxinxi>
        <div class="list-group">
           <group class="cell-group">
             <cell title="支付方式" value="微信支付">
                <img slot="icon" class="pay-methodImg" style="display:block;margin-right:5px;" src="../../assets/weixinzhifu.png">
             </cell>
           </group>
        </div>

        <!-- <p><img src="../../assets/warn.png" alt="">温馨提示：请在30分钟内完成支付，逾期号将会作废，需重新挂号</p> -->
       <div class="bottom-btn"><button @click="cancelFun()">确定</button></div>
    </div>
</template>


<script>
import { Cell, Group } from "vux";
import { ajax,storage,toolsUtils} from "../../common";
import apiUrls from '../../config/apiUrls';
import dingdanxinxi from '../../components/outpatient/dingdanxinxi';
export default {
  name: "index",
  components: {
    Cell,
    Group,
    dingdanxinxi
  },
  data() {
    return {
      title: "选择科室"
    };
  },
  methods:{
    cancelFun(){
      this.$router.push('registerRecord')
    }
  },

  computed:{}
};
</script>

<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 23px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #fff;
  border: 1px solid #24bab8;
}
.bottom-btn button:first-child {
  background:#fff;
  color: #24BAB8;
  margin-right:0.1rem;
}
.bottom-btn button:last-child {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  margin-left:0.1rem;
}
.pay-methodImg{
  height:0.48rem;
}
</style>
