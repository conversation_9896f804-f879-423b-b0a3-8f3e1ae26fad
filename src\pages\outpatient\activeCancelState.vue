<template>
<!--主动取消和超时取消显示的页面-->
    <div class="about-time">
        
        <dingdanxinxi></dingdanxinxi>
        <div class="list-group">
           <group class="cell-group">
             <cell title="支付方式" value="微信支付">
                <img slot="icon" class="pay-methodImg" style="display:block;margin-right:5px;" src="../../assets/weixinzhifu.png">
             </cell>
           </group>
        </div>

        <p><img src="../../assets/warn.png" alt="">已取消订单，请重新预约</p>
        <div class="bottom-btn"><button>再次预约</button></div>
    </div>
</template>

<script>
import { Cell, Group } from "vux";
import dingdanxinxi from '../../components/outpatient/dingdanxinxi'
export default {
  name: "index",
  components: {
    Cell,
    Group,
    dingdanxinxi
  },
  data() {
    return {
      title: "选择科室"
    };
  },
  methods: {},
  computed: {},
  created() {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:nth-child(1)::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 23px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #fff;
  border: 1px solid #24bab8;
}

.bottom-btn button:last-child {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  margin-left:0.1rem;
}
.pay-methodImg{
  height:0.48rem;
}
</style>

