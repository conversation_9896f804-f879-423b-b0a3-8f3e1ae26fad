{"name": "showban", "version": "1.0.0", "description": "A Vue.js project", "author": "mic <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js"}, "dependencies": {"axios": "^0.19.2", "crypto-js": "^4.2.0", "element-ui": "^2.4.4", "fastclick": "^1.0.6", "jquery": "^3.3.1", "jsbarcode": "^3.11.0", "nprogress": "^0.2.0", "vant": "^2.12.53", "vue": "2.5.2", "vue-axios": "^2.1.5", "vue-concise-slider": "^2.4.8", "vue-qr": "^2.3.0", "vue-router": "^3.0.1", "vuex": "^2.1.1", "vuex-i18n": "^1.3.1", "vux": "^2.2.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-loader": "^7.1.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "eventsource-polyfill": "^0.9.6", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "less": "^2.7.1", "less-loader": "^2.2.3", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "2.5.2", "vux-loader": "^1.0.56", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-middleware": "^1.10.0", "webpack-dev-server": "^2.9.1", "webpack-hot-middleware": "^2.16.1", "webpack-merge": "^4.1.0", "yaml-loader": "^0.4.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["iOS >= 7", "Android >= 4.1"]}