<template>
  <div class="personal">
    <div class="qr-code">
      <!-- <img src="../../assets/NewImg/outpatientImg/qrcode.png" alt /> -->
      <vue-qr
        :logoSrc="imgUrl"
        :text="qRCode"
        :size="360"
        :margin="4"
        :logoMargin="4"
        logoBackgroundColor="#fff"
        :logoScale="48"
        :logoCornerRadius="8"
      ></vue-qr>
    </div>
    <div class="input-list">
      <div class="inpit-cell" v-for="(item, index) in cellData" :key="index">
        <div class="input-title">{{ item.name }}</div>
        <div class="input-field">
          <input type="text" readonly :value="item.value" />
        </div>
      </div>
    </div>
    <!-- <van-cell-group>
      <van-field v-model="name" label="姓名" readonly />
      <van-field v-model='idCard' label="身份证号码" readonly/>
      <van-field v-model="phoneNumber" label="手机号" readonly />
      <van-field v-model="patid" label="院内id" readonly />
    </van-cell-group> -->
    <div class="submitBtn" @click="tocardList">
      <span>返回</span>
    </div>
    <!-- <p @click="delHealthCard()" class="tips">{{tips}} ></p> -->
  </div>
</template>
<script>
import VueQr from "vue-qr";
import { ajax, toolsUtils, storage } from "../common";
import apiUrls from "../config/apiUrls";
export default {
  components: {
    VueQr
  },
  name: "personal",
  data() {
    return {
      name: "",
      idCard: "",
      phoneNumber: "",
      codeUrl: "",
      // patCode:"",
      qRCode: "",
      hos_code: "",
      imgUrl: require("../../src/assets/icon/logo_.png"),
      tips: "解绑电子健康卡",
      cellData: [
        {
          name: "姓名",
          value: ""
        },
        {
          name: "身份证号码",
          value: ""
        },
        {
          name: "手机号",
          value: ""
        }
      ]
    };
  },
  created() {
    var that = this;

    if (this.$route.query.type == 0) {
      this.getMyData();
    } else {
      Promise.all([this.getDataAES()]).then(res => {
        if (!res[0]) {
          return;
        }

        // that.patCode = storage.session.get("CheckpatCode");

        // if(that.patCode==null){
        //   toolsUtils.alert('未获取到您的patCode，请先去申请门诊卡')
        //   setTimeout(function () {
        //     that.$router.push({
        //       path:'/addPatient',

        //     })
        //   }, 2000);
        //   return
        // }

        let data = {
          wxid: storage.session.get("openid"),
          type: "1",
          zjlx: "01",
          zjhm: that.idCard,
          xm: that.name,
          brdh: that.phoneNumber,
          bindType: "1",
          bindZjlx: "01",
          bindZjhm: that.idCard
          // 'patCode':that.patCode
        };

        // var url = "http://qtyywx.qtyy.com/personal?vuid=07BA790D4875FC264C10217396ABB8D679BA3F57E2BA5C6B435CCFDEBD33B469&wxid=of5r1wBEjYgoPyZkracX1xu_3fBI&custom=undefined&virtualCardNum=14382994621302226170&name=PTbJxmwR1KKXz9neTB13Dw%3D%3D&zjhm=XVG0UXvQ3Kwme09c7LI8FT7hjXM%2BQq5Mz5%2Ba754qvtA%3D&brdh=8x1G50lKW6Y0YTFFvRpAqw%3D%3D&timestamp=1605776505136&sign=96abc40496996bb987007e75be5aa2cf75c07f111630f9e4d15e4e14daf177ae";
        ajax.post(apiUrls.bindHealthCardqrCode, data).then(r => {
          if (!r.data.success) {
            toolsUtils.alert(r.data.returnMsg);
            return;
          }
          // console.log(r.data.returnData)
          that.qRCode = r.data.returnData;
        });
      });
    }
  },
  methods: {
    tocardList() {
      this.$router.push({
        path: "/cardListV2"
      });
    },
    getMyData() {
      var user = JSON.parse(storage.session.get("user"));
      var data = {
        patIdCard: this.$route.query.patIdCard,
        wxid: user.accountCode
      };
      // console.log(data);
      ajax.post(apiUrls.registerHealthCard, data).then(r => {
        // console.log(r)
        if (!r.data.success) {
          if (r.data.returnMsg != "") {
            toolsUtils.alert(r.data.returnMsg);
            return;
          }
          if (r.data.returnData != "") {
            var url = r.data.returnData;
            // console.log(url);
            window.location.href = url;
            return;
          }
        }
        // console.log(***********)
        this.cellData[0].value = r.data.returnData.name;
        this.cellData[1].value = r.data.returnData.idCard;
        this.cellData[2].value = r.data.returnData.phoneNumber;
        // this.cellData[3].value = r.data.returnData.patCode;
        this.qRCode = r.data.returnData.qrCode;
      });
    },
    getDataAES() {
      let data = {
        xm: this.$route.query.name,
        zjhm: this.$route.query.zjhm,
        brdh: this.$route.query.brdh
      };
      return new Promise((resolve, reject) => {
        ajax.post(apiUrls.EncoderAES, data).then(r => {
          this.name = r.data.returnData.xm;
          this.idCard = r.data.returnData.zjhm;
          this.phoneNumber = r.data.returnData.brdh;
          // this.patCode = storage.session.get("CheckpatCode");

          this.cellData[0].value = this.name;
          this.cellData[1].value = this.idCard;
          this.cellData[2].value = this.phoneNumber;
          // this.cellData[3].value = this.patCode;

          resolve(true);
        });
      });
    }
  }
};
</script>
<style scoped>
.personal {
  width: 100%;
  height: 100%;
}
.personal .qr-code {
  width: 100%;
  height: 4.5rem;
  background: #fff;
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.personal .qr-code img {
  width: 3.45rem;
  height: 3.45rem;
}

.personal .submitBtn {
  width: 90%;
  height: 1.08rem;
  border-radius: 8px;
  position: fixed;
  bottom: 0.2rem;
  left: 5%;
  font-size: 0.36rem;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: linear-gradient(to right, #3dc0f3, #2dc4c2);
  /* box-shadow: 1px 1px 2px #52675a; */
}

.input-list {
  width: 100vw;
  height: 100%;
  margin: 0.2rem auto 0;
}
.input-list .inpit-cell {
  width: 100%;
  height: 0.8rem;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  color: #4a4a4a;
  display: flex;
  font-size: 0.3rem;
}
.inpit-cell .input-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 0.26rem;
  padding-left: 0.24rem;
  flex: 2;
}
.inpit-cell .input-field {
  flex: 5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.inpit-cell .input-field input {
  flex: 1;
  height: 100%;
  border: 0;
  margin: 0 !important;
  padding: 0 !important;
}
</style>
