<template>
  <div class="medicalInsuranceBinding">
    <h3 class="title">[90210]人员定点备案</h3>
    <div>
      <div class="custom-field">
        <div class="field-label">姓名</div>
        <div class="field-content">
          <input type="text" v-model="form.name" placeholder="请输入姓名" disabled class="custom-input" />
        </div>
      </div>
      <!-- <van-field
          readonly
          clickable
          :value="form.cardType"
          label="证件类型"
          placeholder="请选择证件类型"
          @click="showCardType = true"
        /> -->
      <!-- <van-popup v-model="showCardType" position="bottom">
          <van-picker
            show-toolbar
            :columns="columns"
            @confirm="onConfirmCardType"
            @cancel="showCardType = false"
          />
        </van-popup> -->
      <div class="custom-field">
        <div class="field-label">证件号码</div>
        <div class="field-content">
          <input type="text" v-model="form.idCardNo" placeholder="请输入证件号码" disabled class="custom-input" />
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">人员编号</div>
        <div class="field-content">
          <input type="text" v-model="form.number" placeholder="请输入人员编号" disabled class="custom-input" />
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">开始日期</div>
        <div class="field-content">
          <div class="date-picker" @click="showPicker = true">
            {{ format(form.startTime) || '点击选择日期' }}
          </div>
        </div>
      </div>
      <van-popup v-model="showPicker" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择年月日"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="onConfirm"
          @cancel="showPicker = false"
        />
      </van-popup>
      <div class="custom-field">
        <div class="field-label">结束日期</div>
        <div class="field-content">
          <input type="text" v-model="form.endTime" placeholder="请输入结束日期" disabled class="custom-input" />
        </div>
      </div>
      <!-- <van-field name="radio" label="定点事件类型">
          <template #input>
            <van-radio-group v-model="form.type" direction="horizontal">
              <van-radio name="1">变更</van-radio>
              <van-radio name="2">暂停</van-radio>
            </van-radio-group>
          </template>
        </van-field> -->
      <div class="custom-field">
        <div class="field-label">主辅标志</div>
        <div class="field-content">
          <van-radio-group v-model="form.flag" direction="horizontal" icon-size="18px">
            <van-radio :name="1" checked-color="#24bab8">主就医点</van-radio>
            <van-radio :name="2" checked-color="#24bab8">辅就医点</van-radio>
          </van-radio-group>
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">就医点类型</div>
        <div class="field-content">
          <van-radio-group v-model="form.radio" direction="horizontal" icon-size="18px">
            <van-radio :name="1" checked-color="#24bab8">工作地</van-radio>
            <van-radio :name="2" checked-color="#24bab8">居住地</van-radio>
          </van-radio-group>
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">定点医药机构编号</div>
        <div class="field-content">
          <input type="text" v-model="form.organizationNo" placeholder="请输入定点医药机构编号" disabled class="custom-input" />
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">定点医药机构名称</div>
        <div class="field-content">
          <input type="text" v-model="form.organizationName" placeholder="请输入定点医药机构名称" disabled class="custom-input" />
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">备注</div>
        <div class="field-content">
          <input type="text" v-model="form.note" placeholder="请输入备注" class="custom-input" />
        </div>
      </div>
      <div class="submit">
        <van-button round block color="#24bab8" @click="onSubmit"
          >定点备案</van-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import "vant/lib/index.css";
export default {
  name: "medicalInsuranceBinding",
  data() {
    return {
      showPicker: false,
      form: {
        name: "",
        cardType: "居民身份证",
        idCardNo: "",
        number: "",
        startTime: new Date(),
        endTime: "2099-12-31",
        type: "1",
        flag: 1,
        radio: 1,
        organizationNo: "H44190100094",
        organizationName: "东莞市桥头医院",
        note: ""
      },
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(2099, 12, 30),
      currentDate: new Date(),
      showCardType: false,
      columns: [
        "居民身份证",
        "护照",
        "港澳居民来往内地通行证",
        "台湾居民来往内地通行证"
      ]
    };
  },
  created() {
    this.submitFun();
  },
  methods: {
    submitFun() {
      var id_card = JSON.parse(storage.session.get("id_card")),
        mdtrt_cert_type = JSON.parse(storage.session.get("mdtrt_cert_type")),
        data = {
          id_card: id_card,
          mdtrt_cert_type: mdtrt_cert_type
        };
      ajax
        .post(apiUrls.GetPsnno, data)
        .then(r => {
          r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.form.name = r.returnData.psn_name;
          this.form.idCardNo = id_card;
          this.form.number = r.returnData.psn_no;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    onConfirm(time) {
      this.form.startTime = time;
      this.showPicker = false;
    },
    onConfirmCardType(val) {
      this.form.cardType = val;
      this.showCardType = false;
    },
    onSubmit() {
      var psn_no = JSON.parse(storage.session.get("psn_no")),
        data = {
          psn_no: psn_no,
          begndate: this.format(this.form.startTime),
          memo: this.form.note,
          fix_local_type: String(this.form.radio),
          fix_main_scd_flag: String(this.form.flag)
        };
      // console.log(data);
      // return;
      ajax.post(apiUrls.UserYBbinding, data).then(r => {
        r = r.data;
        console.log(r);
        if (r.success == false) {
          // toolsUtils.alert(r.returnMsg);
          //window.location.replace('/medicalInsurance');
          this.$dialog
            .alert({
              title: "提示",
              message: r.returnMsg,
              confirmButtonColor: "#24bab8"
            })
            .then(() => {
              this.$router.replace({ path: "/medicalInsurance" });
            });
        } else {
          // toolsUtils.alert(r.returnData);
          //window.location.replace('/medicalInsurance');
          this.$dialog
            .alert({
              title: "提示",
              message: r.returnData,
              confirmButtonColor: "#24bab8"
            })
            .then(() => {
              this.$router.replace({ path: "/medicalInsurance" });
            });
        }
      });
      // .catch(e => {
      //   toolsUtils.alert("程序异常:" + JSON.stringify(e));
      // });
    },
    format(time) {
      //获取年月日，时间
      let year = time.getFullYear();
      let mon =
        time.getMonth() + 1 < 10
          ? "0" + (time.getMonth() + 1)
          : time.getMonth() + 1;
      let date = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
      let newDate = year + "-" + mon + "-" + date;
      return newDate;
    }
  }
};
</script>

<style lang="less" scoped>
.medicalInsuranceBinding {
  background: #fff;
  font-size: 0.32rem;
  .title {
    font-size: 0.36rem;
    text-align: center;
    padding: 0.4rem 0;
  }
  .submit {
    padding: 0.32rem;
  }
  /deep/.van-field--disabled .van-field__label {
    color: #646566;
  }

  .custom-field {
    position: relative;
    display: flex;
    box-sizing: border-box;
    width: 100%;
    padding: 10px 16px;
    overflow: hidden;
    color: #323233;
    font-size: 14px;
    line-height: 24px;
    background-color: #fff;
    border-bottom: 1px solid #ebedf0;
  }

  .field-label {
    width: 90px;
    margin-right: 12px;
    color: #646566;
  }

  .field-content {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .custom-radio {
    display: inline-flex;
    align-items: center;
    margin-right: 16px;
    cursor: pointer;
  }

  .custom-radio input[type="radio"] {
    margin-right: 6px;
    width: 18px;
    height: 18px;
    vertical-align: middle;
  }

  .radio-text {
    font-size: 14px;
  }

  .custom-input {
    width: 100%;
    border: none;
    outline: none;
    padding: 0;
    margin: 0;
    color: #323233;
    line-height: 24px;
    background-color: transparent;
  }

  .custom-input:disabled {
    color: #969799;
    background-color: transparent;
  }

  .date-picker {
    width: 100%;
    color: #323233;
    line-height: 24px;
    cursor: pointer;
  }
}
</style>
