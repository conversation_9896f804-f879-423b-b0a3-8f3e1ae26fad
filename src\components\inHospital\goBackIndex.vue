<template>
    <div class="go-to-index">
			 <router-link to='/'>
			     <img src="../../assets/logo.png" alt="">
			 </router-link>
		</div>
</template>

<script>

export default {
  name: "index",
  components: {
    
  },
  
  data() {
    return {
      title: "header",
      
    };
  },
  created: function() {
    
  },
  methods: {
    
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.go-to-index{
	height:50px;
	width:50px;
	overflow: hidden;
	border-radius: 100%;
	box-shadow: 0 1px 10px 0 rgba(34,36,38,.3);
}
.go-to-index img{
	display: block;
	height:100%;
	width:100%;
}
</style>