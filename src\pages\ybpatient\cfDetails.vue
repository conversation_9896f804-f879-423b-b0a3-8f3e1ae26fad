<template>
  <!--处方缴费详情-->
  <div class="cf-detail">
    <div class="detail-head">
      <img src="../../assets/pre_icon.png" alt="" />
      <div class="detail-info">
        <p>
          处方编号<span>{{ cfList.visitId }}</span>
        </p>
        <p>
          挂号科室<span>{{ cfList.deptName }}</span>
        </p>
        <p>
          开方时间<span>{{ cfList.visitNo }}</span>
        </p>
      </div>
      <div class="pay-money">
        待缴费
        <div>
          <span>{{ cfList.payAmount / 100 }}</span
          >元
        </div>
      </div>
    </div>
    <div class="tab-head">
      <span>处方缴费详情</span>
      <span>共{{ listData.length }}个项目</span>
    </div>
    <!-- 详情列表 -->
    <div class="list-wrap">
      <ul>
        <!-- <li :key="index" v-for="(list,index) in listData" @click="linkCfMingxi(list.recipeId)"> -->
        <li :key="index" v-for="(list, index) in listData">
          <div>{{ list.recipeName }}</div>
          <div>
            ￥{{ list.payAmount / 100 }}
            <!-- <span>查看明细</span> -->
          </div>
        </li>
      </ul>
    </div>
    <!-- 底部的fixed -->
    <div class="footer">
      <div class="left-div">
        待缴金额<span>{{ cfList.payAmount / 100 }}</span>
      </div>
      <div class="button-group">
        <button @click="onUnlockPrescription" class="button-group__unlock">
          处方解锁
        </button>
        <button @click="allBtn()" class="button-group__pay">
          全部支付
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Dialog } from "vant";
export default {
  name: "index",
  components: {},
  data() {
    return {
      title: "选择科室",
      listData: [],
      cfList: []
    };
  },
  methods: {
    //跳转到处方明细
    linkCfMingxi(kind) {
      storage.session.set("cfList", JSON.stringify([this.cfList]));
      this.$router.push({
        path: "/cfMingxis",
        query: {
          recipeId: kind.trim(),
          deptId: this.$route.query.deptId,
          doctorId: this.$route.query.doctorId
        }
      });
    },
    getcfDetail() {
      var data = {
        //patCardNo: this.$route.query.code,
        hospitalId: "",
        visitNo: this.$route.query.visitNo
      };
      ajax
        .post(apiUrls.getNoPayRecipeInfo, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          this.listData = [];
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无订单~~"
            });
            return;
          }
          this.listData = r.returnData;
          //  console.log(r.returnData);
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    },
    allBtn() {
      var arr = [];
      for (var i = 0, listLength = this.listData.length; i < listLength; i++) {
        arr.push(this.listData[i].recipeId.trim());
      }
      var data = arr.join(",");
      this.linkCfMingxi(data);
    },
    //解锁处方
    onUnlockPrescription() {
      Dialog.confirm({
        title: "处方解锁",
        message: "点击该按钮将退出页面重新获取处方，是否继续？"
      })
        .then(() => {
          // on confirm
          this.unlockPrescription();
        })
        .catch(() => {
          // on cancel
        });
    },
    unlockPrescription() {
      var arr = [];
      for (var i = 0, listLength = this.listData.length; i < listLength; i++) {
        arr.push(this.listData[i].recipeId.trim());
      }
      var data = {
        visitId: this.cfList.visitId,
        type: "0", //解锁处方
        detailId: arr.join(",")
      };
      ajax
        .post(apiUrls.IsLockPatOrder, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          WeixinJSBridge.call("closeWindow");
          return;
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    }
  },
  computed: {},
  created() {
    this.getcfDetail();
    this.cfList = JSON.parse(storage.session.get("chooseCf"));
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.cf-detail {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding-bottom: 1rem;
}
.detail-head {
  padding: 0.24rem;
  background: #fff;
  display: flex;
  align-items: center;
}
.detail-head img {
  display: block;
  height: 1.2rem;
  width: 1.2rem;
}
.detail-head .detail-info {
  font-size: 0.28rem;
  flex-basis: 0;
  flex-grow: 1;
  padding: 0 0.24rem;
}
.detail-head .detail-info p {
  height: 0.4rem;
  line-height: 0.4rem;
}
.detail-head .detail-info p span {
  margin-left: 8px;
}
.detail-head .pay-money {
  font-size: 0.28rem;
  color: red;
  text-align: center;
}
.detail-head .pay-money div {
  height: 30px;
}
.detail-head .pay-money span {
  font-size: 0.44rem;
}
.tab-head {
  display: flex;
  height: 0.48rem;
  align-items: center;
  font-size: 0.24rem;
  padding: 0 0.24rem;
}
.tab-head span:first-child {
  flex-basis: 0;
  flex-grow: 1;
}
.list-wrap li {
  height: 1rem;
  display: flex;
  align-items: center;
  color: #000;
  padding: 0 0.24rem;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}
.list-wrap li div:first-child {
  flex: 1;
  font-size: 0.32rem;
}
.list-wrap li div:last-child {
  font-size: 0.28rem;
}
.list-wrap li div span {
  color: #30c0a7;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1rem;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 0 0.24rem;
  border-top: 1px solid #f0f0f0;
}
.footer .left-div {
  flex: 1;
  font-size: 0.28rem;
  color: #000;
}
.footer .left-div span {
  color: red;
  margin-left: 0.3rem;
}
.footer .button-group {
  display: flex;
}
.button-group .button-group__unlock {
  width: 1.6rem;
  height: 0.65rem;
  line-height: 0.7rem;
  font-size: 0.28rem;
  border-radius: 3px;
  color: #30c0a7;
  border: 1px solid #30c0a7;
  display: block;
  margin-right: 0.3rem;
}
.button-group .button-group__pay {
  width: 1.6rem;
  height: 0.65rem;
  line-height: 0.7rem;
  font-size: 0.28rem;
  border-radius: 3px;
  background: #30c0a7;
  color: #fff;
  border: none;
  display: block;
}
</style>
