@charset "utf-8";
*{
	padding: 0;
	margin: 0;
	box-sizing: border-box;
	font-family: "微软雅黑";
}
.cl:after {
	display: block;
	content: "";
	clear: both;
}

.charge_page {
	width: 100%;
	height:100%;
	overflow: auto;
	padding: 0.24rem;
	background: #F0EFF6;;
	
}

.flex {
	display: flex;
	display: -webkit-flex;
}


.charge_page .moneyWrap {
	background: #fff;
	margin-bottom: 0.2rem;
	border-radius: 5px;
	overflow: hidden;
}

.charge_page .moneyWrap h3 {
	font-size: 0.32rem;
	color: #7F8FA4;
	font-weight: normal;
	border-bottom: 1px solid #DFE3E9;
	padding:0.2rem 0.24rem;
}
.charge_page .moneyWrap h3 span{
	display: block;
	font-size: 0.6rem;
	color: #354052;
}
.charge_page .allMoneyWrap {
	padding: 0.24rem 0.24rem 0;
}

.charge_page .moneyNum {
	margin: 0 -0.12rem;
}

.charge_page .moneyNum div {
	width: 33.33%;
	float: left;
	padding: 0 0.12rem;
	height: 0.84rem;
	margin-bottom: 0.24rem;
}
.charge_page .moneyNum div .active{
	background: #86B0FF;
	color:#fff;
}
.charge_page .moneyNum div span {
	display: block;
	height: 100%;
	width: 100%;
	line-height: 0.84rem;
	border: 1px solid #86B0FF;
	border-radius: 5px;
	text-align: center;
	font-size: 0.32rem;
	color: #86B0FF;
}
.modeGroup{
	width:100%;
	border-bottom: 1px solid #ccc;
	padding: 0 0.24rem;
}
.modeGroup:last-child{
	border:none;
}
.modeGroup label{
	height:1.08rem;
	width:100%;
	align-items: center;
}
.modeGroup label:after{
	display: block;
	height:0.4rem;
	width:0.4rem;
	content: "";
	border:1px solid #ccc;
	border-radius: 100%;
	font-size: 0.36rem;
	text-align: center;
	line-height: 0.4rem;
}
.modeGroup input:checked~label::after{
	background-image: url(../../assets/check.png);
	background-size: 100%;
	border:none;
}
.modeGroup label .modeImg{
	height:0.6rem;
	overflow: hidden;
}
.modeGroup label .modeImg img{
	height:100%;
	display: block;
}
.modeGroup label .modeMethod{
	flex-grow: 1;
	flex-basis: 0;
	font-size: 0.32rem;
	color: #4A4A4A;
	padding: 0 0.24rem;
}
.modeWrap{
	padding: 0 !important;
}

.confirmBtn{
	display: block;
	width:100%;
	color:#fff;
	height:1.08rem;
	border:none;
	font-size: 0.36rem;
	background-image: linear-gradient(-180deg, #3AD1CF 0%, #22B8B6 100%);
	border: 1px solid #24BAB8;
	border-radius: 5px;
	margin-top:0.48rem;
}
