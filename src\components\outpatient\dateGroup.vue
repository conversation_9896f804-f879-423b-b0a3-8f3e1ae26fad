<template>
    <div id="viewReport">
			 <div class="selectTimeline">
			<div class="title">
				<div>{{choicStrat}} 至 {{choicEnd}}</div>
				<div class="right">
					<span @click="choicTime()">确认时间</span>
					<i class="calendarIcon"><img src="../../assets/calendar.png" alt=""></i>
				</div>
			</div>
			<div class="calendar">
				<!-- <div class="colorTip clearfix">
					<div><i></i>已选</div>
				</div> -->
				<div class="months">
					<i class="prevYear yearIcon"><img src="../../assets/prev_year.png" alt=""></i>
					<i class="prevMonth monthIcon"><img src="../../assets/prev_gray.png" alt=""></i> 2017年7月
					<i class="nextMonth monthIcon" @click="nextMonthFun()"><img src="../../assets/next_yellow.png" alt=""></i>
					<i class="nextYear yearIcon"><img src="../../assets/next_year.png" alt=""></i>
				</div>
				<div class="week">
					<ul class="clearfix">
						<li>日</li>
						<li>一</li>
						<li>二</li>
						<li>三</li>
						<li>四</li>
						<li>五</li>
						<li>六</li>
					</ul>
				</div>
				<div class="days">
					<div class="dayWrap">
						<div class="day">
							<ul class="clearfix">
								
								<li :class="list.day==''?'':'choosable'" :key="index" v-for="(list,index) in lisData">{{list.day}}</li>
								
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		</div>
</template>

<script>
import $ from "jquery";
export default {
  name: "index",
  components: {},
  data() {
    return {
      title: "header",
      indexYear: "",
      indexMonth: "",
      dateStart: "", //选中的开始日期
      dateEnd: "", //选中的结束日期
      lisData: [],
      choicStrat: "",
      choicEnd: "",
      Year_cs: "",
      Month_cs: "",
      Day_cs: ""
    };
  },
  created: function() {
    this.dangqianDate();
    this.indexDate();
  },
  methods: {
    dangqianDate() {
      var newDate = new Date(),
        year = newDate.getFullYear(),
        month = newDate.getMonth() + 1,
        day = newDate.getDate();
      this.Year_cs = year;
      this.Month_cs = month;
      this.Day_cs = day;
    },
    indexDate() {
      var newDate = new Date(this.Year_cs + "-" + this.Month_cs),
        year = newDate.getFullYear(),
        month = newDate.getMonth() + 1,
        weekDay = new Date(year + "-" + month + "-" + 1).getDay();
      console.log(weekDay);
      //每个月的天数
      var days = 0;
      if (month == 4 || month == 6 || month == 9 || month == 11) {
        //小月
        days = 30;
      } else if (month == 2) {
        if (year % 4 == 0 || year % 100 == 0) {
          //闰年是二月份天数
          days = 29;
        } else {
          days = 28;
        }
      } else {
        //大月
        days = 31;
      }
      //循环空的li
      var lisData_1 = [];
      for (var i = 0; i < weekDay; i++) {
        lisData_1.push({
          day: ""
        });
      }
      //循环整月的日历表
      for (var i = 0; i < days; i++) {
        lisData_1.push({
          day: i + 1,
          date: year + "-" + month + "-" + (i + 1)
        });
      }
      this.lisData = lisData_1;
    },
    //日历
    calendarFn(
      idx,
      calendarLi,
      day,
      days,
      dayWrap,
      dayWrapStr,
      dayWrapDiv,
      nextMonth,
      prevMonth,
      nextImg,
      prevImg,
      dayChoose,
      chooseStartOne
    ) {
      var that = this;
      //日历中每块的高度
      var day_liW = calendarLi.width() * 0.7142857142857143;
      calendarLi.height(day_liW);
      calendarLi.css("line-height", day_liW + "px");
      var dayH = day.innerHeight();
      days.height(dayH);
      var daysW = days.width(); //750
      dayWrapDiv.width(daysW);
      var length = day.length;
      dayWrap.width(dayWrapDiv.width() * length);

      //=========================================================================================

      //tab左右切换
      var index = 0;
      var bol = false;
      var ind = 0;
      // $(".dayWrap").css("left",-daysW+"px");
      //点击切换到下个月
      // nextMonth.on("click", function() {
      //   that.Month_cs = that.Month_cs + 1;
      //   console.log(that.Month_cs);
      //   prevImg.attr("src", require("../../assets/prev_yellow.png"));
      //   bol = true;
      //   if (index < length - 1) {
      //     index++;
      //   } else {
      //     index = length - 1;
      //   }
      //   if (index > length - 1) {
      //     bol = false;
      //     dayWrap.css(
      //       "left",
      //       -(dayWrap.innerWidth() - dayWrapDiv.innerWidth())
      //     );
      //   } else {
      //     dayWrap.animate(
      //       {
      //         left: -daysW * index + "px"
      //       },
      //       {
      //         duration: 500
      //         //easing: "easeOutSine"
      //       }
      //     );
      //   }
      //   var dayH = day.eq(index).innerHeight();
      //   days.height(dayH);
      //   days.css({
      //     transition: "all 400ms"
      //   });
      //   ind++;
      //   if (ind >= length - 1) {
      //     ind = length - 1;
      //     nextImg.attr("src", require("../../assets/next_gray.png"));
      //   }
      // });
      //点击切换到上个月
      // prevMonth.on("click", function() {
      //   nextImg.attr("src", require("../../assets/next_yellow.png"));
      //   bol = true;
      //   if (index > 0) {
      //     index--;
      //   } else {
      //     index = 0;
      //   }
      //   if (index < 0) {
      //     bol = false;
      //     dayWrap.css("left", "0px");
      //   } else {
      //     dayWrap.animate(
      //       {
      //         left: -daysW * index + "px"
      //       },
      //       {
      //         duration: 500
      //         //easing: "easeOutSine"
      //       }
      //     );
      //   }
      //   var dayH = day.eq(index).innerHeight();
      //   days.height(dayH);
      //   ind--;
      //   if (ind < length - (length - 1)) {
      //     ind = 0;
      //     prevImg.attr("src", require("../../assets/prev_gray.png"));
      //   }
      // });

      //=========================================================================================

      // 选日期范围

      var dayLi = calendarLi; //所有的日期元素(li)
      // 遍历循环添加自定义的属性index
      dayLi.each(function(i) {
        $(this).attr("index", i);
      });

      // var idx = 0;//用来判断第几次点击日历，1：第一次点击，2：第二次点击 ，>2：重置为0
      var indexStart; //标记上次点击日历的下标

      dayChoose.each(function(i) {
        dayChoose.eq(i).on("click", function() {
          idx++;
          if (idx == 1) {
            startFn(dayChoose.eq(i), $(this));
          }
          if (idx == 2) {
            var indexEnd = parseInt($(this).attr("index"));
            that.dateEnd = that.lisData[indexEnd].date;
            console.log("结束" + indexEnd);
            if (indexEnd >= indexStart) {
              //如果第二次点击的日期大于第一次
              // 移除当前的图片并添加新的箭头向右图片
              $(dayWrapStr + " .day li.choosable.chooseStartOne")
                .removeClass("chooseStartOne")
                .addClass("chooseStart");
              // 给对应第二次点击日期添加箭头向左图片
              dayChoose.eq(i).addClass("chooseEnd");
              // 进一步判断第二次点击是否跟第一次一样
              if (dayChoose.eq(i).hasClass("chooseStart")) {
                dayChoose.eq(i).removeClass("chooseStart chooseEnd"); //移除被点击的所有图片
              }

              var size = indexEnd - indexStart - 1; //计算同一月份中两个日期之间有多少天
              console.log("之间有" + size + "个");
              if (size > 0) {
                var a = indexStart;
                while (a < indexEnd - 1) {
                  a += 1;
                  // tip:进一步判断li标签里面是否包含有日期
                  // 只有包含有日期的才添加背景颜色
                  if (!dayLi.eq(a).text() == "")
                    dayLi.eq(a).addClass("isChoose");
                }
              }
            } else {
              //第二次点击的日期没有大于第一次
              idx = 1;
              startFn(dayChoose.eq(i), $(this));
            }
          }
          if (idx >= 2) {
            idx = 0;
          }

          // 添加向右的图片，并记录当前点击日期的下标
          function startFn(obj, obj1) {
            dayLi.removeClass("chooseStartOne chooseStart chooseEnd isChoose");
            obj.addClass("chooseStartOne");

            var index = parseInt(obj1.attr("index"));

            indexStart = index;
            console.log("开始" + indexStart);
            // var =parseInt(indexStart)
            that.dateStart = that.lisData[indexStart].date;
          }
        });
      });
    },
    //确认时间按钮
    choicTime() {
      this.choicStrat = this.dateStart;
      this.choicEnd = this.dateEnd;
      console.log(this);
    },
    //日历的操作事件
    operateFun() {
      var that=this;
      var calendarLi = $("#viewReport .calendar .day li");
      var day = $("#viewReport .calendar .day");
      var days = $("#viewReport .calendar .days");
      var dayWrap = $("#viewReport .calendar .dayWrap");
      var dayWrapStr = "#viewReport .dayWrap";
      var dayWrapDiv = $("#viewReport .calendar .dayWrap div");
      var nextMonth = $("#viewReport .nextMonth");
      var prevMonth = $("#viewReport .prevMonth");
      var nextImg = $("#viewReport .nextMonth img");
      var prevImg = $("#viewReport .prevMonth img");
      var dayChoose = $("#viewReport .days .day li.choosable");

      var calendarShow = $("#viewReport .selectTimeline .title");
      var calendar = $("#viewReport .calendar");
      var calendarBol = true;
      var affirmBtn = $("#viewReport .selectTimeline .title .right span");
      var calendarIcon = $("#viewReport .selectTimeline .title .calendarIcon");
      var onceBol = true;
      var idx = 0;
      calendarShow.on("click", function() {
        if (calendarBol) {
          calendar.show();
          idx = 0;
          if (onceBol) {
            that.calendarFn(
              idx,
              calendarLi,
              day,
              days,
              dayWrap,
              dayWrapStr,
              dayWrapDiv,
              nextMonth,
              prevMonth,
              nextImg,
              prevImg,
              dayChoose,
              null,
              that.dateStart,
              that.dateEnd,
              that.lisData
            ); //调用日历
            console.log(idx);
            onceBol = false;
          }
          $(".days .day li").removeClass(
            "chooseStartOne chooseStart chooseEnd isChoose"
          );
          affirmBtn.show();
          calendarIcon
            .find("img")
            .attr("src", require("../../assets/calendar2.png"));
          calendarBol = false;
        } else {
          calendar.hide();
          affirmBtn.hide();
          calendarIcon
            .find("img")
            .attr("src", require("../../assets/calendar.png"));
          calendarBol = true;
        }
      });
      //点击切换
      var reportsNav = $("#viewReport .reports .reportsNav li");
      var reportList = $("#viewReport .reports .reportList");
      reportsNav.on("click", function() {
        var thisIndex = $(this).index();
        $(this)
          .addClass("active")
          .siblings()
          .removeClass("active");
        reportList
          .eq(thisIndex)
          .addClass("on")
          .siblings()
          .removeClass("on");
      });
    },
    nextMonthFun(){
      this.Month_cs=this.Month_cs+1;
    }
  },
  computed: {},
  mounted() {
    let that = this;
    this.operateFun();
    $(function() {
      //选出诊日期
      var dateChange = $("#registrationCase .dateLine ul li");
      dateChange.on("click", function() {
        $(this)
          .addClass("active")
          .siblings()
          .removeClass("active");
      });
      // 挂号情况
      var timeChange = $("#registrationCase .timeBox li.timecase .time");
      var patients = $("#registrationCase .timeBox .patients");
      var timeImg = $("#registrationCase .timeBox .right .dir img");
      timeChange.on("click", function() {
        patients.slideUp(200);
        timeImg.attr("src", "img/down_gray.png");
        var thisLi = $(this).next();
        var thisImg = $(this).find("img");
        if (thisLi.is(":hidden")) {
          thisLi.slideDown(200);
          thisImg.attr("src", require("../../assets/up_gray.png"));
        } else {
          thisLi.slideUp(200);
          thisImg.attr("src", require("../../assets/down_gray.png"));
        }
      });
    });
  },
  watch: {
    Year_cs: function() {
      this.indexDate();
    },
    Month_cs: function() {
      this.indexDate();
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
#wrap.viewReport {
  background-color: #f7fafa;
}
#viewReport .header {
  padding: 0.24rem 0.24rem 0.24rem 0.28rem;
  overflow: hidden;
  background-color: #fff;
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  margin-bottom: 0.24rem;
}
#viewReport .header .head {
  width: 1.2rem;
  height: 1.2rem;
  float: left;
  border-radius: 50%;
  margin-right: 0.24rem;
}
#viewReport .header .info {
  float: left;
}
#viewReport .header .info .name {
  font-size: 0.32rem;
  color: #4d4d4d;
  line-height: 0.44rem;
  margin-bottom: 0.02rem;
}
#viewReport .header .info .relation,
#viewReport .header .info .num {
  font-size: 0.26rem;
  color: #999999;
  line-height: 0.36rem;
}
#viewReport .header .changePeople {
  float: right;
  height: 1.2rem;
  line-height: 1.2rem;
  position: relative;
  padding-right: 0.401rem;
}
#viewReport .header .changePeople span {
  font-size: 0.32rem;
  color: #23b3b3;
}
#viewReport .header .changePeople i {
  display: inline-block;
  width: 0.214rem;
  margin-left: 0.16rem;
  position: absolute;
  top: 50%;
  margin-top: -0.176rem;
}

#viewReport .reports {
  margin-top: 0.24rem;
}
#viewReport .reports .reportsNav {
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  border-top: 1px solid rgba(204, 204, 204, 0.5);
  box-sizing: border-box;
  height: 0.88rem;
  background-color: #fff;
}
#viewReport .reports .reportsNav ul {
  overflow: hidden;
}
#viewReport .reports .reportsNav li {
  width: 50%;
  box-sizing: border-box;
  float: left;
  text-align: center;
  padding: 0.14rem 0;
  position: relative;
  font-size: 0.24rem;
}
#viewReport .reports .reportsNav li div {
  border-right: 1px solid rgba(204, 204, 204, 0.5);
  height: 0.6rem;
  line-height: 0.6rem;
}
#viewReport .reports .reportsNav li:last-child div {
  border-right: none;
}
#viewReport .reports .reportsNav li i {
  width: 0.96rem;
  height: 0.08rem;
  background-color: transparent;
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -0.48rem;
}
#viewReport .reports .reportsNav li span {
  display: inline-block;
  color: #4a4a4a;
}
#viewReport .reports .reportsNav li.active span {
  color: #23b3b3;
}
#viewReport .reports .reportsNav li.active i {
  background-color: #23b3b3;
}

#viewReport .reports .reportList {
  display: none;
}
#viewReport .reports .reportList.on {
  display: block;
}
#viewReport .reports .reportList li {
  padding: 0.26rem 0 0 0.28rem;
  position: relative;
  background-color: #fff;
}
#viewReport .reports .reportList li .icon {
  width: 0.42rem;
  position: absolute;
  left: 0.28rem;
  top: 50%;
  margin-top: -0.21rem;
}
#viewReport .reports .reportList li .reportCon {
  margin-left: 0.96rem;
  overflow: hidden;
  padding: 0 0.24rem 0.26rem 0;
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
}
#viewReport .reports .reportList li:last-child .reportCon {
  border-bottom: none;
}

#viewReport .reports .reportList li .reportCon .left {
  float: left;
  line-height: 0.74rem;
}
#viewReport .reports .reportList li .reportCon .left .num {
  color: #4d4d4d;
  font-size: 0.28rem;
}
#viewReport .reports .reportList li .reportCon .left .timeLine {
  font-size: 0.24rem;
  line-height: 0.34rem;
  color: #999999;
}
#viewReport .reports .reportList li .reportCon .right {
  float: right;
  margin-top: 0.02rem;
  overflow: hidden;
}
#viewReport .reports .reportList li .reportCon .right > div span:nth-child(1) {
  color: #999999;
  font-size: 0.26rem;
  line-height: 0.36rem;
  margin-right: 0.44rem;
}
#viewReport .reports .reportList li .reportCon .right > div span:nth-child(2) {
  float: right;
  font-size: 0.26rem;
  line-height: 0.36rem;
  color: #4d4d4d;
}
#viewReport .reports .noReports {
  height: 2.34rem;
  line-height: 2.34rem;
  font-size: 0.24rem;
  color: #999999;
  text-align: center;
  background-color: #fff;
  display: none;
}
#viewReport .tip {
  height: 0.88rem;
  line-height: 0.88rem;
  text-align: center;
  font-size: 0.24rem;
  color: #999999;
}
#viewReport .tip span {
  color: #4a90e2;
}

#viewReport .selectTimeline .title {
  height: 0.96rem;
  line-height: 0.92rem;
  background-color: #fff;
  border-top: 1px solid rgba(204, 204, 204, 0.5);
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  color: #4a4a4a;
  font-size: 0.28rem;
  padding-left: 0.24rem;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}
#viewReport .selectTimeline .title .right {
  width: 2.12rem;
  height: 0.92rem;
  position: absolute;
  right: 0;
  top: 0;
  padding-right: 0.84rem;
  color: #23b3b3;
  font-size: 0.32rem;
  box-sizing: border-box;
}
#viewReport .selectTimeline .title .right span {
  display: none;
}
#viewReport .selectTimeline .title .calendarIcon {
  position: absolute;
  right: 0.24rem;
  top: 50%;
  width: 0.38rem;
  margin-top: -0.2rem;
}
#viewReport .selectTimeline .title .calendarIcon img {
  display: block;
  width: 100%;
}
#viewReport .calendar {
  background-color: #fff;
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  display: none;
}
#viewReport .calendar .months {
  height: 0.88rem;
  line-height: 0.88rem;
  text-align: center;
  font-size: 0.28rem;
  color: #4a4a4a;
  position: relative;
}
#viewReport .calendar .months i.prevMonth {
  position: absolute;
  left: 0.72rem;
  height: 0.88rem;
  line-height: 0.96rem;
  padding-right: 0.3rem;
  padding-left: 0.24rem;
}
#viewReport .calendar .months i.nextMonth {
  position: absolute;
  right: 0.72rem;
  height: 0.88rem;
  line-height: 0.96rem;
  padding-left: 0.3rem;
  padding-right: 0.24rem;
}
#viewReport .calendar .months i.prevYear {
  position: absolute;
  left: 0rem;
  height: 0.88rem;
  line-height: 0.96rem;
  padding-right: 0.3rem;
  padding-left: 0.24rem;
}
#viewReport .calendar .months i.nextYear {
  position: absolute;
  right: 0;
  height: 0.88rem;
  line-height: 0.96rem;
  padding-left: 0.3rem;
  padding-right: 0.24rem;
}
#viewReport .calendar .months i.monthIcon img {
  display: inline-block;
  width: 0.188rem;
}
#viewReport .calendar .months i.yearIcon img {
  display: inline-block;
  width: 0.224rem;
}
#viewReport .calendar .week {
  height: 0.48rem;
  line-height: 0.48rem;
  color: #7a7a7a;
}
#viewReport .calendar .week li {
  float: left;
  font-size: 0.28rem;
  width: 14.28571428571429%;
  text-align: center;
}
#viewReport .calendar .week li:last-child {
  margin-right: 0;
}
#viewReport .calendar .days {
  padding-top: 0.2rem;
  padding-bottom: 0.08rem;
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
}
#viewReport .calendar .days .dayWrap {
  position: absolute;
  left: 0;
  overflow: hidden;
}
#viewReport .calendar .days .dayWrap div {
  float: left;
}
#viewReport .calendar .days .day li {
  float: left;
  font-size: 0.36rem;
  width: 14.28571428571429%;
  box-sizing: border-box;
  text-align: center;
  margin: 0 0 0.08rem 0;
  color: #4a4a4a;
}
#viewReport .calendar .days .day li.chooseStartOne {
  background: url(../../assets/rlStart1.png) 0 0/100% 100% no-repeat;
  color: #fff;
}
#viewReport .calendar .days .day li.chooseStart {
  background: url(../../assets/rlStart.png) 0 0/100% 100% no-repeat;
  color: #fff;
}
#viewReport .calendar .days .day li.chooseEnd {
  background: url(../../assets/rlEnd.png) 0 0/100% 100% no-repeat;
  color: #fff;
}
#viewReport .calendar .days .day li + .isChoose {
  background-color: rgba(35, 179, 179, 0.3);
  color: #3d715d;
}
.choosable{
  height:0.76rem;
  line-height: 0.76rem;
}
</style>