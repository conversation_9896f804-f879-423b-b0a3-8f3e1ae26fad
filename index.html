<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="pragram" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate,max-age=0">
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
  <title>东莞市桥头医院</title>
</head>
<style>
  html,
  body {
    height: 100%;
    width: 100%;
    background-color: #f3f3f3 !important;
  }
</style>

<body>
  <div id="app-box"></div>
  <!-- built files will be auto injected -->
</body>

</html>

<!-- 限制微信字体大小 -->
<script type="text/javascript">
  (function () {
       if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
           handleFontSize();
       } else {
           if (document.addEventListener) {

               document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);

           } else if (document.attachEvent) {

               document.attachEvent("WeixinJSBridgeReady", handleFontSize);

               document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
           }
       }
       function handleFontSize() {
           // 设置网页字体为默认大小
           WeixinJSBridge.invoke('setFontSizeCallback', {
               'fontSize': 0
           });
           // 重写设置网页字体大小的事件
           WeixinJSBridge.on('menu:setfont', function () {
               WeixinJSBridge.invoke('setFontSizeCallback', {
                   'fontSize': 0
               });
           });
       }
    })();
    //限定微信内打开
    // var ua = navigator.userAgent.toLowerCase();
    //   var isWeixin = ua.indexOf('micromessenger') != -1;
    //   var isAndroid = ua.indexOf('android') != -1;
    //   var isIos = (ua.indexOf('iphone') != -1) || (ua.indexOf('ipad') != -1);
    //   if (!isWeixin) {
    //     document.head.innerHTML = '<title>抱歉，出错了</title><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0"><link rel="stylesheet" type="text/css" href="https://res.wx.qq.com/open/libs/weui/0.4.1/weui.css">';
    //     document.body.innerHTML = '<div class="weui_msg"><div class="weui_icon_area"><i class="weui_icon_info weui_icon_msg"></i></div><div class="weui_text_area"><h4 class="weui_msg_title">请在微信客户端打开链接</h4></div></div>';
    //   }
</script>

