<template>
  <div class="addPatients">
    <van-notice-bar
      color="#24bab8"
      background="#fffdf4" 
      left-icon="volume-o"
      text="仅限有诊疗卡号的外籍人员用户和暂无身份证儿童。"
    />
    <div class="form-wrap">
      <div class="input-wrap">
        <h3>请填写就诊人信息</h3>
        <label for="">
          <input
            type="text"
            v-model="name"
            placeholder="请输入姓名"
          />
        </label>
        <label for="">
          <input
            type="text"
            v-model.trim="patCardNo"
            placeholder="请输入院内就诊卡号"
          />
        </label>
      </div>
      <input type="button" value="添加" @click="patientAdd()"/>
    </div>
  </div>
</template>

<script>
import { ajax, storage, dataUtils } from "../common";
import apiUrls from "../config/apiUrls";

export default {
  data(){
    return{
      name: '',
      patCardNo: ''
    }
  },
  methods: {
    //添加儿童卡
    patientAdd() {
      var userInfo = JSON.parse(storage.session.get("user"));
      if (storage.session.get("openid") == null) {
        this.$router.push({ path: "/oauth?type=register" });
        return;
      }
      //非空验证
      if (dataUtils.isPatName(this.name) != true) {
        // toolsUtils.alert(dataUtils.isPatName(this.accmodel.name));
        alert(dataUtils.isPatName(this.name));
        return;
      }
      if (this.patCardNo == "") {
        // toolsUtils.alert(dataUtils.isPatName(this.accmodel.name));
        alert("请输入正确的诊疗卡号");
        return;
      }
        var data = {
          name: this.name,
          patCardNo: this.patCardNo.toUpperCase(),
          id_card: userInfo.idCard,
        };
        console.log(data);
        ajax
          .post(apiUrls.AddFamilyTypesForChildren, data)
          .then((r) => {
            console.log(r);
            r = r.data;
            if (!r.success) {
              if (r.returnMsg == "当前卡信息错误！") {
                // toolsUtils.alert(
                //   "您输入就诊卡信息与院内不一致，请联系客服咨询"
                // );
                alert("您输入就诊卡信息与院内不一致，请联系客服咨询");
              } else {
                // toolsUtils.alert(r.returnMsg);
                alert(r.returnMsg);
              }
              return;
            }
            this.$router.back(-1);
            return;
          })
     },
  },
}
</script>

<style lang="less" scoped>
.addPatients {
  height: 100%;
  width: 100%;
  overflow: auto;
  .form-wrap {
    padding: 0.5rem 0.48rem;
  }
  .form-wrap > div {
    width: 100%;
    background: #ffffff;
    box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.35),
      0 2px 6px 0 rgba(36, 186, 184, 0.4), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
    border-radius: 5px;
    padding: 0 0.4rem 0.6rem 0.4rem;
    margin-bottom: 0.42rem;
    overflow: hidden;
  }
  .input-wrap {
    display: flex;
    flex-wrap: wrap;
  }

  .form-wrap input[type="button"] {
    width: 100%;
    display: block;
    background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
    border: 1px solid #24bab8;
    border-radius: 5px;
    height: 0.96rem;
    font-size: 0.32rem;
    color: #ffffff;
  }

  .input-wrap label {
    height: 0.96rem;
    background: #f6f6f6;
    box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    border-radius: 5px;
    display: flex;
    margin-bottom: 0.28rem;
    flex-basis: 100%;
    align-items: center;
  }
  .input-wrap label input {
    flex-grow: 1;
    border: none;
    background: transparent;
    height: 100%;
    outline: none;
    padding: 0 0.28rem;
    font-size: 0.32rem;
  }

  .form-wrap input[type="submit"] {
    width: 100%;
    display: block;
    background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
    border: 1px solid #24bab8;
    border-radius: 5px;
    height: 0.96rem;
    font-size: 0.32rem;
    color: #ffffff;
  }
  .input-wrap h3 {
    font-size: 0.32rem;
    color: #354052;
    height: 1.16rem;
    flex-basis: 100%;
    flex-grow: 1;
    background: #fdfbfb;
    margin: 0 -0.4rem 0.3rem;
    line-height: 1.16rem;
    padding: 0 0.4rem;
  }
  .input-wrap p {
    font-size: 0.26rem;
    color: #fd7070;
    margin-bottom: 0.2rem;
  }
  .input-wrap label input::-webkit-input-placeholder {
    color: #d9d9d9;
  }
}
</style>