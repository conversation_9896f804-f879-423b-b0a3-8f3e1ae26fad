<template>
  <div class="check-in-list">
    <div class="user-info">
      <div class="head-wrap">
        <div class="patient-info">
          <div class="patient-name-wrap">
            <div>您好，{{ patcard.patName }}</div>
            <div class="change-patient">
              <span @click="showPopup()">切换就诊人</span>
              <img src="../../assets/change_patients.png" alt="" />
            </div>
          </div>
          <div>诊疗卡号：{{ patcard.patCode }}</div>
        </div>
        <div class="code-img">
          <img src="../../assets/user_face.png" alt="" />
        </div>
      </div>
    </div>
    <div class="list-wrap" v-show="this.UserHisOrder != ''">
      <div class="list-item">
        <div class="order-num">门诊预约</div>
        <div class="item-wrap">
          <span class="item-title">就诊人</span>
          <span class="item-text">{{ UserHisOrder.name }}</span>
          <span class="item-title">诊疗卡号</span>
          <span class="item-text">{{ UserHisOrder.card_no }}</span>
          <span class="item-title">医生姓名</span>
          <span class="item-text">{{ UserHisOrder.dr_name }}</span>
          <span class="item-title">就诊科室</span>
          <span class="item-text">{{ UserHisOrder.dept_name }}</span>
          <span class="item-title">就诊时间</span>
          <span class="item-text">{{ UserHisOrder.shift_name }}</span>
          <span class="item-title">订单日期</span>
          <span class="item-text">{{ UserHisOrder.book_time }}</span>
          <span class="item-title">挂号费</span>
          <span class="item-text">{{ UserHisOrder.zj_price }}</span>
        </div>
        <div class="btn-wrap">
          <div class="check-in-btn" @click="userReported()">去报到</div>
        </div>
      </div>
    </div>
    <div v-transfer-dom>
      <x-dialog v-model="showHideOnBlur" class="dialog-demo" hide-on-blur>
        <div class="img-box">
          <div class="popup-wrap">
            <h3>家人姓名</h3>
            <div class="popup-content">
              <div class="patient-list">
                <div class="left-solid">
                  <div></div>
                </div>
                <ul>
                  <li
                    v-for="(list, index) in userList"
                    :key="index"
                    @click="changeUser(list.patCode)"
                  >
                    <input
                      type="radio"
                      :id="setId(index)"
                      name="patient"
                      :checked="list.patCode == indexCard ? 'checked' : ''"
                    />
                    <label :for="setId(index)">{{ list.patName }}</label>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </x-dialog>
    </div>
  </div>
</template>

<script>
import { ajax } from "../../common/ajax";
import { storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import {
  XDialog,
  XButton,
  Group,
  XSwitch,
  TransferDomDirective as TransferDom
} from "vux";
export default {
  name: "checkInList",
  directives: {
    TransferDom
  },
  components: {
    XDialog
  },
  props: {},
  data() {
    return {
      title: "header",
      IsBack: false,
      showHideOnBlur: false,
      paymentMoney: 0,
      //切换账号的列表
      userList: [],
      UserHisOrder: [],
      indexCard: "",
      account: {
        accountCode: "",
        name: "",
        patcardNo: ""
      },
      //卡信息
      patcard: {},
      // 授权显示
      Unauthorized: true,
      switchS: true
    };
  },

  created() {
    this.look();
    this.GetUserHisOrder();
  },
  mounted() {
    this.testgetAccountInfo();
  },

  methods: {
    //获取用户信息
    testgetAccountInfo() {
      this.account = JSON.parse(storage.session.get("user")) || {};
      this.patcard = JSON.parse(storage.session.get("patcard")) || {};
      // console.log(this.patcard)
      this.indexCard = this.patcard.patCode;
      var data = {
        type: "1",
        id_card: this.account.idCard,
        openid: this.account.accountCode
      };
      // console.log(data);
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.userList = r.returnData;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    GetUserHisOrder() {
      var data = {
        patCode: this.patcard.patCode
      };
      ajax
        .post(apiUrls.GetUserHisOrder, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.UserHisOrder = r.returnData;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    userReported() {
      var data = {
        patCode: this.patcard.patCode
      };
      ajax
        .post(apiUrls.userReported, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.ret_code == "-1") {
            toolsUtils.alert(r.returnData.ret_note);
          } else {
            toolsUtils.alert("报到成功");
            WeixinJSBridge.call("closeWindow");
            return;
          }
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    //显示弹窗
    showPopup() {
      var userInfo = JSON.parse(storage.session.get("user"));
      storage.session.set("redirect", "/OutpatientPage");
      if (userInfo == null || userInfo == undefined) {
        this.$router.push({
          path: "/oauth",
          query: { type: "jump" }
        });
      } else {
        if (this.userList.length == 0) {
          toolsUtils.alert("你还没绑定诊疗卡");
          this.$router.push({
            path: "/userCenter"
          });
          return;
        }
        this.showHideOnBlur = true;
      }
    },
    // 页面加载读缓存
    look() {
      this.patcard = JSON.parse(storage.session.get("patcard"));
      if (this.patcard.patCode != null && this.patcard.patCode != undefined) {
        this.Unauthorized = false;
        this.switchS = false;
      }
    },
    setId(index) {
      return "radio_" + index;
    },
    //切换账号
    changeUser(patcardNo) {
      var data = {
        patcardNo,
        id_card: this.account.idCard
      };
      ajax
        .post(apiUrls.GetpatFamilyInfo, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          storage.session.set("patcard", JSON.stringify(r.returnData));
          this.$emit("changeParent", "");
          location.reload();
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.check-in-list {
  height: 100%;
  width: 100%;
  background: #f0eff6;
}

.user-info {
  background: #fff;
  padding: 0.17rem;
  margin-bottom: 0.17rem;
}

.head-wrap {
  background: #4ebab6;
  border-radius: 0.14rem;
  color: #fff;
  font-size: 0.28rem;
  padding: 0.31rem 0.24rem;
  display: flex;
  justify-content: space-between;
}

.patient-info {
  display: flex;
  flex-direction: column;
  gap: 0.19rem;
}

.patient-name-wrap {
  margin: auto;
  display: flex;
  align-items: center;
  gap: 0.24rem;
}

.change-patient {
  margin: auto;
  background: rgba(255, 255, 255, 0.9);
  color: #4ebab6;
  padding: 0.05rem 0.17rem;
  display: flex;
  align-items: center;
  gap: 0.02rem;
  border-radius: 0.2rem;
  font-size: 0.21rem;
}

.change-patient img {
  width: 0.28rem;
}

.code-img {
  height: 1.28rem;
  width: 1.28rem;
  border: 2px solid #fff;
  border-radius: 100%;
  background: #fff;
  overflow: hidden;
}

.code-img img {
  display: block;
  width: 100%;
}

.list-item {
  background: #fff;
  font-size: 0.28rem;
}

.order-num {
  color: #333333;
  padding: 0.17rem;
  border-bottom: 1px solid #b2bec3;
}

.item-wrap {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.17rem;
  padding: 0.17rem;
  border-bottom: 1px solid #b2bec3;
}

.item-title {
  color: #80808d;
}

.item-text {
  color: #333333;
  text-align: right;
}

.btn-wrap {
  display: flex;
  justify-content: flex-end;
  padding: 0.17rem;
}

.check-in-btn {
  width: 1.4rem;
  color: #fff;
  background: #4ebab6;
  border-radius: 0.07rem;
  padding: 0.09rem 0.28rem;
}

/* 弹窗内容的样式 */
.popup-wrap {
  width: 100%;
  background: #fff;
  box-shadow: 1px 1px 4px 0 rgba(53, 64, 82, 0.7);
  border-radius: 2px;
}

.popup-wrap h3 {
  height: 1.12rem;
  width: 100%;
  font-size: 0.36rem;
  color: #354052;
  text-align: center;
  line-height: 1.12rem;
  font-weight: normal;
  border-bottom: 1px solid #dfe3e9;
}

.popup-content {
  height: 4.48rem;
  width: 100%;
  overflow: auto;
}

.patient-list {
  position: relative;
}

.patient-list:after {
  display: block;
  content: "";
  clear: both;
}

.left-solid {
  width: 1.74rem;
  height: 100%;
  padding: 0.6rem 0;
  position: absolute;
  top: 0;
}

.left-solid div {
  margin: 0 auto;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #24bab8;
}

.patient-list ul {
  display: block;
  padding-left: 1.74rem;
}

.patient-list ul li {
  height: 1.12rem;
  width: 100%;
  line-height: 1.12rem;
  border-bottom: 1px solid #dfe3e9;
}

.patient-list ul li label {
  display: block;
  height: 100%;
  width: 100%;
  font-size: 0.32rem;
  color: #7f8fa4;
  padding-left: 1rem;
  position: relative;
  text-align: left;
}

.patient-list ul li input {
  display: none;
}

.patient-list ul li label:after {
  display: block;
  content: "";
  height: 10px;
  width: 10px;
  border-radius: 100%;
  background: #24bab8;
  position: absolute;
  top: 50%;
  margin-top: -5px;
  left: -0.96rem;
  z-index: 2;
}

.patient-list ul li input:checked ~ label:after {
  transform: scale(2);
  transition: transform 300ms ease-out;
}
</style>
