<template>
  <!--清单详情页-->
  <div class="about-time">
    <!-- 弹框提醒 -->
    <x-dialog
      v-model="showDialogStyle"
      hide-on-blur
      :dialog-style="{
        'max-width': '100%',
        width: '100%',
        height: '90.6%',
        'background-color': 'transparent'
      }"
    >
      <div class="max-box">
        <!-- <div class="show-top">
          <img src="../../assets/taix.png">
          <span>温馨提示</span>
        </div>
        <div class="box-text" v-html="111"></div> -->
        <div class="show-top">
          <img src="../../assets/text.png" />
          <span>温馨提示</span>
        </div>
        <div
          class="box-text2"
          style="white-space: pre-wrap;"
          v-html="note"
        ></div>
      </div>
      <img
        src="../../assets/round_close.png"
        class="closeBtn"
        @click="close()"
      />
    </x-dialog>

    <div class="list-group">
      <div class="jiuzhenren" @click="choiceInfo()">
        <p>就诊人</p>
        <span>
          {{ self }}
          <img src="../../assets/right.png" alt />
        </span>
      </div>
      <group class="cell-group">
        <cell title="订单类型">{{ dordata.paytype | type }}</cell>
        <cell title="医院">{{ dordata.hosName }}</cell>
        <cell title="科室">{{ dordata.deptName }}</cell>
        <cell title="就诊地址">{{ dordata.outAddr || "医院" }}</cell>
        <cell title="就诊时间"
          >{{ dordata.date }} {{ dordata.begintime }}-{{
            dordata.endtime
          }}</cell
        >
        <cell title="挂号费">
          <div>
            <span style="color: #FD7070;">￥{{ dordata.payamout }}</span>
          </div>
        </cell>
      </group>
    </div>
    <p>
      <img src="../../assets/warn.png" alt />请选择就诊人和确认预约信息是否正确
    </p>
    <button @click="submitFun()">挂号提交</button>
  </div>
</template>

<script>
import { Cell, Group, XDialog } from "vux";
import { ajax, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";

export default {
  name: "index",
  components: {
    Cell,
    Group,
    XDialog
  },
  data() {
    return {
      note:
        "1.本系统预约挂号不需先支付诊金费，将和处方订单一并结算；2.预约挂号成功后请携带手机挂号订单记录到相对应的分诊台进行报到-取号-排队-就诊；3.您可以挂当天和预约医生号源，当天挂号线上无法取消；4.预约系统当天上午7：30开放号源；5.当月取消订单次数多余3次时，将不可在线预约；6.请携带手机支付处方记录前往相对应检验、治疗、取药等;7.口腔科预约患者提早20分钟来院签到；其他科室提早10分钟来院签到；8.有发热及呼吸道症状的患者请到发热门诊就诊，不可预约；",
      title: "选择科室",
      dordata: {
        address: "",
        deptName: "",
        hosName: "",
        payamout: "",
        drname: "",
        Regtime: "",
        paytype: "",
        outAddr: "",
        lockId: ""
      },
      showDialogStyle: false,
      self: ""
    };
  },
  filters: {
    type: function(value) {
      if (value == 1) {
        return "挂号预约";
      }
      if (value == 0) {
        return "当天挂号";
      }
      return "无法获取";
    }
  },
  mounted() {
    this.onload();
    if (this.$route.query.note == "no") {
      this.showDialogStyle = false;
    } else {
      this.showDialogStyle = true;
    }
    this.note = this.change(this.note);
  },
  methods: {
    close() {
      this.showDialogStyle = false;
    },

    change: function(note) {
      note = note.replace(/;/g, ";</br>");
      note = note.replace(/；/g, "；</br>");
      return note;
    },
    onload() {
      this.dordata = JSON.parse(storage.session.get("drInfo"));
      console.log(this.dordata);
      this.self = JSON.parse(storage.session.get("patcard")).patName;
      this.dordata.patCardNo = JSON.parse(
        storage.session.get("patcard")
      ).patCode;
      this.dordata.name = this.self;
      storage.session.set("drInfo", JSON.stringify(this.dordata));
    },
    submitFun() {
      var that = this;
      var data = JSON.parse(storage.session.get("drInfo"));
      var datas = JSON.parse(storage.session.get("patcard"));
      if (
        data.patCardNo == null ||
        data.patCardNo == undefined ||
        data.patCardNo == ""
      ) {
        toolsUtils.alert("请绑定就诊卡且选择就诊卡");
        return;
      }
      ajax.post(apiUrls.UserIsNotTure, datas).then(r => {
        console.log(r);
        r = r.data;
        if (!r.success) {
          // toolsUtils.alert(r.returnMsg);
          alert(r.returnMsg);
          storage.session.delete("drInfo");
          storage.session.delete("patcard");
          this.$router.go(-2);
          return;
        }
        if (data.deptId == "1110") {
          setTimeout(function() {
            that.$router.replace({
              path: "/appointment"
            });
          }, 1000);
          toolsUtils.alert("核酸检测前，请先填写相关个人资料。");
          return;
        }
        ajax
          .post(apiUrls.AddPreRegOrders, data)
          .then(r => {
            console.log(r.data.returnData);
            r = r.data;
            storage.session.set("orderid", r.returnData);
            if (r.success == false) {
              toolsUtils.alert(r.returnMsg);
              return;
            }
            this.dordata.payamout = r.returnMsg / 100;
            storage.session.set("drInfo", JSON.stringify(this.dordata));
            // console.log(r);
            this.$router.replace({
              path: "/nowPay"
            });
          })
          .catch(e => {
            toolsUtils.alert("程序异常:" + JSON.stringify(e));
          });
        // toolsUtils.alert(r.returnData);
      });
    },
    choiceInfo() {
      var info = JSON.parse(storage.session.get("user"));
      console.log(info);
      this.$router.replace({
        path: "/patientManage",
        query: {
          id_card: info.idCard,
          type: 1,
          method: "order"
        }
      });
    }
  },
  computed: {},
  created() {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.max-box {
  width: 83.2%;
  height: 86%;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 8px;
  margin: 4.2% 8% 0 8.8%;
  display: block;
}
.show-top {
  width: 100%;
  height: 7.5%;
  overflow: hidden;
  margin-bottom: 1.5%;
  margin-top: 2.4%;
}
.show-top img {
  float: left;
  width: 0.54rem;

  display: block;
  margin: 2.5% 2.4% 0 4.5%;
}
.show-top span {
  float: left;
  margin: 2.5% 0 1.5% 0;
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #373c40;
  letter-spacing: 0;
  text-align: center;
  display: inline-block;
}
.box-text {
  width: 91.5%;
  height: 14%;
  border: 1px solid #fdbda1;
  margin-left: 4.5%;
  border-radius: 4px;
  background: #fff5f2;
}
.box-text2 {
  text-align: left;
  width: 91.5%;
  height: 80%;
  border: 1px solid #a1cbfd;
  background: #f2f8ff;
  border-radius: 4px;
  margin-left: 4.3%;
}
.closeBtn {
  width: 10.7%;

  margin: 4.6% 44.3% 3.9% 45.1%;
}

.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
}
.jiuzhenren {
  height: 1.08rem;
  background: #fdfbfb;
  border-bottom: none;
  display: flex;
  align-items: center;
  padding: 0 0.3rem;
}
.jiuzhenren p {
  color: #354052;
}
.jiuzhenren span {
  flex-basis: 0;
  flex-grow: 1;
  text-align: right;
  color: #7f8fa4;
}
.jiuzhenren span img {
  vertical-align: middle;
  height: 0.4rem;
  margin-left: 0.08rem;
}
.weui-cell:nth-child(1)::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 16px;
  color: #b5b5b5;
  margin: 9px 0 23px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.about-time button {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 1.08rem;
  width: 100%;
  font-size: 0.36rem;
  display: block;
  color: #fff;
}
</style>
