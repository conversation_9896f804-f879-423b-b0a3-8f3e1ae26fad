<template>
  <!--清单详情页-->
  <div class="about-time">
    <dingdanxinxi @paytype="getPaytype"></dingdanxinxi>
    <div class="list-group">
      <group class="cell-group">
        <cell title="支付方式" value="微信支付" v-if="payType == 'WECHAT'">
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            src="../../assets/weixinzhifu.png"
          />
        </cell>
        <cell title="支付方式" value="医保支付" v-else-if="payType == 'YBPAY'">
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            src="../../assets/weixinzhifu.png"
          />
        </cell>
        <cell title="支付方式" value="未选择" v-else>
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            src="../../assets/weixinzhifu.png"
          />
        </cell>
      </group>
    </div>

    <!-- <p><img src="../../assets/warn.png" alt="">温馨提示：请在30分钟内完成支付，逾期号将会作废，需重新挂号</p> -->
    <!--status==8 是挂号没有支付的版本-->
    <div v-if="status == 2 || (status == 8 && isShow)" class="bottom-btn">
      <button @click="cancelFun()">取消挂号</button>
    </div>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
import { ajax, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import dingdanxinxi from "../../components/outpatient/dingdanxinxi";
export default {
  name: "index",
  components: {
    Cell,
    Group,
    dingdanxinxi
  },
  data() {
    return {
      title: "选择科室",
      pattype: "",
      pactTime: "",
      patCode: "",
      status: 0,
      isShow: true,
      payType: ""
    };
  },
  created() {
    var odata = {
      orderid: this.$route.query.orderid
    };
    //console.log(odata);
    ajax
      .post(apiUrls.getSuccessPayType, odata)
      .then(r => {
        this.payType = r.data[0].toUpperCase();
        //console.log(this.payType);
        // switch(r.data[0].toUpperCase()){
        //   case "WECHAT":
        //     this.payType = "微信支付"
        //     break;
        //   case "wechat":
        //     this.payType = "医保支付"
        //     break;
        //   default:
        //     this.payType = ""
        //     break;
        // }
      })
      .catch(r => {
        toolsUtils.alert("程序异常:" + JSON.stringify(e));
      });
  },
  mounted() {
    this.initCancel();
  },
  methods: {
    getPaytype(val) {
      //debugger
      console.log(val.val);
      this.pattype = val.val;
      this.pactTime = val.Regtime;
      this.patCode = val.patcode;
      this.status = val.status;
    },
    cancelFun() {
      var ptime = this.pactTime;
      var dateday = this.getptinfo();
      var dd = new Date(ptime);
      dd.setDate(dd.getDate() - 1);
      var y = dd.getFullYear();
      var m =
        dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
      var d =
        dd.getDate() + 1 < 10 ? "0" + (dd.getDate() + 1) : dd.getDate() + 1;
      var timeout = y + "-" + m + "-" + d + " " + "21:00:00";
      if (dateday > timeout) {
        this.isShow = false;
        toolsUtils.alert("已超过规定取消预约时间");
        return;
      }
      if (this.pattype == "0") {
        //当天预约不允许退款
        this.isShow = false;
        toolsUtils.alert("无法取消当天预约");
        return;
      }
      var data = {
        orderid: this.$route.query.orderid,
        patCardNo: this.patCode
      };
      ajax
        .post(apiUrls.RefundRegOrders, data)
        .then(r => {
          var r = r.data;
          console.log(r);
          if (r.success != true) {
            this.isShow = false;
            toolsUtils.alert(JSON.stringify(r.returnMsg));
            return;
          }
          // toolsUtils.alert('退费成功！');
          toolsUtils.alert("取消成功！");
          setTimeout(function() {
            WeixinJSBridge.call("closeWindow");
          }, 1000);
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    initCancel() {
      var ptime = this.pactTime;
      var dateday = this.getptinfo();
      var dd = new Date(ptime);
      dd.setDate(dd.getDate() - 1);
      var y = dd.getFullYear();
      var m =
        dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
      var d =
        dd.getDate() + 1 < 10 ? "0" + (dd.getDate() + 1) : dd.getDate() + 1;
      var timeout = y + "-" + m + "-" + d + " " + "21:00:00";
      if (dateday > timeout) {
        this.isShow = false;
        return;
      }
      if (this.pattype == "0") {
        //当天预约不允许退款
        this.isShow = false;
        return;
      }
    },
    getptinfo() {
      var day = new Date();
      var month =
        day.getMonth() + 1 < 10
          ? "0" + (day.getMonth() + 1)
          : day.getMonth() + 1;
      var datem = day.getDate() < 10 ? "0" + day.getDate() : day.getDate();
      var daytime =
        day.getFullYear() +
        "-" +
        month +
        "-" +
        datem +
        " " +
        day.getHours() +
        ":" +
        day.getMinutes() +
        ":" +
        day.getSeconds();
      return daytime;
    }
  },

  computed: {}
};
</script>

<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 23px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #fff;
  border: 1px solid #24bab8;
}
.bottom-btn button:first-child {
  background: #fff;
  /* color: #24BAB8; */
  margin-right: 0.1rem;
}
.bottom-btn button:last-child {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  margin-left: 0.1rem;
}
.pay-methodImg {
  height: 0.48rem;
}
</style>
