<template>
  <div class="dingdanxinxi">
    <div class="list-group">
      <group class="cell-group">
        <cell title="订单号">{{ orderid }}</cell>
        <cell title="订单状态">
          <div>
            <span style="color: #24BAB8;">{{ dordata.status | status }}</span>
          </div>
        </cell>
      </group>
    </div>

    <div class="list-group">
      <group class="cell-group">
        <cell title="就诊人">{{ self }}</cell>
        <cell title="诊疗卡号">{{ dordata.patCardNo }}</cell>
        <svg
          class="barcode"
          :jsbarcode-value="dordata.patCardNo"
          jsbarcode-width="1"
        ></svg>
        <cell title="订单类型">{{ type | capitalize }}</cell>
        <cell title="医院" value="东莞市桥头医院"></cell>
        <cell title="科室">{{ dordata.deptName }}</cell>
        <cell title="医生">{{ dordata.drname }}</cell>
        <cell title="就诊地址">{{ dordata.outAddr || "医院" }}</cell>
        <cell title="就诊时间">{{ Regtime }}</cell>
        <cell v-if="type == 1" title="预约号">{{ dordata.lockId }}</cell>
        <cell title="结算号">{{ dordata.hisPayOrderNo }}</cell>
        <!-- <cell title="挂号费">
                <div>
                   <span style="color: #FD7070;">￥{{dordata.payamout}}</span>
                </div>
             </cell> -->
      </group>
    </div>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
import JsBarcode from "jsbarcode";
import { toolsUtils, storage, ajax, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import { setTimeout } from "timers";
export default {
  name: "index",
  components: {
    Cell,
    Group
  },

  data() {
    return {
      title: "header",
      dordata: {
        address: "",
        deptName: "",
        patCardNo: "",
        hosName: "",
        payamout: "",
        drname: "",
        paytype: "",
        outAddr: "",
        lockId: "",
        hisPayOrderNo: "",
        status: "",
        hisMsg: ""
      },
      orderid: "",
      self: "",
      type: "",
      Regtime: "",
      jzhen: ""
    };
  },
  filters: {
    capitalize: function(value) {
      if (value == 1) {
        return "预约挂号";
      }
      if (value == 0) {
        return "当天挂号";
      }
    },
    status: function(value) {
      if (value == 0) {
        return "订单超时(已取消)";
      }
      if (value == 1) {
        // return "挂号中(待支付)";
        return "挂号中";
      }
      if (value == 2) {
        // return "预约成功(已支付)";
        return "预约成功";
      }
      if (value == 3) {
        // return "订单取消(已退款)";
        return "订单取消";
      }
      if (value == 4) {
        return "订单取消";
      }
      if (value == 5) {
        return "医生停诊(已退款)";
      }
      if (value == 6) {
        return "异常订单";
      }
      if (value == 8) {
        return "预约成功";
      }
    }
  },
  mounted: function() {
    setTimeout(function() {
      JsBarcode(".barcode").init();
    }, 1000);
  },
  created: function() {
    // if (JSON.parse(storage.session.get("drInfo")) == null) {
    if (this.$route.query.orderid != null) {
      //toolsUtils.alert('查看订单');
      var data = {
        orderid: this.$route.query.orderid
      };
      ajax
        .post(apiUrls.GetpatientOrderInfo, data)
        .then(r => {
          data = r.data;
          console.log(data);
          if (data.returnData == null) {
            toolsUtils.alert(data.returnMsg);
          }
          console.log(data.returnData);

          this.dordata = data.returnData;
          this.lockId = data.returnData.lockId;
          this.hisPayOrderNo = data.returnData.hisPayOrderNo;
          this.dordata.drname = data.returnData.doctorName;
          this.dordata.outAddr = data.returnData.outAddr;
          this.Regtime =
            data.returnData.regDate.split("T")[0] +
            "  " +
            data.returnData.regTime;
          this.self = this.dordata.patName;
          this.dordata.payamout = data.returnData.treatFee / 100;
          this.orderid = this.$route.query.orderid;
          this.type = data.returnData.paytype;
          this.jzhen = data.returnData.regDate.split("T")[0];
          this.$emit("orderid", { orderid: this.orderid });
          this.$emit("money", { val: this.dordata.payamout });
          this.$emit("timeoutdate", { val: data.returnData.timeoutTime });
          this.$emit("paytype", {
            val: this.type,
            Regtime: this.jzhen,
            patcode: this.dordata.patCardNo,
            status: this.dordata.status,
            deptName: this.dordata.deptName,
            time: this.dordata.regTime
          });
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
      return;
    }

    //TODO:
    //toolsUtils.alert('预约订单');
    var info = JSON.parse(storage.session.get("drInfo"));
    this.dordata = JSON.parse(storage.session.get("drInfo"));
    this.dordata.status = 1;
    // this.self = JSON.parse(storage.session.get("user")).name;
    this.self = JSON.parse(storage.session.get("patcard")).patName;
    this.type = JSON.parse(storage.session.get("timeinfo")).type;
    this.Regtime = info.date + " " + info.begintime;
    this.orderid = storage.session.get("orderid");
    // this.$emit("timeoutdate",{val:outtime});
    var that = this;
    setTimeout(function() {
      that.$emit("orderid", { orderid: that.orderid });
      that.$emit("money", { val: that.dordata.payamout });
    }, 500);
  },

  methods: {},
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dingdanxinxi {
  width: 100%;
}
.barcode {
  width: 95%;
}
.dingdanxinxi .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
</style>
