<template>
  <div class="appointment">
    <h3 class="title">信息填写</h3>
    <van-field
      v-model="name"
      label="姓名"
      required
      placeholder="请输入姓名"
      input-align="right"
    />
    <van-field
      v-model="age"
      label="年龄"
      placeholder="请输入年龄"
      type="digit"
      input-align="right"
    />
    <van-field
      v-model="phone"
      label="手机号码"
      required
      placeholder="请输入手机号码"
      input-align="right"
    />

    <van-field
      readonly
      clickable
      label="性别"
      :value="sex"
      placeholder="请选择性别"
      @click="showSex = true"
      input-align="right"
      required
    />
    <van-popup v-model="showSex" round position="bottom">
      <van-picker
        title="请选择"
        show-toolbar
        :columns="sexData"
        @cancel="showSex = false"
        @confirm="sexConfirm"
      />
    </van-popup>

    <van-field
      label="国家/地区"
      value="中国"
      readonly
      input-align="right"
      required
    />
    <van-field
      readonly
      clickable
      label="户籍地"
      :value="area"
      placeholder="请选择户籍地"
      @click="showArea = true"
      input-align="right"
    />
    <van-popup v-model="showArea" round position="bottom">
      <van-picker
        title="请选择"
        show-toolbar
        :columns="areaData"
        @cancel="showArea = false"
        @confirm="areaConfirm"
        input-align="right"
      />
    </van-popup>

    <van-field
      v-model="residence"
      label="居住地"
      placeholder="请输入居住地"
      input-align="right"
    />

    <van-field
      readonly
      clickable
      label="证件类型"
      :value="IDType"
      placeholder="请选择证件类型"
      @click="showIDType = true"
      input-align="right"
      required
    />
    <van-popup v-model="showIDType" round position="bottom">
      <van-picker
        title="请选择"
        show-toolbar
        :columns="IDTypeData"
        @cancel="showIDType = false"
        @confirm="IDTypeConfirm"
      />
    </van-popup>

    <van-field
      v-model="IDNumber"
      label="证件号码"
      placeholder="请输入证件号码"
      @blur="getAgeandSex(IDNumber)"
      input-align="right"
      required
    />

    <van-field
      readonly
      clickable
      label="人员来源"
      :value="sources"
      placeholder="请选择人员来源"
      @click="showSources = true"
      input-align="right"
      required
    />
    <van-popup v-model="showSources" round position="bottom">
      <van-picker
        title="请选择"
        show-toolbar
        :columns="sourcesData"
        @cancel="showSources = false"
        @confirm="sourcesConfirm"
      />
    </van-popup>

    <van-field
      class="identity"
      readonly
      clickable
      label="人员身份"
      :value="identity"
      placeholder="请选择人员身份"
      @click="showIdentity = true"
      input-align="right"
      required
    />
    <van-popup v-model="showIdentity" round position="bottom">
      <van-picker
        title="请选择"
        show-toolbar
        :columns="identityData"
        @cancel="showIdentity = false"
        @confirm="identityConfirm"
      />
    </van-popup>

    <van-field
      class="entry"
      readonly
      clickable
      label="14天内境外入境人员"
      :value="entry"
      placeholder="请选择是否14天内境外入境人员"
      @click="showEntry = true"
      input-align="right"
      required
      center
    />
    <van-popup v-model="showEntry" round position="bottom">
      <van-picker
        title="请选择"
        show-toolbar
        :columns="entryData"
        @cancel="showEntry = false"
        @confirm="entryConfirm"
      />
    </van-popup>
    <div class="btn" @click="verification">提交</div>
  </div>
</template>

<script>
import { ajax, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";

export default {
  data() {
    return {
      name: "", //姓名
      age: "", //年龄
      phone: "", //手机号码
      sex: "", //性别
      sexData: ["男", "女"], //性别选项
      // emun_sex:{
      //   1:'男',
      //   2:'女'
      // },
      area: "", //户籍地
      //户籍地选项
      areaData: [
        "北京",
        "天津",
        "河北",
        "山西",
        "内蒙古",
        "辽宁",
        "吉林",
        "黑龙江",
        "上海",
        "江苏",
        "浙江",
        "安徽",
        "福建",
        "江西",
        "山东",
        "河南",
        "湖北",
        "湖南",
        "广东",
        "广西",
        "海南",
        "重庆",
        "四川",
        "贵州",
        "云南",
        "西藏",
        "陕西",
        "甘肃",
        "青海",
        "宁夏",
        "新疆",
        "台湾",
        "香港",
        "澳门"
      ],
      aCity: {
        11: "北京",
        12: "天津",
        13: "河北",
        14: "山西",
        15: "内蒙古",
        21: "辽宁",
        22: "吉林",
        23: "黑龙江",
        31: "上海",
        32: "江苏",
        33: "浙江",
        34: "安徽",
        35: "福建",
        36: "江西",
        37: "山东",
        41: "河南",
        42: "湖北",
        43: "湖南",
        44: "广东",
        45: "广西",
        46: "海南",
        50: "重庆",
        51: "四川",
        52: "贵州",
        53: "云南",
        54: "西藏",
        61: "陕西",
        62: "甘肃",
        63: "青海",
        64: "宁夏",
        65: "新疆",
        71: "台湾",
        81: "香港",
        82: "澳门"
      },
      residence: "东莞", //居住地
      IDType: "居民身份证", //证件类型
      //证件类型选项
      IDTypeData: [
        "居民身份证",
        "港澳居民来往内地通行证",
        "台湾居民来往内地通行证"
      ],
      IDNumber: "", //证件号码
      sources: "其它采样点", //人员来源
      //人员来源选项
      sourcesData: [
        "集中留观",
        "密切接触者",
        "交通检疫",
        "社区排查",
        "发热门诊",
        "其他门（急）诊",
        "住院患者",
        "住院患者陪护人员",
        "农贸市场专项排查",
        "其它采样点"
      ],
      identity: "其它人群", //人员身份
      //人员身份选项
      identityData: [
        "其它人群",
        "出入境通关人员",
        "国内出行人员",
        "上岗就业人员",
        "返校复课师生",
        "医疗机构工作人员",
        "口岸检疫和边防检查人员",
        "监所工作人员",
        "社会福利养老机构工作人员",
        "孕产妇",
        "新生儿",
        "母婴服务类机构人员",
        "农贸市场海水产品从业人员",
        "农贸市场禽畜肉类从业人员",
        "农贸市场进口食品类从业人员",
        "农贸市场其他人员",
        "农贸市场专项排查其他从业人员",
        "屠宰场/各类冷库从业人员",
        "农贸市场中地下或通风不良场所从业人员"
      ],
      entry: "否", //14天内境外入境人员
      //14天内境外入境人员选项
      entryData: ["否", "是"],
      showSex: false,
      showArea: false,
      showIDType: false,
      showSources: false,
      showIdentity: false,
      showEntry: false,
      patcard: JSON.parse(storage.session.get("patcard"))
    };
  },
  methods: {
    sexConfirm(value) {
      this.sex = value;
      this.showSex = false;
    },
    areaConfirm(value) {
      this.area = value;
      this.showArea = false;
    },
    IDTypeConfirm(value) {
      this.IDType = value;
      this.showIDType = false;
    },
    sourcesConfirm(value) {
      this.sources = value;
      this.showSources = false;
    },
    identityConfirm(value) {
      this.identity = value;
      this.showIdentity = false;
    },
    entryConfirm(value) {
      this.entry = value;
      this.showEntry = false;
    },
    getAgeandSex(UUserCard) {
      if (
        !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
          UUserCard
        ) &&
        !/^[0-9]{8}$/.test(UUserCard)
      ) {
        return;
      }
      let areaCode = UUserCard.substring(0, 2);
      this.area = this.aCity[areaCode];
      //获取性别
      if (parseInt(UUserCard.substr(16, 1)) % 2 == 1) {
        //男
        this.sex = "男";
      } else {
        //女
        this.sex = "女";
      }
      //获取年龄
      var myDate = new Date();
      var month = myDate.getMonth() + 1;
      var day = myDate.getDate();
      var age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1;
      if (
        UUserCard.substring(10, 12) < month ||
        (UUserCard.substring(10, 12) == month &&
          UUserCard.substring(12, 14) <= day)
      ) {
        age++;
      }
      this.age = age;
    },
    // 校验
    verification() {
      var data = JSON.parse(storage.session.get("drInfo"));
      var user = JSON.parse(storage.session.get("user"));
      var orderid = "";
      if (
        data.patCardNo == null ||
        data.patCardNo == undefined ||
        data.patCardNo == ""
      ) {
        toolsUtils.alert("请绑定就诊卡且选择就诊卡");
        return;
      }
      var that = this;
      if (!this.name) {
        this.$toast("请输入姓名");
        return false;
      }
      if (!/^1[0-9]{10}$/.test(this.phone)) {
        this.$toast("手机号格式错误");
        return false;
      }
      if (!this.sex) {
        this.$toast("请选择性别");
        return false;
      }
      if (!this.IDType) {
        this.$toast("请选择证件类型");
        return false;
      }
      if (!this.IDNumber) {
        this.$toast("请输入证件号码");
        return false;
      } else {
        if (
          !/^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
            this.IDNumber
          ) &&
          !/^[0-9]{8}$/.test(this.IDNumber)
        ) {
          this.$toast("证件号码格式错误");
          return false;
        }
        if (this.IDType == "居民身份证" && this.IDNumber.length != 18) {
          this.$toast("身份证长度错误");
          return false;
        }
      }
      if (!this.sources) {
        this.$toast("请选择人员来源");
        return false;
      }
      if (!this.identity) {
        this.$toast("请选择人员身份");
        return false;
      }
      if (!this.entry) {
        this.$toast("请选择是否14天内境外入境人员");
        return false;
      }
      var dataver = {
        patMedNo: this.patcard.patCode,
        patMobile: this.phone,
        patIdCardType: this.IDType,
        patIdCard: this.IDNumber,
        source: this.sources,
        status: this.identity,
        isAbroad: this.entry
      };

      //1.生成微信订单
      ajax
        .post(apiUrls.AddPreRegOrders, data)
        .then(r => {
          console.log(r.data.returnData);
          r = r.data;
          storage.session.set("orderid", r.returnData);
          orderid = r.returnData;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          var payamout = r.returnMsg / 100;
          storage.session.set("drInfo", JSON.stringify(payamout));
          // console.log(r);

          //2.完成支付订单
          var datanopay = {
            openid: user.accountCode,
            orderid: orderid
          };
          if (
            datanopay["orderid"] == "" ||
            datanopay["orderid"] == null ||
            datanopay["orderid"] == undefined
          ) {
            alert("网络异常，请重新生成订单！");
            return;
          }
          ajax
            .post(apiUrls.noWxPayRegOrders, datanopay)
            .then(r => {
              r = r.data;
              console.log(r.returnData);
              if (!r.success) {
                toolsUtils.alert(r.returnMsg);
                return;
              }

              //3.生成his处方订单
              ajax
                .post(apiUrls.addHSRegPreOrders, dataver)
                .then(r => {
                  console.log(r.data.returnData);
                  r = r.data;
                  if (r.success == false) {
                    toolsUtils.alert(r.returnMsg);
                    return;
                  }
                  storage.session.delete("drInfo");
                  this.$router.replace({
                    path: "/cfList"
                  });
                  return;
                })
                .catch(e => {
                  toolsUtils.alert("录入处方程序异常" + JSON.stringify(e));
                });

              return;
            })
            .catch(e => {
              toolsUtils.alert("开单异常" + e.toString());
            });
        })
        .catch(e => {
          toolsUtils.alert("提交预约程序异常" + JSON.stringify(e));
        });
    }
  },
  created() {
    this.getAgeandSex(this.patcard.patIdCard);
    this.name = this.patcard.patName;
    this.phone = this.patcard.patMobile;
    this.IDNumber = this.patcard.patIdCard;
  }
};
</script>

<style lang="less" scoped>
.appointment {
  font-size: 0.4rem;
  .title {
    text-align: center;
    margin: 0.2rem 0;
  }
  .entry /deep/.van-field__label {
    width: 1.6rem;
  }
  .van-cell {
    padding-right: 10px;
    font-size: 0.32rem;
  }
  .identity /deep/.van-field__label {
    width: 1.6rem;
  }
  /deep/.van-field__error-message {
    text-align: right;
  }
  .btn {
    margin: 0.4rem 0.4rem;
    font-size: 0.32rem;
    background: #24bab8;
    color: #fff;
    height: 0.75rem;
    text-align: center;
    line-height: 0.75rem;
    border-radius: 0.1rem;
  }
}
</style>
