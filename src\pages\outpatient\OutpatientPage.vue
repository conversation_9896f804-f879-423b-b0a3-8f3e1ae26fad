<template>
  <!--门诊主页-->
  <div class="outpatient-page">
    <Vheader></Vheader>
    <!-- 选项内容 -->
    <div v-for="(item, index) in contentList" :key="index" class="content_list">
      <div class="contentDiv">
        <router-link :to="item.toGo1" class="contentDivs">
          <img :src="item.img1" alt />
          <span :class="'contentDiv' + index + '1'">{{ item.textGo1 }}</span>
        </router-link>
      </div>
      <div class="contentDiv">
        <router-link :to="item.toGo2" class="contentDivs">
          <img :src="item.img2" alt />
          <span :class="'contentDiv' + index + '2'">{{ item.textGo2 }}</span>
        </router-link>
      </div>
      <div class="contentDiv">
        <router-link :to="item.toGo3" class="contentDivs">
          <img :src="item.img3" alt />
          <span :class="'contentDiv' + index + '3'">{{ item.textGo3 }}</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import Vheader from "@/components/outpatient/header";
export default {
  components: {
    Vheader
  },
  data() {
    return {
      contentList: [
        {
          toGo1: "/choiceMethod",
          img1: require("../../assets/icon/yuyueguahao.png"),
          textGo1: "预约挂号",
          //  当天挂号需要传参method为0
          toGo2: "../../choiceDoctor?method=0&deptId=1110",
          img2: require("../../assets/icon/dangtianguahao.png"),
          textGo2: "核酸预约",
          toGo3: "/registerRecord",
          img3: require("../../assets/icon/guahaodingdan.png"),
          textGo3: "挂号订单"
        },
        {
          toGo1: "/cfregisterRecord",
          img1: require("../../assets/icon/chufangdingdan.png"),
          textGo1: "处方订单",
          toGo2: "/cfLists",
          img2: require("../../assets/icon/chufangjiaofei.png"),
          textGo2: "处方缴费",
          toGo3: "/seeReport",
          img3: require("../../assets/icon/baogaochaxun.png"),
          textGo3: "报告查询"
        }
      ],
      title: "门诊主页",
      IsBack: false
    };
  },
  methods: {},
  computed: {}
};
</script>
<style scoped>
.outpatient-page {
  height: 100%;
  width: 100%;
  background: #f0eff6;
  overflow: auto;
}
.content_list {
  width: 88%;
  height: 2.76rem;
  margin: 0 auto;
  margin-top: 0.48rem;
  display: flex;
  justify-content: space-between;
}
.contentDiv {
  width: 2.04rem;
  height: 2.76rem;
}
.contentDivs {
  width: 2.04rem;
  height: 2.76rem;
  background: white;
  border-radius: 5px;
  font-size: 0.28rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  letter-spacing: -0.01px;
  -moz-box-shadow: 1px 1px 4px #999;
  -webkit-box-shadow: 1px 1px 4px #999;
  box-shadow: 1px 1px 4px #999;
}
.contentDiv01 {
  color: #2dc4c2;
}
.contentDiv02 {
  color: #4ab5f1;
}
.contentDiv03 {
  color: #af703d;
}
.contentDiv11 {
  color: #f35689;
}
.contentDiv12 {
  color: #f0a20a;
}
.contentDiv13 {
  color: #a28bda;
}
</style>
