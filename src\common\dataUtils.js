import { isNumber } from "util";
import { dateFormat } from 'vux'

//数据验证过滤

export const dataUtils = {

    //判断是否数字
    checkRate(nubmer) {
        var re = /^[0-9]+.?[0-9]*/;//判断字符串是否为数字//判断正整数/[1−9]+[0−9]∗]∗/
        if (!re.test(nubmer)) {
            return false;
        }
        return true;
    },

    //根据身份证获取出生日期
    GetBirthday(IdCard){
        var birthdayno,birthdaytemp
        if(IdCard.length==18){
            birthdayno=IdCard.substring(6,14)
        }else if(IdCard.length==15){
            birthdaytemp=IdCard.substring(6,12)
            birthdayno="19"+birthdaytemp
        }else{
            return "";
        }
        var birthday=birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8)
        return birthday;
    },

    //根据身份证获取年龄
    GetAgeByIdCard(IdCard) {
        //获取年龄
        var myDate = new Date();
        var month = myDate.getMonth() + 1;
        var day = myDate.getDate();
        var age = myDate.getFullYear() - IdCard.substring(6, 10) - 1;
        if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) == month && IdCard.substring(12, 14) <= day) {
            age++;
        }
        return age;
    },

    //身份证判断
    isCardID(sId) {
        // debugger;
        if (sId.length == 9) {
            if (!/^1[45][0-9]{7}$|([P|p|S|s]\d{7}$)|([S|s|G|g|E|e]\d{8}$)|([Gg|Tt|Ss|Ll|Qq|Dd|Aa|Ff]\d{8}$)|([H|h|M|m]\d{8,10})$/.test(sId)) {
                return "护照号码格式错误";
            }
            else {
                return "OK";
            }
        } else {
            if (sId.length == 18) {
                var aCity = { 11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古", 21: "辽宁", 22: "吉林", 23: "黑龙江", 31: "上海", 32: "江苏", 33: "浙江", 34: "安徽", 35: "福建", 36: "江西", 37: "山东", 41: "河南", 42: "湖北", 43: "湖南", 44: "广东", 45: "广西", 46: "海南", 50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏", 61: "陕西", 62: "甘肃", 63: "青海", 64: "宁夏", 65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外" };
                var iSum = 0;
                var info = "";
                if (!/^\d{17}(\d|x)$/i.test(sId)) return "你输入的身份证长度或格式错误";
                sId = sId.replace(/x$/i, "a");
                if (aCity[parseInt(sId.substr(0, 2))] == null) return "你的身份证地区非法";
                var sBirthday = sId.substr(6, 4) + "-" + Number(sId.substr(10, 2)) + "-" + Number(sId.substr(12, 2));
                var d = new Date(sBirthday.replace(/-/g, "/"));
                if (sBirthday != (d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate())) return "身份证上的出生日期非法";
                for (var i = 17; i >= 0; i--) iSum += (Math.pow(2, i) % 11) * parseInt(sId.charAt(17 - i), 11);
                if (iSum % 11 != 1) return "你输入的身份证号非法";
            }
            else {
                return "你输入的身份证或护照格式有误";
            }
        }

        //aCity[parseInt(sId.substr(0,2))]+","+sBirthday+","+(sId.substr(16,1)%2?"男":"女");//此次还可以判断出输入的身份证号的人性别
        return true;
    },

     // 去除字符串空格
     Trim(str) {
        var reg = /[\t\r\f\n\s]*/g;
        if (typeof str === 'string') {
            return str.replace(reg,'');
        }
    },


    //姓名不允许，字符串，数字
    isName(value) {
        var regEn = /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im,
            regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
        if (regEn.test(value) || regCn.test(value)) {
            return "不能包含特殊字符!!!";
        }
        var reg = new RegExp(/[0-9]/g);
        if (reg.test(value)) {
            return "不能包含数字!!!";
        }
        return true;
    },

    //身份证不允许有空格，字符串，中文
    isCard(value) {
        var reg = /[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/g;
        if (reg.test(value)) {
            return "不能有中文!!!";
        }
        if (/\s/g.test(value)) {
            return "不能有空格!!!";
        }
        var regEn = /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im,
            regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
        if (regEn.test(value) || regCn.test(value)) {
            return "不能包含特殊字符!!!";
        }
        return true;
    },

    //手机判断
    isTel(tel) {
        let reg = /^1[0-9]{10}$/;
        if (!reg.test(tel)) {
            return "填写完整的11位手机号码!!!"
        }
        return true;
    },

    //名称判断
    isPatName(tel) {
        // let namereg = /^[\u4e00-\u9fa5]{2,4}$/;
        let namereg = /^[a-zA-Z\u4e00-\u9fa5]{2,20}$/;
        if (!namereg.test(tel)) {
            return "请输入正确的名字"
        }
        return true;
    },

    //截取带T的日期
    interceptDate(date) {
        return date.split("T")[0]
    },

    //分转元
    pointsTrunRMB(vlue, num) {
        try {
            return (vlue / 100).toFixed(num)
        } catch (error) {
            console.log('数字转换错误')
            return 0;
        }
    },
    changedatetype(val, type) {
        return dateFormat(val, type)
    },

    //生成订单号
    getorderId() {
        orderid = DateTime.Now.ToString("yyyyMMddHHmmssfff") + random.Next(10, 9999).ToString();
        return orderid;
    },
    dateToString(date) {
        var year = date.getFullYear();
        var month = (date.getMonth() + 1).toString();
        var day = (date.getDate()).toString();
        if (month.length == 1) {
            month = "0" + month;
        }
        if (day.length == 1) {
            day = "0" + day;
        }
        var dateTime = year + "-" + month + "-" + day;
        return dateTime;
    },

}
