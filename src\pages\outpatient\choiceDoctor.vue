<template>
  <!--清单详情页-->
  <div class="choice-doctor">
    <Date @getDrInfo="getDrInfo" @onload="getDrInfo"></Date>
    <div class="index-content">
      <section>
        <h3><span>选择医生</span></h3>
        <div class="dor-wrap">
          <div
            v-show="items.doctorInfo.visitTimeInfo != 0"
            class="every-dor"
            :key="index"
            v-for="(items, index) in dorData"
            @click="
              pushTimeInfo(
                items.doctorInfo.doctorId,
                items.doctorInfo.visitTimeInfo
              )
            "
          >
            <div class="dor-img">
              <img
                :src="
                  imghost +
                    items.doctorInfo.doctorId
                      .replace(' ', '')
                      .replace(' ', '')
                      .replace(' ', '') +
                    '.jpg'
                "
                :onerror="defaultImg"
                alt=""
              />
            </div>
            <div class="dor-info">
              <h4>
                {{ items.doctorInfo.doctorName }}
                <span
                  v-if="
                    items.doctorInfo.visitTimeInfo != -1 &&
                      items.doctorInfo.visitTimeInfo != -20
                  "
                  >剩余号源：{{ items.doctorInfo.visitTimeInfo
                  }}<img src="../../assets/more.png" alt=""
                /></span>
                <span
                  v-if="items.doctorInfo.visitTimeInfo == -1"
                  style="color:red"
                  >该医生已停诊<img src="../../assets/more.png" alt=""
                /></span>
                <span
                  v-if="items.doctorInfo.visitTimeInfo == -20"
                  style="color:red"
                  >该医生已挂满<img src="../../assets/more.png" alt=""
                /></span>
              </h4>
              <p>{{ items.doctorInfo.doctorTitle }}</p>
              <div>
                诊疗特长：<span>{{ items.doctorInfo.doctorRemark }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { ajax, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import baseData from "../../config/baseData";
import Date from "../../components/inHospital/date";
export default {
  components: {
    Date
  },
  data() {
    return {
      title: "选择科室",
      dorData: [],
      imghost: baseData.imgHost,
      defaultImg: 'this.src="' + require("../../assets/doctor.png") + '"'
    };
  },
  created() {
    // alert("粤康码为黄码者不需预约缴费，请走黄码通道办理");
  },
  mounted() {},
  methods: {
    //选中医生后的跳转
    checkLink() {
      this.$router.push({
        path: "/aboutTime",
        query: {
          method: this.$route.query.method
        }
      });
    },
    //获取医生基本信息
    getDrInfo(data) {
      var timeinfo = {
        date: data.date,
        deptid: data.deptid,
        type: this.$route.query.method
      };

      storage.session.set("timeinfo", JSON.stringify(timeinfo));
      ajax
        .post(apiUrls.GetDoctorInfo, data)
        .then(r => {
          var r = r.data.returnData;
          if (r == null) {
            toolsUtils.alert("暂无医生出诊信息！");
            return;
          }
          this.dorData = r;
          console.log(this.dorData);
        })
        .catch(e => {
          console.log(e);
          toolsUtils.alert("系统异常");
        });
    },
    //选中医生
    pushTimeInfo(Id, num) {
      if (num == "0") {
        //判断号源
        toolsUtils.alert("该医生当前暂无排班");
        return;
      }
      if (num == "-1") {
        //判断号源
        toolsUtils.alert("该医生已停诊");
        return;
      }
      if (num == "-20") {
        //判断号源
        toolsUtils.alert("该医生已挂满");
        return;
      }
      var timeinfo = JSON.parse(storage.session.get("timeinfo"));
      var data = {
        deptId: timeinfo.deptid,
        doctorId: Id,
        date: timeinfo.date
      };
      storage.session.set("drId", JSON.stringify(data));
      storage.session.set("drId", JSON.stringify(data));
      this.$router.push({
        path: "/aboutTime"
      });
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.choice-doctor {
  height: 100%;
  width: 100%;
  background: #fff;
  overflow: auto;
  padding: 0.24rem 0;
}
.index-content {
  padding: 0 0.4rem;
  margin-bottom: 0.24rem;
}
.index-content section {
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.15),
    0 2px 6px 0 rgba(36, 186, 184, 0.2), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  background: #fff;
}
.index-content section h3 {
  display: flex;
  font-weight: normal;
  height: 1.04rem;
  background: #fdfbfb;
  align-items: center;
  padding: 0 0.28rem;
}
.index-content section h3 span:first-child {
  flex-grow: 1;
  font-size: 0.32rem;
  color: #354052;
}
.every-dor {
  display: flex;
  padding: 0.24rem 0.28rem;
  background: #fff;
  position: relative;
}
.dor-img {
  height: 1.64rem;
  width: 1.24rem;
  overflow: hidden;
  margin-right: 0.24rem;
  border: 1px solid #dfe3e9;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
.dor-img img {
  display: block;
  width: 100%;
}
.dor-info {
  flex-grow: 1;
  flex-basis: 0;
}
.dor-info::after {
  display: block;
  content: "";
  height: 1px;
  position: absolute;
  bottom: 0;
  right: 0;
  background: #dfe3e9;
  width: calc(100% - 1.76rem);
}
.every-dor:last-child .dor-info::after {
  height: 0;
}
.dor-info h4 {
  font-weight: normal;
  font-size: 0.32rem;
  color: #354052;
  display: flex;
}
.dor-info h4 span {
  text-align: right;
  flex-basis: 0;
  flex-grow: 1;
  font-size: 0.28rem;
  color: #6895ff;
  line-height: 0.5rem;
}
.dor-info h4 span img {
  height: 0.32rem;
  vertical-align: middle;
  margin-top: -0.06rem;
  margin-left: 0.12rem;
}
.dor-info p {
  font-size: 0.28rem;
  color: #7f8fa4;
}
.dor-info p span {
  margin-right: 0.4rem;
}
.dor-info div {
  font-size: 0.28rem;
  color: #bbbbbb;
  line-height: 0.36rem;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
</style>
