<template>
  <!--授权登录-->
  <div>
    <!-- {{title}} -->
    <loading :show="true" text="正在授权医保"></loading>
    <van-overlay :show="loadingShow" class="overlay_mask">
      <img src="../../assets/loading.gif" alt="" class="" />
      <p>加载中</p>
    </van-overlay>
  </div>
</template>

<script>
import { toolsUtils, storage, ajax } from "../../common";
import { authService } from "../../services/index";
import apiUrls from "../../config/apiUrls";
import { setTimeout } from "timers";
import { Loading } from "vux";

export default {
  components: {
    Loading
  },
  data() {
    return {
      title: "正在授权医保中...",
      success: "",
      retCode: "",
      qrcode: "",
      cfMingxis: {},
      loadingShow: true,
      requestcontent: {
        payAuthNo: "",
        payOrdId: "",
        setlLatlnt: ""
      }
    };
  },
  created() {
    this.retCode = this.$route.query["retCode"];
    this.qrcode = this.$route.query["authCode"];
    storage.session.set("IsMedicalInsurance", JSON.stringify(false));
    this.cfMingxis = JSON.parse(storage.session.get("cfMingxis"));
    this.getybinfo();
  },
  methods: {
    getybinfo() {
      //跳转到业务页面
      if (this.retCode != "0") {
        //错误
        //toolsUtils.alert("授权失败");
        this.$router.go(-3);
        return;
      }
      var data = {
        openid: storage.session.get("openid"),
        qrcode: this.qrcode
      };
      // alert("授权返回参数：" + JSON.stringify(data));
      //调用查询接口
      ajax
        .post(apiUrls.userQuery, data)
        .then(r => {
          if (r.data.success != true) {
            storage.session.set("IsMedicalInsurance", JSON.stringify(false));
            // alert(
            //   "授权失败" +
            //     r.data.returnMsg +
            //     "     |状态：" +
            //     r.data.success +
            //     "     |qrcode" +
            //     data.qrcode +
            //     "     |openid" +
            //     data.openid
            // );
            // window.location.href = "https://qtyywx.qtyy.com/cfList";
            alert("授权失败,请重新尝试。");
            this.$router.go(-3);
          } else {
            let obj = r.data.returnData;
            // alert(
            //   "授权成功" +
            //    "     |UserQuery接口返参:" +
            //     JSON.stringify(r.data.returnData)
            //     "     |pay_auth_no:" +
            //     obj.pay_auth_no +
            //     "     |longitude:" +
            //     obj.user_longitude_latitude.longitude +
            //     "     |latitude:" +
            //     obj.user_longitude_latitude.latitude
            // );
            // toolsUtils.alert("授权成功");
            if (obj.user_name != this.cfMingxis.patName) {
              alert("当前就诊人非当前医保用户本人。");
              this.$router.go(-3);
            }
            storage.session.set("IsMedicalInsurance", JSON.stringify(true));
            storage.session.set("pay_auth_no", obj.pay_auth_no);
            storage.session.set(
              "longitude",
              obj.user_longitude_latitude.longitude
            );
            storage.session.set(
              "latitude",
              obj.user_longitude_latitude.latitude
            );
            var cfMingxisData = {
                openid: this.cfMingxis.openid,
                id_card: this.cfMingxis.id_card,
                patCardNo: this.cfMingxis.patCardNo,
                orderid: this.cfMingxis.orderid,
                payAmout: this.cfMingxis.payAmout,
                visitId: this.cfMingxis.visitId,
                settleMethodId: this.cfMingxis.settleMethodId,
                settleMethodName: this.cfMingxis.settleMethodName,
                patientidpid: this.cfMingxis.patientidpid,
                deptName: this.cfMingxis.deptName,
                deptId: this.cfMingxis.deptId,
                doctorId: this.cfMingxis.doctorId,
                doctorName: this.cfMingxis.doctorName,
                patName: this.cfMingxis.patName,
                patTel: this.cfMingxis.patTel,
                detailId: this.cfMingxis.detailId,
                listDatastr: this.cfMingxis.listDatastr,
                payMode: this.cfMingxis.payMode
              },
              SetYBinfoModel = {
                arg0:
                  cfMingxisData.patCardNo +
                  "," +
                  cfMingxisData.visitId +
                  "|" +
                  cfMingxisData.detailId.replace(/,/g, "&") +
                  "^" +
                  "Ayaneru" +
                  "," +
                  cfMingxisData.doctorId +
                  "," +
                  cfMingxisData.settleMethodId +
                  "," +
                  obj.pay_auth_no +
                  "," +
                  obj.user_longitude_latitude.longitude +
                  "," +
                  obj.user_longitude_latitude.latitude +
                  "," +
                  "wx" +
                  "," +
                  "1",
                patCardNo: cfMingxisData.patCardNo,
                patientidpid: cfMingxisData.patientidpid,
                visitId: cfMingxisData.visitId,
                detailId: cfMingxisData.detailId,
                doctorId: cfMingxisData.doctorId,
                settleMethodId: cfMingxisData.settleMethodId,
                payMode: cfMingxisData.payMode,
                pay_auth_no: obj.pay_auth_no,
                isIndividualAccount: "1"
              };
            storage.session.set(
              "SetYBinfoModel",
              JSON.stringify(SetYBinfoModel)
            );
            ajax
              .post(apiUrls.SetYBinfo, SetYBinfoModel)
              .then(r => {
                r = r.data;
                if (r.success == true) {
                  storage.session.set(
                    "GetYBinfoModel",
                    JSON.stringify(r.returnData)
                  );
                  this.requestcontent.payAuthNo = obj.pay_auth_no;
                  this.requestcontent.payOrdId = r.returnData.payOrdId;
                  this.requestcontent.setlLatlnt =
                    obj.user_longitude_latitude.longitude +
                    "," +
                    obj.user_longitude_latitude.latitude;
                  storage.session.set(
                    "requestcontent",
                    JSON.stringify(this.requestcontent)
                  );
                  // alert("获取医保信息成功:" + JSON.stringify(r.returnData));
                  // alert(
                  //   "自费金额:" +
                  //     r.returnData.payAmout +
                  //     "统筹金额" +
                  //     r.returnData.recPayAmount +
                  //     "总金额" +
                  //     r.returnData.totalPayAmount +
                  //     "订单号" +
                  //     r.returnData.payOrdId
                  // );
                  //生成微信订单
                  ajax
                    .post(apiUrls.addRegPatPreOrders, cfMingxisData)
                    .then(r => {
                      r = r.data;
                      storage.session.set("cfpayMode", this.payMode);
                      storage.session.set("orderid", r.returnData);
                      if (r.success == false) {
                        toolsUtils.alert(r.returnMsg);
                        if (
                          r.returnMsg ==
                          "您有未支付订单，请取消或支付上个处方订单"
                        ) {
                          this.$router.push({
                            path: "/cfregisterRecord"
                          });
                        }
                        return;
                      }
                      this.$router.push({
                        path: "/cfpayInfo",
                        query: {
                          orderid: r.returnData,
                          detailId: this.$route.query.recipeId
                        }
                      });
                    })
                    .catch(e => {
                      toolsUtils.alert("程序异常:" + JSON.stringify(e));
                      storage.session.set(
                        "IsMedicalInsurance",
                        JSON.stringify(false)
                      );
                      this.$router.go(-3);
                    });
                } else {
                  alert("调用插入医保接口失败:" + r.returnMsg);
                  storage.session.set(
                    "IsMedicalInsurance",
                    JSON.stringify(false)
                  );
                  this.$router.go(-3);
                  return;
                }
              })
              .catch(e => {
                toolsUtils.alert("程序异常:" + JSON.stringify(e));
                storage.session.set(
                  "IsMedicalInsurance",
                  JSON.stringify(false)
                );
                this.$router.go(-3);
                return;
              });
            // window.location.href = "https://qtyywx.qtyy.com/cfpayInfo";
            //toolsUtils.sleep(1000);
          }
        })
        .catch(e => {
          alert("接口异常" + e);
          storage.session.set("IsMedicalInsurance", JSON.stringify(false));
          this.$router.go(-3);
          return;
        });
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped style lang="less" scoped>
.overlay_mask {
  background: rgba(255, 255, 255, 0.8);
  padding-top: 2rem;
  img {
    display: block;
    margin: 0 auto;
  }
  p {
    text-align: center;
    max-width: 5.6rem;
    margin: 0.4rem auto 0;
    color: #3b71e8;
    font-size: 0.4rem;
  }
}
</style>
