<template>
  <!--支付成功后的取消订单和支付成功的页面-->
  <div class="register-record">
    <div class="select-wrap">
      <div class="head-wrap" @click="selectFun()">
        就诊人
        <span>
          {{ showPatName }}
          <img src="../../assets/Y-down.png" alt="" />
        </span>
      </div>
      <!-- 下拉列表 -->
      <div class="user-list" v-show="toggleFun">
        <group>
          <radio
            :options="userList"
            v-model="choosePatCard"
            @on-change="changeUser"
          ></radio>
        </group>
      </div>
    </div>
    <!-- 记录列表 -->
    <div class="list-wrap">
      <div
        class="every-record"
        :key="index"
        v-for="(list, index) in chooseData"
        @click="selectOrders(list.status, list.orderNo)"
      >
        <group class="cell-group">
          <cell
            :title="list.deptName + ':' + list.doctorName"
            is-link
            class="cell cell-head"
          >
            <div>
              <span :class="colorFun(list.status)">{{
                list.status | capitalize
              }}</span>
              <!-- <span :class="colorFun(list.status)">缴费成功（已缴费）</span> -->
            </div>
          </cell>
          <cell
            title="缴费时间"
            :value="list.updateTime.split('T')[0]"
            class="cell"
          ></cell>
          <cell title="处方金额" class="cell">
            <div>
              <span style="color: #FD7070;">￥{{ list.payAmout / 100 }}</span>
            </div>
          </cell>
        </group>
      </div>
    </div>
  </div>
</template>

<script>
import { Cell, Group, Radio } from "vux";
import { toolsUtils, storage, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "index",
  components: {
    Cell,
    Group,
    Radio
  },
  data() {
    return {
      title: "选择科室",
      checkUser: "凉快圣",
      checkFlag: false,
      //   userData1:[{
      //       value:"china",
      //       key:"001"
      //   },{
      //       value:"史珍香",
      //       key:"002"
      //   },{
      //       value:"李白",
      //       key:"003"
      //   }],
      userList: [], //家人门诊信息
      choosePatCard: "", //选中的就诊人的门诊卡号
      showPatName: "全部",
      listData: [], //全部订单列表
      chooseData: [] //选中的就诊人的订单列表
    };
  },
  filters: {
    capitalize: function(value) {
      if (value == 1) {
        return "预约中(待支付)";
      }
      if (value == 0) {
        return "订单超时(已取消)";
      }
      if (value == 2) {
        return "支付成功(已支付)";
      }
      if (value == 3) {
        return "订单取消(已退款)";
      }
      if (value == 4) {
        return "订单取消";
      }
      if (value == 6) {
        return "异常订单";
      }
    }
  },
  mounted() {
    this.getCfOrderList();
    this.testgetAccountInfo();
  },
  methods: {
    //根据用户信息获取其亲属的就诊人信息
    testgetAccountInfo() {
      this.account = JSON.parse(storage.session.get("user")) || {};
      var data = {
        type: "1",
        id_card: this.account.idCard,
        openid: this.account.accountCode
      };
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          var userData = r.returnData;
          for (var i = 0; i < userData.length; i++) {
            var obj = { value: userData[i].patName, key: userData[i].patCode };
            this.userList.push(obj);
          }
          console.log(this.userList);
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },

    //切换就诊人
    changeUser(value, label) {
      //value为门诊卡号,label为姓名

      console.log(value, label);
      // console.log(this.choosePatCard);

      this.showPatName = label;
      var chooseList = [];
      console.log(this.listData.length);
      for (var i = 0; i < this.listData.length; i++) {
        if (this.listData[i].patCardNo == value) {
          console.log(this.listData[i]);
          chooseList.push(this.listData[i]);
        }
      }
      this.chooseData = chooseList;
      console.log(this.chooseData);
      this.checkFlag = false;
    },

    //获取处方列表
    getCfOrderList() {
      var data = {
        id_card: JSON.parse(storage.session.get("user")).idCard,
        openid: JSON.parse(storage.session.get("user")).accountCode
      };
      ajax
        .post(apiUrls.GetCfOrderList, data)
        .then(r => {
          data = r.data;
          if (data.returnData == null) {
            toolsUtils.alert(data.returnMsg);
          }
          this.listData = data.returnData;
          this.chooseData = data.returnData;
          console.log(this.listData);
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    selectFun() {
      this.checkFlag = !this.checkFlag;
    },
    colorFun(n) {
      switch (n) {
        case 0:
          return "timeout";
        case 1:
          return "unpaid";
        case 2:
          return "payed";
        case 3:
          return "refund";
        case 4:
          return "cancel";
      }
    },
    selectOrders(type, orderid) {
      if (type == 2) {
        this.$router.push({
          path: "cfPaySuccess",
          query: { orderid: orderid }
        });
      }
      if (type == 1) {
        this.$router.push({
          path: "cfNowPay",
          query: { orderid: orderid }
        });
      }
      if (type == 0) {
        this.$router.push({
          path: "cfNowPay",
          query: { orderid: orderid }
        });
      }
      if (type == 4) {
        this.$router.push({
          path: "cfNowPay",
          query: { orderid: orderid }
        });
      }
      if (type == 6) {
        this.$router.push({
          path: "cfNowPay",
          query: { orderid: orderid }
        });
      }
    }
  },
  computed: {
    toggleFun() {
      return this.checkFlag;
    }
  },
  created() {
    if (storage.session.get("patcard") == null) {
      alert("请先绑定门诊卡");
      this.$router.push("/oauth?type=outpatient");
      return;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.register-record {
  height: 100%;
  width: 100%;
  background: #f0eff6;
  overflow: auto;
  padding: 0.24rem;
}
.select-wrap {
  position: relative;
}
.user-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: #fff;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
.head-wrap {
  height: 0.96rem;
  border-radius: 5px;
  background: #24bab8;
  box-shadow: 0 1px 4px -2px rgba(0, 0, 0, 0.2);
  font-size: 0.32rem;
  color: #f1efb3;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
  margin-bottom: 0.24rem;
}
.head-wrap span {
  flex-basis: 0;
  flex-grow: 1;
  text-align: right;
}
.head-wrap span img {
  vertical-align: middle;
}
.every-record {
  border-radius: 5px;
  background: #fff;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.every-record .cell {
  height: 0.96rem;
  font-size: 0.32rem;
  color: #354052;
}
.every-record .cell:nth-child(2)::before {
  border: none;
}
.every-record .cell-head {
  background: #fdfbfb;
}
.timeout {
  color: #6895ff;
}
.unpaid {
  color: #24bab8;
}
.payed {
  color: #f6912c;
}
.refund {
  color: #f6912c;
}
.cancel {
  color: red;
}
</style>
