<template>
  <div class="personal">
    <div class="qr-code">
      <!-- <img src="../../assets/NewImg/outpatientImg/qrcode.png" alt />:text="codeUrl" :size="360" :margin="4" :logoMargin='4' logoBackgroundColor	 = "#fff" :logoScale="48" :logoCornerRadius="8" -->
      <vue-qr :logoSrc="imgUrl" :text="codeUrl"></vue-qr>
    </div>
    <van-cell-group>
      <van-field v-model="name" label="姓名" readonly />
      <van-field v-model="idCard" label="身份证号码" readonly />
      <van-field v-model="phoneNumber" label="手机号" readonly />
      <van-field v-model="patid" label="院内id" readonly />
    </van-cell-group>
    <div class="submitBtn" @click="toTXcardList()">
      <span>进入卡包</span>
    </div>
    <p @click="delHealthCard()" class="tips">{{ tips }} ></p>
  </div>
</template>
<script>
import VueQr from "vue-qr";
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Field, CellGroup } from "vant";
import "vant/lib/Field/style";
export default {
  components: {
    [Field.name]: Field,
    [CellGroup.name]: CellGroup,
    VueQr
  },
  name: "personal",
  data() {
    return {
      name: "",
      idCard: "",
      phoneNumber: "",
      codeUrl: "",
      patid: "",
      qRCode:
        "07BA790D4875FC264C10217396ABB8D679BA3F57E2BA5C6B435CCFDEBD33B469:0:961DC064F5798530A63D203EA59BA1A0:4401A0058GZHA0001",
      hos_code: "",
      imgUrl: require("../../assets/NewImg/outpatientImg/logo_.png"),
      tips: "解绑电子健康卡"
    };
  },
  created() {
    this.getMeData();
  },
  methods: {
    getMeData() {
      var that = this;
      var data = {
        openid: storage.session.get("openid") || "",
        qRCode: this.$route.query.qRCode
      };

      ajax
        .post(apiUrls.getMyHealthCard, data)
        .then(r => {
          console.log(r);
          if (!r.data.success) {
            toolsUtils.alert(r.data.returnMsg);
            this.$router.go(-1);
            return;
          }
          console.log(r.data.returnData);
          that.name = r.data.returnData.patName;
          that.idCard = `${r.data.returnData.patIdCard.substring(
            0,
            4
          )}**********${r.data.returnData.patIdCard.substring(14, 18)}`;
          that.phoneNumber = `${r.data.returnData.patMobile.substring(
            0,
            3
          )}****${r.data.returnData.patMobile.substring(7, 11)}`;
          that.codeUrl = r.data.returnData.qRCode;
          that.patid = r.data.returnData.patCode;
          that.qRCode = r.data.returnData.qRCode;
          that.hos_code = r.data.returnData.hos_code;
        })
        .catch(e => {
          console.log(e);
        });
    },
    delHealthCard() {
      if (!confirm("确定解绑健康卡吗？")) {
        return;
      }
      var that = this;
      let info = JSON.parse(storage.session.get("user"));

      var data = {
        id_card: info.idCard,
        patCardNo: this.$route.query.patCardNo,
        relation: this.$route.query.relation.trim(),
        type: this.$route.query.type
      };

      ajax
        .post(apiUrls.DeletePatfamilyrelation, data)
        .then(r => {
          console.log(r);
          if (!r.data.success) {
            console.log(r.data.returnMsg);
            return;
          }
          toolsUtils.alert("解绑成功！");
          this.$router.push({
            path: "/cardListV2",
            query: {
              type: "1",
              method: storage.session.get("method")
            }
          });
        })
        .catch(e => {
          console.log(e);
        });
    },
    toTXcardList() {
      var that = this;
      var data = {
        appid: "587b0f2f4a371325ab35a17b9fd3b237",
        qrCode: that.qRCode
      };
      ajax
        .post(apiUrls.getOrderId, data)
        .then(r => {
          console.log(r);
          if (!r.data.success) {
            console.log(r.data.returnMsg);
            return;
          }
          // debugger
          let burl = encodeURIComponent("https://qtyywx.qtyy.com");
          window.location.href = `https://health.tengmed.com/open/takeMsCard?order_id=${r.data.returnData}&redirect_uri=${burl}`;
          return;
        })
        .catch(e => {
          console.log(e);
        });
    }
  }
};
</script>
<style scoped>
.personal {
  width: 100%;
  height: 100%;
}
.personal .qr-code {
  width: 100%;
  height: 4.5rem;
  background: #fff;
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.personal .qr-code img {
  width: 3.45rem;
  height: 3.45rem;
}
.personal .submitBtn {
  width: 90%;
  height: 1.08rem;
  border-radius: 8px;
  margin: 0.68rem auto 0;
  font-size: 0.36rem;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: linear-gradient(to right, #3dc0f3, #2dc4c2);
  box-shadow: 1px 1px 2px #52675a;
}
.personal .tips {
  text-align: center;
  line-height: 0.6rem;
  font-size: 0.26rem;
  color: #2054d1;
  text-decoration: underline;
}
</style>
