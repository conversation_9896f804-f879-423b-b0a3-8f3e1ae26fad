<template>
  <!--首页-->
  <div class="success-page">
    <!-- head -->
    <div class="success-head">
      <img src="../../assets/chongzhichenggong.png" alt="" />
      <h3>支付成功</h3>
    </div>
    <!-- content -->
    <div class="success-content">
      <div class="list-wrap">
        <p>支付方式</p>
        <span>{{ listData.payCode }}</span>
      </div>
      <div class="list-wrap">
        <p>缴费金额</p>
        <span>{{ listData.payMoney }}元</span>
      </div>
      <!-- <div class="list-wrap">
                <p>当前余额</p>
                <span>{{listData.nowMoney}}</span>
            </div> -->
      <!-- <div class="list-wrap">
                <p>交易流水号</p>
                <span>{{listData.flowNum}}</span>
            </div> -->
      <div class="list-wrap">
        <p>交易时间</p>
        <span>{{ listData.tradTime }}</span>
      </div>
    </div>
    <!-- button -->
    <div class="footer-btn">
      <!-- <button><router-link to="/">完成</router-link></button> -->
      <button @click="close()"><font color="white">完成</font></button>
    </div>
  </div>
</template>

<script>
import { toolsUtils } from "../../common";
export default {
  name: "index",
  components: {
    // HeaderBar
  },
  data() {
    var now = new Date();
    return {
      title: "首页",
      IsBack: false,
      listData: {
        payCode: "微信支付",
        payMoney: this.$route.query.money,
        orderid: this.$route.query.orderid,
        tradTime: toolsUtils.getNow()
      }
    };
  },
  methods: {
    // close(){
    //   WeixinJSBridge.call('closeWindow');
    //   return
    // }
    close() {
      //处方跳转
      if (this.$route.query.iscf == "1") {
        window.location.href =
          "/cfPaySuccess?orderid=" + this.$route.query.orderid;
      } else {
        WeixinJSBridge.call("closeWindow");
      }
      return;
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.success-page {
  padding: 0.2rem;
  background: #f0eff6;
  height: 100%;
  width: 100%;
  overflow: auto;
}
.success-head {
  padding: 0.36rem 0 0.44rem 0;
}
.success-head img {
  width: 1.52rem;
  height: 1.52rem;
  display: block;
  margin: 0 auto 0.2rem;
}
.success-head h3 {
  text-align: center;
  font-size: 0.4rem;
  color: #354052;
  font-weight: normal;
}
.success-content {
  width: 100%;
  background: #fff;
  border-radius: 5px;
  padding: 0.4rem 0.28rem;
  margin-bottom: 0.48rem;
}
.success-content .list-wrap {
  display: flex;
  height: 0.44rem;
  align-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.success-content .list-wrap:last-child {
  margin-bottom: 0;
}
.success-content .list-wrap span {
  color: #354052;
  flex-grow: 1;
  text-align: right;
}
.footer-btn button {
  width: 100%;
  height: 1.08rem;
  border-radius: 5px;
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  border: 1px solid #24bab8;
  display: block;
  font-size: 0.36rem;
  color: #ffffff;
}
</style>
