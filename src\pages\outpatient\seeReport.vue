<template>
  <!--查看报告-->
  <div class="see-report">
    <header>
      <div class="head-img">
        <img src="../../assets/user_face.png" alt="" />
      </div>
      <div class="user-info">
        <h3>{{ patcard.patName }}</h3>
        <p>家人关系：{{ relationName(patcard.relationType) }}</p>
        <p>患者编号：{{ patcard.patCode }}</p>
        <!-- <p>限制查询三个月内报告</p> -->
      </div>
      <div class="switch-user" @click="showPop()">
        切换家人
        <img src="../../assets/next_yellow.png" alt="" />
      </div>
    </header>
    <div>
      <div class="date-wrap">
        <!-- <div class="block"> -->
        <!-- {{intitime}} -->
        <!-- <el-date-picker @click.native="onItemClick($event)" @change='reportTab(tabIndex)' class="date-el" v-model="intitime" type="daterange" range-separator="至" start-placeholder="开始日期"
                :picker-options="pickerOptions2" end-placeholder="结束日期"></el-date-picker> -->
        <!-- </div> -->

        <div class="dates">
          <el-date-picker
            v-model="value2"
            :editable="editFlag"
            align="right"
            type="date"
            size="large"
            placeholder="请选择日期"
            :picker-options="{}"
            @change="ddd"
          >
          </el-date-picker
          >--
          <span>今天</span>
        </div>
      </div>
      <tab custom-bar-width="40%" active-color="#23B3B3">
        <tab-item @on-item-click="reportTab" selected>检验项目</tab-item>
        <tab-item @on-item-click="reportTab">检查项目</tab-item>
      </tab>
      <div class="list-wrap">
        <ul>
          <li
            v-for="(item, index) in dataList"
            :key="index"
            @click="linkDetail(item.VisitNO)"
          >
            <img src="../../assets/report_3x.png" alt="" />
            <div class="report-wrap">
              <div class="report-num">报告单号：{{ item.VisitNO }}</div>
              <div class="docName">
                <!-- <p>报告医生<span>{{item.ReportDrName}}</span></p> -->
                <p>
                  报告日期<span>{{ datetostring(item.ReportDate) }}</span>
                </p>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="prompt">
        本时间段内没有更多记录，更多记录请<span>重选时段</span>查找
      </div>
    </div>
    <!-- 报告为空时显示的内容 -->
    <div class="content-null">
      <h3>
        温馨提示：<span>本系统仅支持查看通过微信公众号缴费的消费记录。</span>
      </h3>
      <p>抱歉，没有记录</p>
    </div>
    <!-- <div class="pop" :class="closFlag?'clos-date':''" @click="closDateFun()">asldasdjdsf</div> -->

    <!-- 弹窗 -->
    <div v-transfer-dom>
      <x-dialog v-model="showHideOnBlur" class="dialog-demo" hide-on-blur>
        <div class="img-box">
          <div class="popup-wrap">
            <h3>家人姓名</h3>
            <div class="popup-content">
              <div class="patient-list">
                <div class="left-solid">
                  <div></div>
                </div>
                <ul>
                  <li
                    v-for="(list, index) in userList"
                    :key="index"
                    @click="showHideOnBlur = false"
                  >
                    <input type="radio" id="id_1" name="patient" />
                    <label for="id_1" @click="changeUser(list.patCode)">{{
                      list.patName
                    }}</label>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </x-dialog>
    </div>
  </div>
</template>

<script>
import { Tab, TabItem, Group, XSwitch, XDialog } from "vux";
import $ from "jquery";
import DateGroup from "../../components/outpatient/dateGroup";
import { ajax, storage, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import { TransferDomDirective as TransferDom } from "vux";

export default {
  components: {
    Tab,
    TabItem,
    XDialog,
    Group,
    XSwitch
  },
  directives: {
    TransferDom
  },
  data() {
    return {
      editFlag: false, //日期标识
      title: "",
      pickerOptions1: {
        disabledDate(time) {
          // 三个月日期选择限制
          let curDate = new Date().getTime();
          let three = 90 * 24 * 3600 * 1000;
          let threeMonths = curDate - three;
          return time.getTime() > Date.now() || time.getTime() < threeMonths;
          // return time.getTime() > Date.now();
        }
        // shortcuts: [{
        //   text: '今天',
        //   onClick(picker) {
        //     picker.$emit('pick', new Date());
        //   }
        // }, {
        //   text: '昨天',
        //   onClick(picker) {
        //     const date = new Date();
        //     date.setTime(date.getTime() - 3600 * 1000 * 24);
        //     picker.$emit('pick', date);
        //   }
        // }, {
        //   text: '一周前',
        //   onClick(picker) {
        //     const date = new Date();
        //     date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
        //     picker.$emit('pick', date);
        //   }
        // }]
      },

      value2: dataUtils.changedatetype(
        new Date(new Date().getTime() - 1000 * 60 * 60 * 24),
        "YYYY-MM-DD"
      ),

      // pickerOptions2: {
      //   disabledDate(time) {
      //     return time.getTime() > Date.now();
      //   }
      // },
      intitime: [
        new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * 30),
        new Date()
      ],
      closFlag: false,
      showHideOnBlur: false,
      account: {},
      //卡信息
      patcard: {},
      userList: [],
      dataList: [],
      LaboratoryList: [],
      InspectionList: [],
      starttime: "2019-01-01",
      endtime: "2019-08-01",
      choosedtype: 1,
      tabIndex: 0
    };
  },
  created() {
    //this.value2=dataUtils.changedatetype(new Date(),"YYYY-MM-DD");
    //this.value2 = new Date;
  },
  mounted() {
    this.getAccountInfo();
    if (storage.session.get("seeReportDate") == undefined) {
    } else {
      this.intitime = storage.session.get("seeReportDate").split(",");
    }

    this.reportTab(0);
  },
  methods: {
    ddd() {
      this.reportTab(this.tabIndex);
    },
    onItemClick(e) {
      e.target.blur();
      this.closFlag = true;
    },
    // 跳转到报告明细页
    linkDetail(visitNo) {
      if (this.choosedtype == 1) {
        this.$router.push({
          path: "/reportDetail",
          query: {
            visitNo: visitNo
          }
        });
      }
      if (this.choosedtype == 2) {
        this.$router.push({
          path: "/reportCheckDetail",
          query: {
            visitNo: visitNo
          }
        });
      }
    },
    showPop() {
      this.showHideOnBlur = true;
    },
    reportTab(index) {
      if (this.intitime.length > 1) {
        storage.session.set("seeReportDate", this.intitime);
        this.starttime = dataUtils.changedatetype(
          this.intitime[0],
          "YYYY-MM-DD"
        );
        this.endtime = dataUtils.changedatetype(this.intitime[1], "YYYY-MM-DD");
      }
      if (index == 0) {
        this.tabIndex = index;
        this.getLaboratoryList();
      }
      if (index == 1) {
        this.tabIndex = index;
        this.getInspectionList();
      }
    },
    //获取用户信息
    getAccountInfo() {
      this.account = JSON.parse(storage.session.get("user")) || {};
      this.patcard = JSON.parse(storage.session.get("patcard")) || {};
      // console.log(this.patcard)
      this.indexCard = this.patcard.patCode;
      var data = {
        type: "1",
        id_card: this.account.idCard,
        openid: this.account.accountCode
      };
      // console.log(data);
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.userList = r.returnData;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    changeUser(patcardNo) {
      var data = {
        patcardNo,
        id_card: this.account.idCard
      };
      if (
        this.account.idCard == null ||
        this.account.idCard == "" ||
        this.account.idCard == undefined
      ) {
        storage.session.delete("patcard");
        storage.session.delete("user");
        alert("微信授权失败，请重新登入");
        window.location.reload();
      }
      ajax
        .post(apiUrls.GetpatFamilyInfo, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          storage.session.set("patcard", JSON.stringify(r.returnData));
          this.patcard = r.returnData;
          this.reportTab(this.tabIndex);
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    relationName(val) {
      switch (val) {
        case "0":
          return "自己";
          break;
        case "1":
          return "父母";
          break;
        case "2":
          return "配偶";
          break;
        case "3":
          return "子女";
          break;
        default:
          return "其他";
          break;
      }
    },

    getLaboratoryList() {
      var data = {
        id_card: this.account.idCard,
        patCardNo: JSON.parse(storage.session.get("patcard")).patCode,
        begintime: dataUtils.changedatetype(this.value2, "YYYY-MM-DD"),
        endtime: dataUtils.changedatetype(new Date(), "YYYY-MM-DD")
      };
      // console.log(data);
      ajax
        .post(apiUrls.getLaboratoryList, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          //去重
          var result = [];
          var obj = {};
          for (var i = 0; i < r.returnData.length; i++) {
            if (!obj[r.returnData[i].VisitNO]) {
              result.push(r.returnData[i]);
              obj[r.returnData[i].VisitNO] = true;
            }
          }
          this.LaboratoryList = result;
          this.dataList = this.LaboratoryList;
          this.choosedtype = 1;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    getInspectionList() {
      var data = {
        patCardNo: JSON.parse(storage.session.get("patcard")).patCode,
        begintime: dataUtils.changedatetype(this.value2, "YYYY-MM-DD"),
        endtime: dataUtils.changedatetype(new Date(), "YYYY-MM-DD")
      };
      // console.log(data);
      ajax
        .post(apiUrls.getInspectionList, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          //去重
          var result = [];
          var obj = {};
          for (var i = 0; i < r.returnData.length; i++) {
            if (!obj[r.returnData[i].VisitNO]) {
              result.push(r.returnData[i]);
              obj[r.returnData[i].VisitNO] = true;
            }
          }
          this.InspectionList = result;
          this.dataList = this.InspectionList;
          this.choosedtype = 2;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    datetostring(str) {
      if (str != undefined) {
        str = str.replace(/-/g, "/");
      }
      var date = new Date(str);
      return dataUtils.dateToString(date);
    }
  },
  computed: {},
  created() {
    if (storage.session.get("patcard") == null) {
      alert("请先绑定门诊卡");
      this.$router.push("/oauth?type=outpatient");
      return;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.see-report {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f7fafa;
}
.dates {
  display: flex;
}
.dates .el-input {
  width: 100% !important;
  flex: 1;
}
.dates > span {
  flex: 1;
  /* -webkit-appearance: none;
    border-radius: 4px;
    
    box-sizing: border-box;
    color: #606266;
    height: 40px;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color .2s cubic-bezier(.645,.045,.355,1);
    transition: border-color .2s cubic-bezier(.645,.045,.355,1); */
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  background: #fff;
  text-align: center;
  line-height: 40px;
}
.see-report header {
  padding: 0.24rem;
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  display: flex;
  align-items: center;
  background: #fff;
  margin-bottom: 0.24rem;
}
.see-report header .head-img {
  height: 1.2rem;
  width: 1.2rem;
  border-radius: 100%;
  overflow: hidden;
}
.see-report header .head-img img {
  display: block;
  height: 100%;
}
.see-report header .user-info {
  flex: 1;
  padding: 0 0.24rem;
}
.see-report header .user-info h3 {
  font-size: 0.32rem;
  color: #4d4d4d;
  font-weight: normal;
}
.see-report header .user-info p {
  font-size: 0.26rem;
  color: #999999;
}
.see-report header .switch-user {
  font-size: 0.32rem;
  color: #23b3b3;
  text-align: right;
  display: flex;
  align-items: center;
}
.date-wrap {
  margin-bottom: 0.24rem;
  padding: 0 0.24rem;
  font-size: 20px;
}
.list-wrap ul li {
  display: flex;
  align-items: center;
  padding: 0 0 0 0.24rem;
  height: 1.28rem;
  background: #fff;
}
.list-wrap ul li img {
  height: 0.48rem;
  margin-right: 0.24rem;
}
.report-wrap {
  display: flex;
  align-items: center;
  flex: 1;
  padding-right: 0.24rem;
  height: 100%;
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
}
.list-wrap ul li:last-child .report-wrap {
  border: none;
}
.report-wrap .report-num {
  font-size: 0.28rem;
  color: #4d4d4d;
  flex: 1;
}
.report-wrap .docName {
  flex: 1;
  padding-left: 0.4rem;
}
.report-wrap .docName p {
  display: flex;
  font-size: 0.26rem;
  color: #999999;
}
.report-wrap .docName p span {
  flex: 1;
  text-align: right;
  font-size: 0.24rem;
  color: #4d4d4d;
}
.see-report .prompt {
  font-size: 0.24rem;
  color: #999999;
  height: 0.88rem;
  line-height: 0.88rem;
  text-align: center;
}
.see-report .prompt span {
  color: #4a90e2;
}
.content-null {
  display: none;
}
.content-null h3 {
  height: 0.88rem;
  line-height: 0.88rem;
  border-top: 1px solid rgba(204, 204, 204, 0.5);
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  padding: 0 0.24rem;
  font-size: 0.24rem;
  color: #4a4a4a;
  font-weight: normal;
}
.content-null h3 span {
  color: #4c88fa;
}
.content-null p {
  height: 2.34rem;
  line-height: 2.34rem;
  text-align: center;
  font-size: 12px;
  color: #999999;
  background: #fff;
}
.date-el {
  width: 100%;
}
.switch-user img {
  height: 0.32rem;
  display: block;
  margin-left: 8px;
}
/* .pop {
  width: 100px;
  height: 100px;
  border-radius: 100%;
  background: #23b3b3;
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 999999999;
  display:none;
}
.clos-date{
  display: block !important;
} */

/* 弹窗内容的样式 */
.popup-wrap {
  width: 100%;
  background: #fff;
  box-shadow: 1px 1px 4px 0 rgba(53, 64, 82, 0.7);
  border-radius: 2px;
}

.popup-wrap h3 {
  height: 1.12rem;
  width: 100%;
  font-size: 0.36rem;
  color: #354052;
  text-align: center;
  line-height: 1.12rem;
  font-weight: normal;
  border-bottom: 1px solid #dfe3e9;
}

.popup-content {
  height: 4.48rem;
  width: 100%;
  overflow: auto;
}

.patient-list {
  position: relative;
}

.patient-list:after {
  display: block;
  content: "";
  clear: both;
}

.left-solid {
  width: 1.74rem;
  height: 100%;
  padding: 0.6rem 0;
  position: absolute;
  top: 0;
}

.left-solid div {
  margin: 0 auto;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #24bab8;
}

.patient-list ul {
  display: block;
  padding-left: 1.74rem;
}

.patient-list ul li {
  height: 1.12rem;
  width: 100%;
  line-height: 1.12rem;
  border-bottom: 1px solid #dfe3e9;
}

.patient-list ul li label {
  display: block;
  height: 100%;
  width: 100%;
  font-size: 0.32rem;
  color: #7f8fa4;
  padding-left: 1rem;
  position: relative;
  text-align: left;
}

.patient-list ul li input {
  display: none;
}

.patient-list ul li label:after {
  display: block;
  content: "";
  height: 10px;
  width: 10px;
  border-radius: 100%;
  background: #24bab8;
  position: absolute;
  top: 50%;
  margin-top: -5px;
  left: -0.96rem;
  z-index: 2;
}

.patient-list ul li input:checked ~ label:after {
  transform: scale(2);
  transition: transform 300ms ease-out;
}
</style>
