<template>
<!--绑定页面-->
    <div class="bind-page">
        <div class="form-wrap">
           <form action="">
               <div class="input-wrap">
                   <h3>绑定开启门诊功能</h3>
                   <label for=""><input type="text" v-model="bindMsg.bindName" placeholder="真实姓名"></label>
                   <label for=""><input type="text" v-model="bindMsg.bindPatCardNo" placeholder="医疗卡号"></label>
                   <p>备注：请先关注公众号后再进行操作</p>
                   <p>请务必提供真实有效信息，否则将无法开启</p>
                    <input type="button" @click="bindInfo" value="确认"/>
               </div>

           </form>
       </div>
    </div>
</template>

<script>
import {ajax,toolsUtils,dataUtils, storage} from "../../common"
import apiUrls from '../../config/apiUrls'
import { isNullOrUndefined } from 'util';
export default {
  name: "index",
  components: {
    // HeaderBar
  },
  data() {
    return {
      title: "绑定页面",
      bindMsg:{
        bindName:'',
        bindPatCardNo:''
      }
    };
  },
  created(){
      if (storage.session.get("user")==null)
     {
       toolsUtils.alert('请先绑定信息')
       this.$router.push("/oauth?type=register")
       return;
     }
  },
  methods: {
    //绑定门诊卡
    bindInfo:function(){
      debugger
      // if(dataUtils.isPatName(this.bindMsg.bindName)!=true)
      // {
      //     toolsUtils.alert('请输入正确名字')
      //     return;
      // }
      // if(this.bindMsg.bindPatCardNo==null||this.bindMsg.bindPatCardNo=="")
      {
         toolsUtils.alert('门诊卡号不正确')
         return;
      }
      var info=JSON.parse(storage.session.get("user"))
      var data={
        name:this.bindMsg.bindName,
        patCardNo:this.bindMsg.bindPatCardNo,
        id_card:info.idCard
      }
      // console.log(data)
      //更新数据
      ajax.post(apiUrls.bindPatCard,data).then(r=>{
        console.log(r)
        r=r.data;
        if(r.success==false){
          toolsUtils.alert(r.returnMsg);
          return
        }
        storage.session.set('patcard', JSON.stringify(r.returnData));
        if(storage.session.get('redirect')!=null&&storage.session.get('redirect')!="")
        {
           this.$router.push({path:storage.session.get('redirect')})
           return;
        }
        this.$router.push({path:"/outpatientPage"})
        return
      })
     
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.bind-page{
    height:100%;
    width:100%;
    background: #fff;
    overflow: auto;
}
.form-wrap {
  padding:1rem 0.48rem;
}
.form-wrap form > div {
  width: 100%;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.35),
    0 2px 6px 0 rgba(36, 186, 184, 0.4), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 0.6rem 0.4rem;
  margin-bottom: 0.42rem;
}
.input-wrap {
  display: flex;
  flex-wrap: wrap;
}

.form-wrap form input[type="button"] {
  width: 100%;
  display: block;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 0.96rem;
  font-size: 0.32rem;
  color: #ffffff;
}

.input-wrap label {
  height: 0.96rem;
  background: #f6f6f6;
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  margin-bottom: 0.28rem;
  flex-basis: 100%;
  align-items: center;
}
.input-wrap label input {
  flex-grow: 1;
  border: none;
  background: transparent;
  height: 100%;
  outline: none;
  padding: 0 0.28rem;
  font-size: 0.32rem;
}

.form-wrap form input[type="submit"] {
  width: 100%;
  display: block;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 0.96rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.input-wrap h3 {
  font-size: 0.4rem;
  color: #354052;
  text-align: center;
  flex-basis: 100%;
  margin-bottom: 0.6rem;
  font-weight: normal;
}
.input-wrap p {
  font-size: 0.26rem;
  color: #fd7070;
  margin-bottom: 0.2rem
}
.input-wrap label input::-webkit-input-placeholder{
    color: #D9D9D9;
}
</style>

