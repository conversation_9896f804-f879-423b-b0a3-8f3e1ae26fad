<template>
  <!--预约的具体时间-->
  <div class="about-time">
    <div class="header">
      <div class="img-wrap">
        <img
          :src="
            imghost +
              dorData.doctorId
                .replace(' ', '')
                .replace(' ', '')
                .replace(' ', '') +
              '.jpg'
          "
          :onerror="defaultImg"
          alt
        />
      </div>
      <div class="doctor-news">
        <h3>
          {{ dorData.doctorName }}
          <span>({{ dorData.doctorTitle }})</span>
        </h3>
        <p>{{ dorData.doctorRemark }}</p>
      </div>
    </div>

    <div class="checkTime-wrap">
      <div class="checkTime-div">
        <div
          class="time-wrap"
          :key="index"
          v-for="(items, index) in doctorTimeInfo"
        >
          <ul>
            <li v-if="items.regleaveCount > -1">
              <!-- <div>
                          <span>{{items.scheduleName}}</span>
                          <span style="margin-left:2%">余号:{{(items.regleaveCount)}}个</span>
                          <span style="margin-left:2%">¥{{pointsTrunRMB(items.treatFee)}}</span>
              </div>-->
              <div>
                <div style="height:.55rem;">{{ items.scheduleName }}</div>
                <div style="height:.55rem;">
                  <div style="width:35%;height:100%;float: left;">
                    余号:{{ items.regleaveCount }}个
                  </div>
                  <div
                    style="width:5%;height:100%;float: left;text-align:right;"
                  >
                    <i style="margin:0 !important;"></i>
                  </div>
                  <div
                    style="width:60%;height:100%;text-align:center;float: left;"
                  >
                    <span>¥{{ pointsTrunRMB(items.treatFee) }}</span>
                  </div>
                </div>
              </div>
              <button
                @click="
                  linkNowPay(
                    items.scheduleName,
                    items.treatFee,
                    items.scheduleType,
                    items.regleaveCount
                  )
                "
              >
                {{ isRegister() }}
              </button>
            </li>
            <li v-if="items.regleaveCount == -1">
              <div>
                <span>{{ items.scheduleName }}</span>
                <i></i>
                <span style="color:red">当前医生已停诊</span>
              </div>
            </li>
            <li v-if="items.regleaveCount == -20 || items.regleaveCount == 0">
              <div>
                <span>{{ items.scheduleName }}</span>
                <i></i>
                <span style="color:red">当前医生已挂满</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import oDate from "../../components/inHospital/date";
import { ajax, storage, dataUtils, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import baseData from "../../config/baseData";
import { TransferDomDirective as TransferDom } from "vux";
export default {
  name: "index",
  directives: {
    TransferDom
  },
  components: {
    oDate
  },
  data() {
    return {
      title: "选择科室",
      dorData: {
        regScheduleInfos: { regScheduleInfo: {} },
        doctorId: "",
        doctorName: "",
        doctorRemark: "",
        doctorTitle: ""
      },
      doctorTimeInfo: [], //存储医生的排班信息
      imghost: baseData.imgHost,
      defaultImg: 'this.src="' + require("../../assets/doctor.png") + '"'
    };
  },
  mounted() {
    storage.session.delete("nopatcode");
    this.getTimeInfo();
  },
  methods: {
    getTimeInfo() {
      var data = JSON.parse(storage.session.get("drId"));
      ajax
        .post(apiUrls.GetDoctorTimeInfo, data)
        .then(r => {
          console.log(r);
          var r = r.data.returnData;
          if (r == null) {
            toolsUtils.alert("该医生当前暂无排班");
            try {
              var backdata = JSON.parse(storage.session.get("timeinfo"));
              this.$router.push({
                path:
                  "/choiceDoctor?method=" +
                  backdata["type"] +
                  "&deptId=" +
                  backdata["deptid"]
              });
            } catch (error) {
              this.$router.go(-2);
            }
            return;
          }
          this.dorData = r.doctorInfo;
          console.log(r.doctorInfo.regScheduleInfos.regScheduleInfo);
          if (r.doctorInfo.regScheduleInfos.regScheduleInfo) {
            this.doctorTimeInfo = r.doctorInfo.regScheduleInfos.regScheduleInfo;
            //去重
            if (r.doctorInfo.regScheduleInfos.regScheduleInfo.length == 2) {
              if (
                r.doctorInfo.regScheduleInfos.regScheduleInfo[0].scheduleName ==
                r.doctorInfo.regScheduleInfos.regScheduleInfo[1].scheduleName
              )
                this.doctorTimeInfo.pop();
            }
          } else {
            toolsUtils.alert("排班信息有误");
          }
        })
        .catch(e => {
          console.log(e);
          toolsUtils.alert("网络异常");
        });
    },

    getptinfo() {
      var day = new Date();
      var month =
        day.getMonth() + 1 < 10
          ? "0" + (day.getMonth() + 1)
          : day.getMonth() + 1;
      var datem =
        day.getDate() < 10 ? "0" + (day.getDate() + 1) : day.getDate() + 1;
      var daytime = day.getFullYear() + "-" + month + "-" + datem;
      return daytime;
    },

    getptinfos() {
      var day = new Date();
      var month =
        day.getMonth() + 1 < 10
          ? "0" + (day.getMonth() + 1)
          : day.getMonth() + 1;
      var datem =
        day.getDate() < 10 ? "0" + (day.getDate() + 1) : day.getDate() + 1;
      var daytime =
        day.getFullYear() +
        "-" +
        month +
        "-" +
        datem +
        " " +
        day.getHours() +
        ":" +
        day.getMinutes() +
        ":" +
        day.getSeconds();
      var Daytime = new Date(daytime);
      return Daytime;
    },

    linkNowPay(scheduleName, treatFee, scheduleType, regleaveCount) {
      var dateday = this.getptinfo();
      var datedays = this.getptinfos();
      var timeout = JSON.parse(storage.session.get("timeinfo")).date;
      var timeouts =
        JSON.parse(storage.session.get("timeinfo")).date + " 23:30:00";
      var Timeouts = new Date(timeouts);
      if (datedays > Timeouts && dateday == timeout) {
        toolsUtils.alert("已超过预约挂号时间");
        return;
      }

      console.log(scheduleName, treatFee, scheduleType, regleaveCount);
      var nowtime = new Date();
      var hour = nowtime.getHours();
      var Minute = nowtime.getMinutes();
      if (
        regleaveCount != null &&
        regleaveCount != undefined &&
        regleaveCount < 0
      ) {
        toolsUtils.alert("当前时段已经停诊");
        return;
      }
      if (
        regleaveCount == null ||
        regleaveCount == undefined ||
        regleaveCount == 0
      ) {
        toolsUtils.alert("当前时段没有号源");
        return;
      }

      var guahuaoType = JSON.parse(storage.session.get("timeinfo")).type; //获取挂号类型
      if (guahuaoType == 0) {
        // var times=scheduleName.split("-");
        // if(times!=null&&times!=undefined&&times.length>1)
        // {
        //    if(times[1].split(":")[0]<=hour)
        //    {
        //       if((times[1].split(":")[0]!=hour)||(times[1].split(":")[0]==hour&&times[1].split(":")[1]<=Minute))
        //       {
        //           toolsUtils.alert("已过当前时段挂号时间");
        //           return;
        //       }
        //   }
        // }

        //判断预约挂号时间是否超时
        var timearr = scheduleName.split("-");
        var times = timearr[timearr.length - 1];
        var regtimestr = new Date(
          nowtime.getFullYear() +
            "-" +
            (nowtime.getMonth() + 1 < 10
              ? "0" + (nowtime.getMonth() + 1)
              : nowtime.getMonth() + 1) +
            "-" +
            nowtime.getDate() +
            " " +
            times
        );
        console.log(regtimestr);
        if (nowtime.getTime() > regtimestr.getTime()) {
          toolsUtils.alert("已过当前时段挂号时间");
          return;
        }
      }
      var deptId = JSON.parse(storage.session.get("drId")).deptId;
      // 中午11点后不能挂上午的号，下午4点30后不能挂下午的号
      // if (scheduleName == "上午"&&deptId!="1110") {
      //   if (hour >= 11 || (hour <= 7 && Minute <= 30)) {
      //     toolsUtils.alert("上午挂号时间为7：30-11:00");
      //     return;
      //   }
      // }

      // if (scheduleName == "下午"&&deptId!="1110") {
      //   if ((hour >= 22 && Minute >= 30) || (hour <= 13 && Minute <= 30)) {
      //     toolsUtils.alert("下午挂号时间为13：30-22:30");
      //     return;
      //   }
      // }

      // else{
      //   if(hour>=17 || hour<14){
      //       toolsUtils.alert("已过今天挂号时间");
      //       return;
      //     }
      // }

      if (storage.session.get("user") == null) {
        this.$router.replace("/oauth?type=aboutTime");
        return;
      }
      if (storage.session.get("patcard") == null) {
        alert("请先绑定诊疗卡");
        storage.session.set("nopatcode", "/aboutTime");
        this.$router.replace({
          path: "/cardListV2",
          query: {
            // id_card: JSON.parse(storage.session.get("user")).idCard,
            type: "1",
            method: "user"
          }
        });

        //this.$router.push("/addPatient");
        return;
      }
      this.getInfo(scheduleName, treatFee, scheduleType);
      this.$router.push({
        path: "/submitAgo"
      });
    },

    isRegister() {
      var guahuaoType = JSON.parse(storage.session.get("timeinfo")).type; //获取挂号类型
      if (guahuaoType == 0) {
        return "挂号";
      }
      return "预约";
    },

    getInfo(scheduleName, treatFee, scheduleType) {
      try {
        var drId = JSON.parse(storage.session.get("drId"));
        var patcard = JSON.parse(storage.session.get("patcard"));
        var user = JSON.parse(storage.session.get("user"));
        var doctor = {
          drname: this.dorData.doctorName,
          paytype: JSON.parse(storage.session.get("timeinfo")).type,
          deptName: storage.session.get("deptName"),
          hosName: baseData.webtag,
          address: baseData.webtag,
          date: drId.date,
          begintime: scheduleName.split("-")[0],
          endtime: scheduleName.split("-")[1],
          payamout: treatFee.replace(".000000", ".00") / 100,
          deptId: drId.deptId,
          doctorId: drId.doctorId,
          scheduleFlag: scheduleType,
          id_card: user.idCard,
          tel: user.telePhone,
          treatFee: treatFee.replace(".000000", ".00") / 100,
          openid: user.accountCode,
          patCardNo: patcard.patCode,
          name: patcard.patName,
          outAddr: this.dorData.outAddr
        };
        storage.session.set("drInfo", JSON.stringify(doctor));
        // console.log("医生数据" + JSON.stringify(doctor));
        return true;
      } catch (error) {
        return false;
      }
    },
    //分转元
    pointsTrunRMB(val) {
      return dataUtils.pointsTrunRMB(val, 2);
    },
    imgError(item) {
      item.img = require("../../assets/doctor.png");
    }
  },

  computed: {},
  created() {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
}
.about-time .header {
  padding: 0.28rem;
  background: #fff;
  display: flex;
  margin-bottom: 0.24rem;
}
.img-wrap {
  width: 1.64rem;
  height: 2.16rem;
  overflow: hidden;
  border-radius: 5px;
  border: 1px solid #ccc;
  display: flex;
  align-items: center;
}
.img-wrap img {
  display: block;
  width: 100%;
}
.doctor-news {
  flex-basis: 0;
  flex-grow: 1;
  padding-left: 0.2rem;
}
.doctor-news h3 {
  font-size: 0.32rem;
  color: #354052;
  font-weight: inherit;
}
.doctor-news h3 span {
  font-size: 0.28rem;
  color: #7f8fa4;
  margin-left: 0.2rem;
}
.doctor-news p {
  font-size: 0.28rem;
  color: #bbbbbb;
}
.checkTime-wrap {
  padding: 0 0.24rem;
}
.checkTime-div {
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.15),
    0 2px 6px 0 rgba(36, 186, 184, 0.2), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  background: #fff;
}
.time-wrap ul li {
  height: 1.3rem;
  border-bottom: 1px solid #dfe3e9;
  padding: 0 0.24rem;
  display: flex;
  align-items: center;
}
.time-wrap ul li div {
  font-size: 0.32rem;
  color: #7f8fa4;
  /* line-height: 1.04rem; */
  vertical-align: middle;
  flex-basis: 0;
  flex-grow: 1;
}
.time-wrap ul li div span:last-child {
  color: #fd7070;
}
.time-wrap ul li div i {
  font-style: inherit;
  margin: -0.06rem 0.26rem 0;
  display: inline-block;
  border-left: 1px solid #ccc;
  height: 0.4rem;
  vertical-align: middle;
}
.time-wrap ul li button {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  border: 1px solid #24bab8;
  border-radius: 2px;
  height: 0.56rem;
  width: 1.36rem;
  font-size: 0.28rem;
  color: #ffffff;
}
</style>
