<template>
  <!-- 门诊住院 -->
  <div class="routerList">
    <div>
      <router-link to="" class="listOne">
        <div class="listImg">
          <img src="../assets/icon/menzhen01.png" alt="" />
        </div>
        <div class="listText"><span>来文单位:东莞市桥头医院</span></div>
        <div class="listImgage"></div>
      </router-link>
    </div>

    <div>
      <router-link to="" class="listTwo">
        <div class="listImg">
          <img src="../assets/icon/zhuyuan01.png" alt="" />
        </div>
        <div class="listText"><span>来文标题:门诊数据</span></div>
        <div class="listImgage"></div>
      </router-link>
    </div>

    <div>
      <router-link to="" class="listThree">
        <div class="listImg">
          <img src="../assets/icon/gerenxinxi.png" alt="" />
        </div>
        <div class="listText">
          <!-- <span>来文编号:{{ sendMessage }}</span> -->
          <span
            >来文编号:2023.04.25 门急诊人次: 1383 发热门诊:7 急诊人次:223入院:45
            出院:54 在院:360 ICU：8</span
          >
        </div>
        <div class="listImgage"></div>
      </router-link>
    </div>
    <div></div>
  </div>
</template>

<script>
import { Group, Cell, XButton, Confirm } from "vux";
import apiUrls from "../config/apiUrls";
import { toolsUtils, storage, ajax } from "../common";
export default {
  name: "index",
  components: {
    // HeaderBar
    Group,
    Cell,
    XButton,
    Confirm
  },
  data() {
    return {
      sendMessage: ""
    };
  },
  methods: {
    defaultData() {
      //用户
      ajax.get(apiUrls.GetWxMeassage).then(r => {
        r = r.data;
        if (r.success) {
          debugger;
          this.sendMessage = r.returnMsg;
          console.log(this.sendMessage);
          return;
        }
      });
    }
  },

  created() {
    this.defaultData();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.userCenter-page {
  height: 100%;
  width: 100%;
  background: #f0eff6;
  display: flex;
  flex-flow: column;
}
.head-logo {
  display: flex;
  width: 100%;
  height: 3.92rem;
  color: #fff;
  align-items: center;
  border-bottom: 1px solid #fff;
  background-image: url("../assets/icon/bg01.png");
  background-repeat: no-repeat;
  background-size: cover;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.head-logo img {
  display: block;
  width: 1.6rem;
  height: 1.6rem;
  margin: 0 auto 0.2rem;
}
.head-logo span {
  font-size: 0.4rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  /* text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5); */
  text-shadow: 1px -2px 1px rgba(0, 0, 0, 0.9);
  text-align: center;
}
.routerList {
  width: 100%;
  background: #ffffff;
  font-size: 0.32rem;
  margin-top: 0.36rem;
}
.listOne {
  width: 100%;
  height: 1.16rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.listTwo {
  margin-top: 1px;
  width: 100%;
  height: 1.16rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f0eff6;
}
.listThree {
  margin-top: 1px;
  width: 100%;
  height: 2.16rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f0eff6;
}
.listImg {
  height: 1.16rem;
  width: 11%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.listText {
  height: 1.16rem;
  width: 78%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.listText span {
  margin-left: 0.2rem;
  color: #354052;
}
.out-account {
  color: #354052;
  font-size: 0.32rem;
}
.listImgage {
  height: 1.16rem;
  width: 11%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.GoHome {
  width: 100%;
  height: 1.16rem;
  background: #2dc4c2;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
}
.GoHomeImg {
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 2rem;
  height: 1.16rem;
}
.GoHomeImg img {
  width: 0.4rem;
  height: 0.4rem;
}
</style>
