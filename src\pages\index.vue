<template>
  <!--首页-->
  <div class="index-page">
    <van-swipe class="swipe" :autoplay="3000" height="180">
      <van-swipe-item v-for="(image, index) in images" :key="index">
        <img class="swipe-img" :src="image.img" />
      </van-swipe-item>
    </van-swipe>
    <div class="page-user">
      <div class="user-info">
        <div class="user-text">
          <span class="user-name">您好，{{ patcard.patName }}</span>
          <div class="change-patients">
            <span @click="showPopup()">切换就诊人</span>
            <img src="../assets/icon/changePatients.png" alt="" />
          </div>
        </div>
        <div>卡号：{{ patcard.patCode }}</div>
      </div>
      <div class="user-code">
        <router-link to="/cardListV2?type=1&method=user">
          <img src="../assets/icon/qrcode.png" alt="" />
        </router-link>
      </div>
    </div>
    <div class="page-main">
      <div
        class="main-item"
        v-for="(item, index) in mainList"
        :key="index"
        @click="onLink(item)"
      >
        <img class="item-img" :src="item.img" alt="" />
        <div class="item-title">{{ item.title }}</div>
        <div class="item-text">{{ item.text }}</div>
      </div>
    </div>
    <div class="page-common">
      <div class="common-title">常用服务</div>
      <div class="common-list">
        <div
          class="common-item"
          v-for="(item, index) in commonList"
          :key="index"
          @click="onLink(item)"
        >
          <img class="common-img" :src="item.img" alt="" />
          <div>{{ item.title }}</div>
        </div>
      </div>
    </div>
    <div v-transfer-dom>
      <x-dialog v-model="showHideOnBlur" class="dialog-demo" hide-on-blur>
        <div class="img-box">
          <div class="popup-wrap">
            <h3>家人姓名</h3>
            <div class="popup-content">
              <div class="patient-list">
                <div class="left-solid">
                  <div></div>
                </div>
                <ul>
                  <li
                    v-for="(list, index) in userList"
                    :key="index"
                    @click="changeUser(list.patCode)"
                  >
                    <input
                      type="radio"
                      :id="setId(index)"
                      name="patient"
                      :checked="list.patCode == indexCard ? 'checked' : ''"
                    />
                    <label :for="setId(index)">{{ list.patName }}</label>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </x-dialog>
    </div>
  </div>
</template>

<script>
import { ajax } from "../common/ajax";
import { storage, toolsUtils } from "../common";
import apiUrls from "../config/apiUrls";
import { XDialog, TransferDomDirective as TransferDom } from "vux";
export default {
  name: "index",
  directives: {
    TransferDom
  },
  components: {
    XDialog
  },
  props: {},
  data() {
    return {
      title: "header",
      IsBack: false,
      showHideOnBlur: false,
      paymentMoney: 0,
      //切换账号的列表
      userList: [],
      UserHisOrder: [],
      indexCard: "",
      account: {
        accountCode: "",
        name: "",
        patcardNo: ""
      },
      //卡信息
      patcard: {},
      // 授权显示
      Unauthorized: true,
      switchS: true,
      images: [
        { img: require("../assets/banner01.png") },
        { img: require("../assets/banner02.png") }
      ],
      mainList: [
        {
          img: require("../assets/icon/健康码.png"),
          title: "健康卡",
          text: "新建或查询健康卡详情",
          path: "/cardListV2?type=1&method=user"
        },
        {
          img: require("../assets/icon/bookingRegister.png"),
          title: "预约挂号",
          text: "实时查看医生情况",
          path: "/choiceDepartment?method=1"
        },
        {
          img: require("../assets/icon/处方缴费.png"),
          title: "处方缴费",
          text: "快捷缴费无需等待",
          path: "/cfLists"
        }
      ],
      commonList: [
        {
          img: require("../assets/icon/myInformation.png"),
          title: "我的信息",
          path: "/userCenter"
        },
        {
          img: require("../assets/icon/paymentRecord.png"),
          title: "缴费记录",
          path: "/cfregisterRecord"
        },
        {
          img: require("../assets/icon/registrationRecord.png"),
          title: "挂号记录",
          path: "/registerRecord"
        },
        {
          img: require("../assets/icon/reportQuery.png"),
          title: "报告查询",
          path: "/seeReport"
        },  
        {
          img: require("../assets/icon/medicalInsuranceCoopetition.png"),
          title: "医保共济",
          path: "/medicalInsurance"
        },
        {
          img: require("../assets/icon/体检服务.png"),
          title: "体检服务",
          path: "http://qtyywx-tj.qtyy.com/#/"
        },
        {
          img: require("../assets/icon/住院服务.png"),
          title: "住院清单",
          path: "/homepage"
        },
        // {
        //   img: require("../assets/icon/postConvenience.png"),
        //   title: "便民邮寄",
        //   path: ""
        // }
      ]
    };
  },
  created() {
    this.look();
  },
  mounted() {
    this.testgetAccountInfo();
  },
  methods: {
    //获取用户信息
    testgetAccountInfo() {
      this.account = JSON.parse(storage.session.get("user")) || {};
      this.patcard = JSON.parse(storage.session.get("patcard")) || {};
      // console.log(this.patcard)
      this.indexCard = this.patcard.patCode;
      var data = {
        type: "1",
        id_card: this.account.idCard,
        openid: this.account.accountCode
      };
      // console.log(data);
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.userList = r.returnData;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    //显示弹窗
    showPopup() {
      var userInfo = JSON.parse(storage.session.get("user"));
      storage.session.set("redirect", "/OutpatientPage");
      if (userInfo == null || userInfo == undefined) {
        this.$router.push({
          path: "/oauth",
          query: { type: "jump" }
        });
      } else {
        if (this.userList.length == 0) {
          toolsUtils.alert("你还没绑定诊疗卡");
          this.$router.push({
            path: "/userCenter"
          });
          return;
        }
        this.showHideOnBlur = true;
      }
    },
    // 页面加载读缓存
    look() {
      this.patcard = JSON.parse(storage.session.get("patcard"));
      if (this.patcard.patCode != null && this.patcard.patCode != undefined) {
        this.Unauthorized = false;
        this.switchS = false;
      }
    },
    setId(index) {
      return "radio_" + index;
    },
    //切换账号
    changeUser(patcardNo) {
      var data = {
        patcardNo,
        id_card: this.account.idCard
      };
      ajax
        .post(apiUrls.GetpatFamilyInfo, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          storage.session.set("patcard", JSON.stringify(r.returnData));
          this.$emit("changeParent", "");
          location.reload();
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    // 跳转页面
    onLink(item) {
      if (item.title === "体检服务") {
        window.location.href = item.path;
        return;
      }
      this.$router.push({
        path: item.path
      });
    }
  }
};
</script>

<style lang="less" scoped>
.index-page {
  color: #333333;
  .swipe {
    .swipe-img {
      width: 100%;
      height: 100%;
    }
  }

  .page-user {
    position: relative;
    background: #fff;
    padding: 0.35rem 0.27rem;
    font-size: 0.3rem;
    margin: 0 0.19rem;
    box-shadow: 0 0.04rem 0.23rem 0 rgba(45, 142, 141, 0.14);
    border-radius: 0.15rem;
    margin-top: -1.27rem;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .user-info {
      display: grid;
      gap: 0.19rem;
      .user-text {
        display: flex;
        align-items: center;
        gap: 0.42rem;
        .user-name {
          font-weight: 600;
        }
        .change-patients {
          display: flex;
          align-items: center;
          gap: 0.04rem;
          background: #4ebab6;
          border-radius: 0.23rem;
          font-size: 0.23rem;
          color: #ffffff;
          padding: 0.05rem 0.15rem;
          img {
            width: 0.3rem;
          }
        }
      }
    }
    .user-code {
      width: 1.15rem;
      height: 1.15rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .page-main {
    position: relative;
    z-index: 1;
    background: #ffffff;
    border-radius: 0.27rem 0.27rem 0 0;
    margin-top: -0.92rem;
    margin-bottom: 0.19rem;
    padding: 0.92rem 0.38rem 0.38rem 0.38rem;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 1fr;
    gap: 0.83rem;
    .main-item {
      font-size: 0.38rem;
      margin-top: 0.38rem;
      text-align: center;
      .item-img {
        width: 0.92rem;
      }
      .item-title {
        font-weight: 600;
      }
      .item-text {
        font-size: 0.27rem;
        color: #80808d;
      }
    }
  }

  .page-common {
    background: #ffffff;
    padding: 0.19rem;
    .common-title {
      font-size: 0.38rem;
      font-weight: 600;
      &::before {
        content: "";
        display: inline-block;
        width: 0.12rem;
        height: 0.27rem;
        background: #4ebab6;
        border-radius: 0.02rem;
        margin-right: 0.12rem;
      }
    }
    .common-list {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: 1fr 1fr;
      gap: 0.35rem;
      margin: 0.2rem 0;
      .common-item {
        font-size: 0.3rem;
        font-weight: 600;
        margin: 0.2rem 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.19rem;
        .common-img {
          width: 0.62rem;
        }
      }
    }
  }
}
/* 弹窗内容的样式 */
.popup-wrap {
  width: 100%;
  background: #fff;
  box-shadow: 1px 1px 4px 0 rgba(53, 64, 82, 0.7);
  border-radius: 2px;
}

.popup-wrap h3 {
  height: 1.12rem;
  width: 100%;
  font-size: 0.36rem;
  color: #354052;
  text-align: center;
  line-height: 1.12rem;
  font-weight: normal;
  border-bottom: 1px solid #dfe3e9;
}

.popup-content {
  height: 4.48rem;
  width: 100%;
  overflow: auto;
}

.patient-list {
  position: relative;
}

.patient-list:after {
  display: block;
  content: "";
  clear: both;
}

.left-solid {
  width: 1.74rem;
  height: 100%;
  padding: 0.6rem 0;
  position: absolute;
  top: 0;
}

.left-solid div {
  margin: 0 auto;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #24bab8;
}

.patient-list ul {
  display: block;
  padding-left: 1.74rem;
}

.patient-list ul li {
  height: 1.12rem;
  width: 100%;
  line-height: 1.12rem;
  border-bottom: 1px solid #dfe3e9;
}

.patient-list ul li label {
  display: block;
  height: 100%;
  width: 100%;
  font-size: 0.32rem;
  color: #7f8fa4;
  padding-left: 1rem;
  position: relative;
  text-align: left;
}

.patient-list ul li input {
  display: none;
}

.patient-list ul li label:after {
  display: block;
  content: "";
  height: 10px;
  width: 10px;
  border-radius: 100%;
  background: #24bab8;
  position: absolute;
  top: 50%;
  margin-top: -5px;
  left: -0.96rem;
  z-index: 2;
}

.patient-list ul li input:checked ~ label:after {
  transform: scale(2);
  transition: transform 300ms ease-out;
}
</style>
