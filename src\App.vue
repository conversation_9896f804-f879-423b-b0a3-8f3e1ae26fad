<template>
  <div id="app">
    <transition :name="transitionName">
      <router-view></router-view>
    </transition>
    
  </div>
</template>

<script>
export default {
  name: 'app',
  data(){
    return{
      transitionName:""
    }
  },
  created: function() {
    const that = this;
    var SizeFun=function(){
      document.getElementsByTagName("html")[0].style.fontSize =
      document.getElementsByTagName("html")[0].offsetWidth / 7.5 + "px";
    }
    SizeFun();
    window.onresize = function() {
      return (function() {
        if (document.documentElement.clientWidth <= 750) {
          SizeFun();
        }

        //					window.screenWidth = document.body.clientWidth
        //					that.screenWidth = window.screenWidth
      })();
    };
  },
  mounted(){
  },
  watch:{
    $route(to,from){
      if(to.meta.index>from.meta.index){
        this.transitionName="slide-left"
      }else{
        this.transitionName="slide-right"
      }
    }
  }
}
</script>

<style lang="less">
@import '~vux/src/styles/reset.less';
@import '~vux/src/styles/1px.less';
@import '~vux/src/styles/close.less';
*{
  box-sizing: border-box;
  list-style: none;
  word-break: break-all;
  font-family: "黑体","Microsoft Yahei",helvetica,arial;
}
button:disabled
{
background: #cccccc !important;
}
html,body {
  background-color: #fbf9fe;
  height:100%;
  width:100%;
}
#app{
  height:100%;
  -webkit-overflow-scrolling: touch;
}
.weui-toast{
  width: 40%;
  min-height: 35vw;
  top: 28%;
  margin-left: 0;
  font-size: 0.4em;
  // border-radius: 5px;
}
.weui-dialog
{
  font-size: 0.3rem;
  width:80%;
  max-width: inherit;
}
.weui-dialog__btn_primary{
  color:#333;
}
/*修改轮播图样式*/
.vux-indicator-right{
    right:50% !important;
    transform: translateX(50%);
    bottom:1.12rem !important;
}
.vux-indicator-right a{
  height:20px;
  overflow: hidden;
  line-height: 20px;
}

.weui-cells{
  margin-top:0 !important;
}
.cell-group .weui-cells::before{
  border:none;
}
.cell-group .weui-cells::after{
  border:none;
}
/*定义页面tab切换的动画效果*/
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform;
  transition: all 400ms ease-out;
  position: absolute;
}
.slide-right-enter {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}
.slide-right-leave-active {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}
.slide-left-enter {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}
.slide-left-leave-active {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}

// 改变vux的箭头方向
.cf-list .info h3 .weui-cell__ft{
  transform: rotate(90deg);
  margin-left: 5px;
}
.cf-list .info h3 .weui-cells::after,
.cf-list .info h3 .weui-cells::before{
  height:0 !important;
  border:none;
}
.cf-list .info h3 .weui-cell{
  padding: 0;
}
.cf-list .info h3 .weui-cell_hd{
  display: none;
}
.cf-list .info h3 .vux-cell-primary{
  flex:inherit;
}
.cf-list .info h3 .vux-cell-value{
  font-size: 0.32rem;
  color:#1a1a1a;
}
// 修改element的日历组件样式
.el-date-range-picker{
  width:100%;
  height:100%;
  overflow: auto;
}
.el-picker-panel__body{
  display: flex;
  flex-direction: column;
  min-width: auto !important;
}
.el-date-range-picker__content{
  flex:1;
  width:100%;
}
.el-date-table{
  font-size: 16px;
}
.el-picker-panel__icon-btn{
  font-size: 18px;
}
.el-icon-d-arrow-left,
.el-icon-d-arrow-right{
  display: none;
}
.el-range-separator{
  width:24px !important;
}
</style>
