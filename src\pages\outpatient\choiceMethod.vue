<template>
  <!--支付成功后的取消订单和支付成功的页面-->
  <div class="choice-method">
    <p>
      <img
        src="../../assets/notice.png"
        alt=""
      />预约挂号产生的费用将和处方订单一并结算
    </p>
    <div class="section-wrap">
      <section>
        <!-- <h3>请选择挂号方式</h3> -->
        <group class="cell-group">
          <!-- <cell title="当天挂号" is-link class="cell"  @click.native="checkMethod(0)">
                     <img slot="icon" class="pay-methodImg" src="../../assets/Tbespeak.png">
                  </cell> -->
          <cell
            title="预约挂号"
            is-link
            class="cell"
            @click.native="checkMethod(1)"
          >
            <img
              slot="icon"
              class="pay-methodImg"
              src="../../assets/bespeak.png"
            />
          </cell>
          <cell
            title="当天挂号"
            is-link
            class="cell"
            @click.native="checkMethod(0)"
          >
            <img
              slot="icon"
              class="pay-methodImg"
              src="../../assets/bespeak.png"
            />
          </cell>
        </group>
      </section>
      <section>
        <h3 class="prompt">
          <img src="../../assets/reminder.png" alt="" />温馨提示
        </h3>
        <div class="prompt-content">
          东莞市桥头医院温馨提醒您:预约挂号时请注意选择就诊时间，就诊当天不可取消当天的预约订单。（如预约我院外聘专家号，所收取的诊查费为特需服务费，50元/次，该项目费用不纳入医保报销范畴，需患者自费支付。）预约就诊当天请您到医院的门诊大楼进行就诊，如有疑惑可致电：<a
            href="tel:0769-81037699"
            >0769-81037699</a
          >。您的健康就是我们最大的追求!
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
export default {
  name: "index",
  components: {
    Cell,
    Group
  },
  data() {
    return {
      title: "选择科室"
    };
  },
  methods: {
    checkMethod(index) {
      // alert("微信正在修复中...");return;
      this.$router.push({
        path: "/choiceDepartment",
        query: {
          method: index
        }
      });
    }
  },
  computed: {},
  created() {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.choice-method {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
}
.choice-method > p {
  height: 32px;
  background: #fd7070;
  color: #fff;
  font-size: 14px;
  padding: 0 0.24rem;
  line-height: 32px;
}
.choice-method > p img {
  display: inline-block;
  vertical-align: middle;
  margin-top: -3px;
  margin-right: 5px;
  height: 20px;
}
.section-wrap {
  padding: 0.24rem;
}
.section-wrap section {
  border-radius: 5px;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.15),
    0 2px 6px 0 rgba(36, 186, 184, 0.2), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  margin-bottom: 0.28rem;
  background: #fff;
  overflow: hidden;
}
.section-wrap section h3 {
  height: 0.96rem;
  font-size: 0.32rem;
  color: #24bab8;
  text-align: center;
  background: #fdfbfb;
  font-weight: normal;
  line-height: 0.96rem;
}
.section-wrap section .prompt {
  color: #f6912c;
}
.section-wrap section h3 img {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.06rem;
  margin-right: 0.12rem;
}
.cell-group .cell {
  height: 0.96rem;
  font-size: 0.32rem;
  color: #354052;
}
.cell-group .cell img {
  height: 0.4rem;
  display: block;
  margin-right: 0.2rem;
}
.section-wrap section .prompt-content {
  padding: 0.28rem;
  text-indent: 1.6em;
  font-size: 0.28rem;
  color: #9b9b9b;
}
</style>
