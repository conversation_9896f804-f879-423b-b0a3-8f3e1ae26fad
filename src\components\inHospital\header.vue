<template>
  <header class="header">
    <div class="man-img"><img src="../../assets/user_face.png" alt="" /></div>
    <div class="man-information">
      <div class="man-name">
        {{ account.name }}<span @click="showPopup()">更换账号</span>
      </div>
      <div class="car-num">
        {{ admissionCard.name }} {{ admissionCard.admissionNo }}
      </div>
    </div>
    <!-- <div class="charge-btn">
			<img src="../../assets/chargebtn_1x.png" alt="" @click="chargeBtn()"> -->
    <!-- <p>￥<span>1000</span></p> -->

    <!-- </div> -->
    <!-- 弹窗 -->
    <div v-transfer-dom>
      <x-dialog v-model="showHideOnBlur" class="dialog-demo" hide-on-blur>
        <div class="img-box">
          <div class="popup-wrap">
            <h3>住院人姓名</h3>
            <div class="popup-content">
              <div class="patient-list">
                <div class="left-solid">
                  <div></div>
                </div>
                <ul>
                  <li
                    v-for="(list, index) in userList"
                    :key="index"
                    @click="changeUser(list.admissionNo)"
                  >
                    <input
                      type="radio"
                      :id="setId(index)"
                      name="patient"
                      :checked="list.admissionNo == indexCard ? 'checked' : ''"
                    />
                    <label :for="setId(index)">{{ list.name }}</label>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </x-dialog>
    </div>
  </header>
</template>

<script>
import { ajax } from "../../common/ajax";
import { storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import {
  XDialog,
  XButton,
  Group,
  XSwitch,
  TransferDomDirective as TransferDom
} from "vux";
export default {
  name: "index",
  directives: {
    TransferDom
  },
  components: {
    XDialog
  },
  props: {},
  data() {
    return {
      title: "header",
      IsBack: false,
      swtitle: "121fggf",
      showHideOnBlur: false,
      paymentMoney: 0,
      //切换账号的列表
      userList: [],
      indexCard: "",
      account: {
        accountCode: "",
        name: "",
        admissionCardNo: "",
        patCardNo: ""
      },
      //卡信息
      admissionCard: {}
    };
  },
  mounted: function() {
    this.testgetAccountInfo();
  },
  methods: {
    //获取用户信息
    testgetAccountInfo() {
      this.account = JSON.parse(storage.session.get("user")) || {};
      this.admissionCard =
        JSON.parse(storage.session.get("admissionCard")) || {};

      this.indexCard = this.admissionCard.admissionNo;
      var data = {
        type: "2",
        id_card: this.account.idCard
      };
      // console.log(data);
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.userList = r.returnData;
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    //显示弹窗
    showPopup() {
      var userinfo = JSON.parse(storage.session.get("user"));
      storage.session.set("redirect", "/Hospitalization");
      if (userinfo == null || userinfo == undefined) {
        this.$router.push({
          path: "/oauth",
          query: { type: "jump" }
        });
      } else {
        if (this.userList.length == 0) {
          toolsUtils.alert("您还没有绑定住院卡");
          this.$router.push({
            path: "/userCenter"
          });
          return;
        }
        this.showHideOnBlur = true;
      }
    },
    //获取待缴费金额
    getPaymentMoney(val) {
      this.paymentMoney = val;
    },
    chargeBtn() {
      this.$router.push({
        path: "/charge",
        query: { paymentMoney: this.paymentMoney }
      });
    },
    setId(index) {
      return "radio_" + index;
    },
    //切换账号
    changeUser(admissionCardNo) {
      var data = {
        admissionCardNo
      };
      ajax
        .post(apiUrls.GetadmissionFamilyInfo, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          storage.session.set("admissionCard", JSON.stringify(r.returnData[0]));
          this.$emit("changeParent", "");
          location.reload();
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.header {
  display: flex;
  height: 2.4rem;
  background: #22b8b6;
  color: #fff;
  padding: 0 0.24rem;
  align-items: center;
  border-bottom: 1px solid #fff;
}

.man-img {
  height: 1.28rem;
  width: 1.28rem;
  border: 2px solid #fff;
  border-radius: 100%;
  margin-right: 0.24rem;
  background: #fff;
  overflow: hidden;
}
.man-img img {
  display: block;
  width: 100%;
}
.man-information {
  flex-grow: 1;
  flex-basis: 0;
  font-size: 0.36rem;
}

.charge-btn {
  font-size: 0.32rem;
  margin-top: 0.25rem;
}

.charge-btn img {
  width: 1.24rem;
  display: block;
}

.charge-btn p {
  color: #f1efb3;
}

.man-information .man-name span {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  display: inline-block;
  height: 0.48rem;
  width: 1.24rem;
  text-align: center;
  line-height: 0.48rem;
  font-size: 0.24rem;
  margin-left: 0.2rem;
}

.man-information .car-num {
  font-size: 0.32rem;
}
/* 弹窗内容的样式 */
.popup-wrap {
  width: 100%;
  background: #fff;
  box-shadow: 1px 1px 4px 0 rgba(53, 64, 82, 0.7);
  border-radius: 2px;
}

.popup-wrap h3 {
  height: 1.12rem;
  width: 100%;
  font-size: 0.36rem;
  color: #354052;
  text-align: center;
  line-height: 1.12rem;
  font-weight: normal;
  border-bottom: 1px solid #dfe3e9;
}

.popup-content {
  height: 4.48rem;
  width: 100%;
  overflow: auto;
}

.patient-list {
  position: relative;
}

.patient-list:after {
  display: block;
  content: "";
  clear: both;
}

.left-solid {
  width: 1.74rem;
  height: 100%;
  padding: 0.6rem 0;
  position: absolute;
  top: 0;
}

.left-solid div {
  margin: 0 auto;
  width: 1px;
  height: 100%;
  border-left: 1px dashed #24bab8;
}

.patient-list ul {
  display: block;
  padding-left: 1.74rem;
}

.patient-list ul li {
  height: 1.12rem;
  width: 100%;
  line-height: 1.12rem;
  border-bottom: 1px solid #dfe3e9;
}

.patient-list ul li label {
  display: block;
  height: 100%;
  width: 100%;
  font-size: 0.32rem;
  color: #7f8fa4;
  padding-left: 1rem;
  position: relative;
  text-align: left;
}

.patient-list ul li input {
  display: none;
}

.patient-list ul li label:after {
  display: block;
  content: "";
  height: 10px;
  width: 10px;
  border-radius: 100%;
  background: #24bab8;
  position: absolute;
  top: 50%;
  margin-top: -5px;
  left: -0.96rem;
  z-index: 2;
}

.patient-list ul li input:checked ~ label:after {
  transform: scale(2);
  transition: transform 300ms ease-out;
}
</style>
