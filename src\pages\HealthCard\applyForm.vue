<template>
  <div class="apply">
    <van-cell-group>
      <van-field v-model="name" label="姓名" placeholder="填写持卡人的姓名" />
      <van-field
        readonly
        clickable
        is-link
        arrow-direction="down"
        label="证件类型"
        :value="idTypeName"
        placeholder="请选择证件类型"
        @click="showPickeridType = true"
      />
      <van-popup v-model="showPickeridType" position="bottom">
        <van-picker
          show-toolbar
          :columns="useridTypeName"
          @cancel="showPickeridType = false"
          @confirm="onConfirmofidtype"
        />
      </van-popup>
      <van-field
        v-model="idCard"
        label="证件号码"
        placeholder="填写证件号码"
        @input="getIdCard"
      />
      <van-field
        readonly
        clickable
        is-link
        arrow-direction="down"
        label="职业类型"
        :value="OccupationTypeName"
        placeholder="请选择职业类型"
        @click="showPickerOccupationType = true"
      />
      <van-popup v-model="showPickerOccupationType" position="bottom">
        <van-picker
          show-toolbar
          :columns="userOccupationName"
          @cancel="showPickerOccupationType = false"
          @confirm="onConfirmofOccupationtype"
        />
      </van-popup>
      <!-- <van-field v-model="patCode" label="院内门诊卡号" placeholder="没有可以不填" /> -->
      <van-field
        readonly
        clickable
        is-link
        arrow-direction="down"
        label="出生日期"
        :value="birthday"
        placeholder="请选择您的生日"
        @click="showPickerbirthday = true"
      />
      <van-popup v-model="showPickerbirthday" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择年月日"
          :min-date="minDate"
          :max-date="maxDate"
          :formatter="formatter"
          @cancel="showPickerbirthday = false"
          @confirm="onConfirmbirthday"
        />
      </van-popup>

      <van-field
        readonly
        clickable
        is-link
        arrow-direction="down"
        label="性别"
        :value="gender"
        placeholder="请选择性别"
        @click="showPickerSex = true"
      />
      <van-popup v-model="showPickerSex" position="bottom">
        <van-picker
          show-toolbar
          :columns="Sex"
          @cancel="showPickerSex = false"
          @confirm="onConfirmofSex"
        />
      </van-popup>
      <!-- <van-field
        readonly
        clickable
        label="民族"
        :value="nationValue"
        placeholder="选择民族"
        @click="showPicker = true"
      /> -->
      <!-- <van-popup v-model="showPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="columns"
          @cancel="showPicker = false"
          @confirm="onConfirm"
        />
      </van-popup> -->
    </van-cell-group>
    <van-cell-group class="phone-number">
      <van-field
        readonly
        clickable
        is-link
        arrow-direction="down"
        label="家人关系"
        :value="nationValue"
        placeholder="默认为自己"
        @click="showPicker = true"
      />
      <van-popup v-model="showPicker" position="bottom">
        <van-picker
          show-toolbar
          :columns="userRelationship"
          @cancel="showPicker = false"
          @confirm="onConfirm"
        />
      </van-popup>
      <van-field
        v-model="phoneNumber"
        name="validator"
        placeholder="请输入手机号"
        label="手机号"
        :rules="[{ validator: validatorA, message: '请输入手机号' }]"
      />
      <van-field
        v-model="message"
        rows="1"
        autosize
        label="详细地址"
        type="textarea"
        placeholder="请输入详细地址"
      />

      <!-- 通过 validator 进行异步函数校验 -->
      <!-- <van-field
        v-model="codeNumber"
        name="asyncValidator"
        center
        clearable
        label="短信验证码"
        placeholder="请输入短信验证码"
        :rules="[{ validator: asyncValidator, message: '请输入正确验证码' }]"
        class="codePadding"
      >
        <template #button>
          <van-button
            size="small"
            type="primary"
            native-type="button"
            slot="right-icon"
            :disabled="disableCode"
            @click="getVerifyCode()"
          >{{getCode}}</van-button>
        </template>
      </van-field> -->
    </van-cell-group>
    <div class="buttonSum" @click="LQ()">
      <span>导入本人注册信息</span>
    </div>
    <div class="buttonSum" @click="submitRegister()">
      <span>完成申领</span>
    </div>
  </div>
</template>
<script>
import {
  Cell,
  CellGroup,
  Field,
  Button,
  Popup,
  Picker,
  Toast,
  DatetimePicker
} from "vant";
import "vant/lib/index.css";
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "applyForm",
  components: {
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Toast.name]: Toast,
    [DatetimePicker.name]: DatetimePicker
  },
  data() {
    return {
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(2025, 10, 1),
      currentDate: new Date(),
      birthday: "",
      name: "", //姓名
      idCard: "", //身份证号
      // patCode:"",//门诊卡号
      showPicker: false,
      showPickeridType: false,
      showPickerOccupationType: false,
      showPickerSex: false,
      showPickerbirthday: false,
      message: "", //住址
      phoneNumber: "",
      // codeNumber: "",
      // getCode: "发送验证码",
      count: 60, //倒计时
      disableCode: false,
      userRelationship: ["自己", "父母", "配偶", "子女", "其他"],
      nationValue: "",
      relationship: 0,
      useridTypeName: [
        // "就诊卡",
        "居民身份证",
        "居民户口簿",
        "护照",
        "军官证",
        "驾驶证",
        "港澳居民来往内地通行证",
        "台湾居民来往内地通行证",
        "出生医学证明",
        "医保卡",
        "外国人永久居住证"
      ],
      //idType:["10","01","02","03","04","05","06","07","08","09"],
      idType: ["01", "02", "03", "04", "05", "06", "07", "08", "09", "15"],
      idTypeName: "",
      idTypeValue: "",
      userOccupationName: [
        "国家公务员",
        "专业技术人员",
        "职员",
        "企业管理人员",
        "工人",
        "农民",
        "学生",
        "现役军人",
        "自由职业者",
        "个体经营户",
        "无业人员",
        "退（离）休人员",
        "其他"
      ],
      OccupationType: [
        "11",
        "13",
        "17",
        "21",
        "24",
        "27",
        "31",
        "37",
        "51",
        "54",
        "70",
        "80",
        "90"
      ],
      OccupationTypeName: "",
      OccupationTypeValue: "",
      Sex: ["男", "女"],
      gender: ""
    };
  },
  created() {
    //保存wechatCode
    let wechatCode = this.$route.query.wechatCode;

    if (typeof wechatCode != "undefined") {
      storage.session.set("wechatCode", wechatCode);
    }

    let registerType = storage.session.get("registerType");
    console.log(registerType);
    if (registerType != "healthCode") {
      return;
    }
    //一键注册的方法
    this.yiRegister();
  },
  methods: {
    getidTypeName(idTypeValue) {
      switch (idTypeValue) {
        case "01":
          return "居民身份证";
        case "02":
          return "居民户口簿";
        case "03":
          return "护照";
        case "04":
          return "军官证";
        case "05":
          return "驾驶证";
        case "06":
          return "港澳居民来往内地通行证";
        case "07":
          return "台湾居民来往内地通行证";
        case "08":
          return "出生医学证明";
        case "09":
          return "医保卡";
        case "10":
          return "就诊卡";
        default:
          return null;
      }
    },

    getOccupationTypeName(OccupationTypeValue) {
      switch (OccupationTypeValue) {
        case "11":
          return "国家公务员";
        case "13":
          return "专业技术人员";
        case "17":
          return "职员";
        case "21":
          return "企业管理人员";
        case "24":
          return "工人";
        case "27":
          return "农民";
        case "31":
          return "学生";
        case "37":
          return "现役军人";
        case "51":
          return "自由职业者";
        case "54":
          return "个体经营者";
        case "70":
          return "无业人员";
        case "80":
          return "退（离）休人员";
        case "90":
          return "其他";
        default:
          return null;
      }
    },

    onConfirm(value, index) {
      this.nationValue = value;
      this.relationship = index;
      this.showPicker = false;
    },
    onConfirmofidtype(value, index) {
      this.idTypeName = value;
      this.idTypeValue = this.idType[index];
      this.showPickeridType = false;
    },
    onConfirmofOccupationtype(value, index) {
      this.OccupationTypeName = value;
      this.OccupationTypeValue = this.OccupationType[index];
      this.showPickerOccupationType = false;
    },
    onConfirmofSex(value) {
      this.gender = value;
      // console.log(this.gender);
      this.showPickerSex = false;
    },
    onConfirmbirthday(value) {
      var that = this;
      var date = value;
      var seperator1 = "-";
      var seperator2 = ":";
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      that.currentDate =
        date.getFullYear() + seperator1 + month + seperator1 + strDate;
      that.birthday = that.currentDate;
      that.showPickerbirthday = false;
    },
    formatter(type, val) {
      if (type === "year") {
        return `${val}年`;
      } else if (type === "month") {
        return `${val}月`;
      } else if (type === "day") {
        return `${val}日`;
      }
      return val;
    },
    validator(val) {
      return /^[A-Za-z0-9]+$/.test(val);
    },
    validatorA(val) {
      return 11 && /^((13|14|15|17|18)[0-9]{1}\d{8})$/.test(val);
    },
    // 异步校验函数返回 Promise
    asyncValidator(val) {
      return new Promise(resolve => {
        this.$toast.loading("验证中...");
        this.$toast.clear();
        resolve(/\d{6}/.test(val));
      });
    },
    // 验证码按钮
    // getVerifyCode() {
    //   if (this.validatorA(this.phoneNumber)) {
    //     ajax
    //     .get(apiUrls.GetVerifyCode + "?phoneNumber=" + this.phoneNumber)
    //     .then(r => {
    //       console.log(r);
    //       r = r.data;
    //       if (!r.success) {
    //         toolsUtils.alert(r.returnMsg);
    //         return;
    //       }
    //       toolsUtils.alert(r.returnData);
    //       // this.yzmtime();
    //     });

    //     let countDown = setInterval(() => {
    //       if (this.count < 1) {
    //         this.getCode = "获取验证码";
    //         this.count = 6;
    //         this.disableCode = false;
    //         clearInterval(countDown);
    //       } else {
    //         this.disableCode = true;
    //         this.getCode = this.count-- + "s后重发";
    //       }
    //     }, 1000);
    //   } else {
    //     Toast.fail({
    //       duration: 800,
    //       message: "请输入手机号",
    //     });
    //   }
    // },
    yiRegister() {
      //一键注册
      var that = this;

      //获取healthCode
      let healthCode = that.$route.query.healthCode || "";
      if (healthCode == "") {
        let burl = encodeURIComponent("https://qtyywx.qtyy.com/applyForm");
        window.location.href = `https://health.tengmed.com/open/getHealthCardList?redirect_uri=${burl}&hospitalId=34504`;
      }

      if (healthCode == "0") {
        storage.session.set("registerType", "wechatCode");
        let burl = encodeURIComponent("https://qtyywx.qtyy.com/applyForm");
        window.location.href = `https://health.tengmed.com/open/getUserCode?apiVersion=2&redirect_uri=${burl}`;
        return;
      }

      let hdata = {
        openid: storage.session.get("openid"),
        healthCode: healthCode
      };

      if (hdata.healthCode != "" && hdata.healthCode != "0") {
        console.log(hdata);
        ajax.post(apiUrls.yjregisterHealthCard, hdata).then(r => {
          console.log(r);
          r = r.data;
          console.log(r.returnData);
          // let rsp = r.returnData;
          that.name = r.returnData.name;
          that.idCard = r.returnData.idNumber;
          // that.patCode=r.returnData.patCode;
          that.idTypeValue = r.returnData.idType;
          that.idTypeName = that.getidTypeName(r.returnData.idType);
          // that.OccupationTypeValue=r.returnData.phone2;
          // that.OccupationTypeName=that.getOccupationTypeName(r.returnData.phone2);
          that.nationValue = that.userRelationship[that.relationship];
          that.message = r.returnData.address;
          that.gender = r.returnData.gender;
          that.birthday = r.returnData.birthday;
          that.phoneNumber = r.returnData.phone1;
          getIdCard;
          if (!r.success) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          storage.session.set("registerType", "true"); //修改状态
          setTimeout(function() {
            that.submitRegister();
          }, 800);
        });
      }
    },
    getIdCard() {
      if (this.idTypeName === "居民身份证" && this.idCard.length === 18) {
        this.birthday = this.getBirthday(this.idCard);
        this.gender = this.getSex(this.idCard);
      } else {
        this.birthday = "";
        this.gender = "";
      }
    },
    getBirthday(idCard) {
      let birthdayNo, birthdayTemp;
      if (idCard.length === 18) {
        birthdayNo = idCard.substring(6, 14);
      } else if (idCard.length === 15) {
        birthdayTemp = idCard.substring(6, 12);
        birthdayNo = "19" + birthdayTemp;
      } else {
        return "";
      }
      let birthday = `${birthdayNo.substring(0, 4)}-${birthdayNo.substring(
        4,
        6
      )}-${birthdayNo.substring(6, 8)}`;
      return birthday;
    },
    getSex(idCard) {
      let sexNo, sex;
      if (idCard.length === 18) {
        sexNo = idCard.substring(16, 17);
      } else if (idCard.length === 15) {
        sexNo = idCard.substring(14, 15);
      } else {
        return "";
      }
      sex = sexNo % 2 === 0 ? "女" : "男";
      return sex;
    },
    LQ() {
      this.name = JSON.parse(storage.session.get("user")).name;
      this.idCard = JSON.parse(storage.session.get("user")).idCard;
      this.phoneNumber = JSON.parse(storage.session.get("user")).telePhone;
      if (this.idCard.length == 18) {
        this.idTypeValue = "01";
        this.idTypeName = "居民身份证";
        this.getIdCard();
      }
    },
    submitRegister() {
      //#region 验证输入是否合法
      if (this.name == "") {
        toolsUtils.alert("请输入您的姓名");
        return;
      }
      // if(dataUtils.isCardID(this.idCard)!=true){ toolsUtils.alert(dataUtils.isCardID(this.idCard));return }
      if (this.birthday == "") {
        toolsUtils.alert("请选择你的出生日期");
        return;
      }
      // if(this.relationship==""){ alert("请选择家人关系");return }
      if (this.idTypeValue == "") {
        toolsUtils.alert("请选择证件类型");
        return;
      }
      if (this.OccupationTypeValue == "") {
        toolsUtils.alert("请选择职业类型");
        return;
      }
      if (this.gender == "") {
        toolsUtils.alert("请选择您的性别");
        return;
      }
      // if(this.patCode==""){ toolsUtils.alert("请输入您的门诊卡号");return }
      if (this.message == "") {
        toolsUtils.alert("请输入您的住址");
        return;
      }
      if (this.phoneNumber == "") {
        toolsUtils.alert("请输入您的电话");
        return;
      }
      if (dataUtils.isTel(this.phoneNumber) != true) {
        toolsUtils.alert("您的手机号有误");
        return;
      }
      if (this.codeNumber == "") {
        toolsUtils.alert("请输入手机验证码");
        return;
      }
      //#endregion
      var that = this;
      let data = {
        openid: storage.session.get("openid"),
        wechatCode: storage.session.get("wechatCode"),
        // nationValue: that.nationValue,
        // healthCode: this.$route.query.healthCode||'',
        name: that.name, //姓名
        idNumber: that.idCard.toUpperCase(), //身份证号
        // patCode:that.patCode,//门诊卡号
        idType: that.idTypeValue,
        OccupationType: that.OccupationTypeValue,
        //showPicker: false,
        relationship: that.relationship,
        address: that.message, //住址
        gender: that.gender,
        birthday: this.birthday,
        phone1: that.phoneNumber, //手机号
        codeNumber: "888888", //短信验证码
        myidCard: JSON.parse(storage.session.get("user")).idCard
      };
      console.log(data);

      ajax
        .post(apiUrls.registerHealthCard, data)
        .then(r => {
          if (!r.data.success) {
            if (
              r.data.returnMsg == "ESOCKETTIMEDOUT" ||
              r.data.returnMsg == "OpenCircuitError"
            ) {
              toolsUtils.alert("系统繁忙，请稍后再试。");
              return;
            }
            toolsUtils.alert(r.data.returnMsg);
            return;
          }

          let info = JSON.parse(storage.session.get("user"));
          this.$router.replace({
            path: "/cardListV2",
            query: {
              type: "1",
              method: storage.session.get("method")
            }
          });
        })
        .catch(e => {
          console.log(e);
        });
    }
  }
};
</script>
<style scoped>
.apply .van-cell {
  border-bottom: 1px solid #dfe3e9;
}
.apply .phone-number {
  margin-top: 0.2rem;
}

.apply .buttonSum {
  width: 90%;
  height: 1.08rem;
  border-radius: 8px;
  margin: 0.68rem auto 0;
  font-size: 0.36rem;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: linear-gradient(to right, #3dc0f3, #2dc4c2);
  box-shadow: 1px 1px 2px #52675a;
}
</style>
