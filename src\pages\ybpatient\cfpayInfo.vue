<template>
  <div class="cfpayInfo_page">
    <header>
      <h1>
        <span>付款给</span>
        <p>东莞市桥头医院</p>
      </h1>
      <img class="login_img" src="../../assets/hospLogo.png" />
    </header>
    <div class="content">
      <div class="cost_info">
        <h3>
          <span>费用总额</span>
          <span>{{ GetYBinfoModel.totalPayAmount }}元</span>
        </h3>
        <ul>
          <li>
            <label>医保基金支付</label>
            <span>{{ GetYBinfoModel.recPayAmount }}元</span>
          </li>
          <li>
            <label>个人账户支付</label>
            <span>{{ GetYBinfoModel.payAmout }}元</span>
          </li>
          <!-- <li>
            <label>其他抵抗金额</label>
            <span>0.00元</span>
          </li> -->
          <li>
            <label>现金支付</label>
            <span>{{ GetYBinfoModel.ownPayAmt }}元</span>
          </li>
        </ul>
        <!-- <div class="look_btn" @click="showDetail(true)">
          查看明细
        </div> -->
      </div>
      <div class="payMode_wrap">
        <div class="pay_title">个人账号支付</div>
        <div class="payMode_btn">
          <span :class="{ span_active: isUse }" @click="useFun(true)"
            >使用</span
          >
          <span :class="{ span_active: !isUse }" @click="useFun(false)"
            >不使用</span
          >
        </div>
      </div>
      <!-- <div class="payMode_wrap">
        <div class="pay_title">支付方式</div>
        <ul>
          <li>
            <img src="../../assets/weixinzhifu.png" alt="" />
            <p>
              <label for="test">微信支付</label>
              <van-radio id="test" v-model="radio" name="1"></van-radio>
            </p>
          </li>
        </ul>
      </div> -->
      <div class="foot_tips">
        <img src="../../assets/hunhezhifu.png" alt="" />
        <span>医保移动支付</span>
      </div>
    </div>
    <footer>
      <p>
        您还需支付：<span>￥{{ GetYBinfoModel.ownPayAmt }}</span>
      </p>
      <div class="payBtn" @click="YbPay()">去支付</div>
    </footer>
    <van-popup
      v-model="popupShow"
      round
      position="bottom"
      style="max-height:80%;"
    >
      <div class="popup_content">
        <h1>
          <span>处方明细</span>
          <van-icon name="close" @click="showDetail(false)" />
        </h1>
        <div class="every_wrap">
          <div class="every_detail">
            <h3>
              <span>
                就诊信息
              </span>
            </h3>
            <ul>
              <li>
                <span>门诊类别</span>
                <span>门（急）诊</span>
              </li>
              <li>
                <span>门诊科室</span>
                <span>普通内科</span>
              </li>
              <li>
                <span>医生姓名</span>
                <span>张三</span>
              </li>
              <li>
                <span>处方时间</span>
                <span>2023/11/21 09:30:00</span>
              </li>
              <li>
                <span>费用总额</span>
                <span style="color:#3b71e8">368.50元</span>
              </li>
            </ul>
          </div>
          <div class="every_detail">
            <h3>
              <span>
                诊断信息
              </span>
            </h3>
            <ul>
              <li>
                <span>诊断名称</span>
                <span>外伤肿胀</span>
              </li>
              <li>
                <span>诊断编号</span>
                <span>E3D.25</span>
              </li>
            </ul>
          </div>
          <div class="every_detail">
            <h3>
              <span>
                特殊信息
              </span>
            </h3>
            <ul>
              <li>
                <span>病情名称</span>
                <span>高血压</span>
              </li>
              <li>
                <span>病情编号</span>
                <span>2220003495858</span>
              </li>
            </ul>
          </div>
          <div class="every_detail ">
            <h3>
              <span>
                费用信息
              </span>
            </h3>
            <ul class="cost_ul">
              <li>
                <span>
                  <i></i>
                  万胫骨贴*1
                  <p>8g/片/3</p>
                </span>
                <span>37.8元</span>
              </li>
              <li>
                <span>
                  <i></i>
                  阿莫西林
                  <p>8g/片/3</p>
                </span>
                <span>17.80元</span>
              </li>
            </ul>
          </div>
          <div class="every_detail">
            <h3>
              <span>
                其他抵扣金额
              </span>
            </h3>
            <ul>
              <li>
                <span>住院押金抵扣</span>
                <span>50.00元</span>
              </li>
              <li>
                <span>医院负担金额抵扣</span>
                <span>50.00元</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 加载页面动画 -->
    <van-overlay :show="loadingShow" class="overlay_mask">
      <img src="../../assets/loading.gif" alt="" class="" />
      <p>加载中</p>
    </van-overlay>
  </div>
</template>

<script>
import { Cell, Group, Clocker } from "vux";
import { ajax, storage, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      radio: "1",
      popupShow: false,
      isUse: true,
      SetYBinfoModel: {},
      GetYBinfoModel: {},
      requestcontent: {
        payAuthNo: "",
        payOrdId: "",
        setlLatlnt: ""
      },
      loadingShow: false
    };
  },
  created() {
    this.SetYBinfoModel = JSON.parse(storage.session.get("SetYBinfoModel")); //入参
    this.GetYBinfoModel = JSON.parse(storage.session.get("GetYBinfoModel")); //返参
    this.requestcontent = JSON.parse(storage.session.get("requestcontent"));
    this.getOrder(); //获取订单信息
  },
  mounted() {
    this.getdate();
    // this.WxConfig();
  },
  methods: {
    showDetail(flag) {
      this.popupShow = flag;
    },
    // 使用和不使用的选中
    useFun(flag) {
      this.loadingShow = true;
      this.isUse = flag;
      var data = {};
      if (this.isUse == false) {
        data = {
          arg0: this.SetYBinfoModel.arg0.slice(0, -1) + "0",
          patCardNo: this.SetYBinfoModel.patCardNo,
          patientidpid: this.SetYBinfoModel.patientidpid,
          visitId: this.SetYBinfoModel.visitId,
          detailId: this.SetYBinfoModel.detailId,
          doctorId: this.SetYBinfoModel.doctorId,
          settleMethodId: this.SetYBinfoModel.settleMethodId,
          payMode: this.SetYBinfoModel.payMode,
          pay_auth_no: this.SetYBinfoModel.pay_auth_no,
          isIndividualAccount: "0"
        };
      } else {
        data = {
          arg0: this.SetYBinfoModel.arg0.slice(0, -1) + "1",
          patCardNo: this.SetYBinfoModel.patCardNo,
          patientidpid: this.SetYBinfoModel.patientidpid,
          visitId: this.SetYBinfoModel.visitId,
          detailId: this.SetYBinfoModel.detailId,
          doctorId: this.SetYBinfoModel.doctorId,
          settleMethodId: this.SetYBinfoModel.settleMethodId,
          payMode: this.SetYBinfoModel.payMode,
          pay_auth_no: this.SetYBinfoModel.pay_auth_no,
          isIndividualAccount: "1"
        };
      }
      // alert("切换使用个账接口入参：" + JSON.stringify(data));
      ajax
        .post(apiUrls.SetYBinfo, data)
        .then(r => {
          r = r.data;
          this.loadingShow = false;
          if (r.success == true) {
            // alert(
            //   "切换使用个账接口返参：      " +
            //     "费用总额:" +
            //     r.returnData.totalPayAmount +
            //     "医保基金支付：" +
            //     r.returnData.recPayAmount +
            //     "个人账户支付:" +
            //     r.returnData.payAmout +
            //     "现金支付:" +
            //     r.returnData.ownPayAmt
            // );
            storage.session.set("GetYBinfoModel", JSON.stringify(r.returnData));
            this.requestcontent.payOrdId = r.returnData.payOrdId;
            storage.session.set(
              "requestcontent",
              JSON.stringify(this.requestcontent)
            );
            this.GetYBinfoModel = JSON.parse(
              storage.session.get("GetYBinfoModel")
            );
          } else {
            alert("调用插入医保接口失败:" + r.returnMsg);
            this.loadingShow = false;
            return;
          }
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
          this.loadingShow = false;
          return;
        });
    },
    //获取订单信息
    getOrder() {
      this.orderid = this.$route.query.orderid;
      this.checkedwx = this.checkimg;
      var pdata = { orderid: this.orderid };
      var that = this;
      console.log(pdata);
      ajax
        .post(apiUrls.GetpatientOrderInfo, pdata)
        .then(r => {
          var data = r.data;
          if (!data.success) {
            toolsUtils.alert(data.returnMsg);
            return;
          }
          this.listDatastr = data.returnData.listDatastr;
          this.payMode = data.returnData.payMode.toUpperCase();
          that.orderinfo = data.returnData;
          var datas = [
            {
              orderid: that.orderinfo.orderNo,
              deptId: that.orderinfo.deptId,
              deptName: that.orderinfo.deptName,
              doctorId: that.orderinfo.doctorId,
              doctorName: that.orderinfo.doctorName,
              patName: that.orderinfo.patName,
              payAmount: that.orderinfo.payAmout,
              visitId: that.orderinfo.visitId,
              visitNo: that.orderinfo.regDate,
              detailId: that.orderinfo.detailId,
              status: that.orderinfo.status
            }
          ];
          storage.session.set("cfList", JSON.stringify(datas));
          this.paymoney = datas[0].payAmount;
          //如果订单状态为6(异常订单) 则显示异常信息
          if (datas[0].status == 6) {
            alert(datas[0].hisMsg);
          }
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    //获取时间
    getdate() {
      if (this.$route.query.orderid != null) {
        this.orderid = this.$route.query.orderid;
        var pdata = { orderid: this.orderid };
        ajax.post(apiUrls.GetpatientOrderInfo, pdata).then(r => {
          var data = r.data;
          if (!data.success) {
            toolsUtils.alert(data.returnMsg);
            return;
          }
          // console.log(data.returnData.timeoutTime);
          var date = data.returnData.timeoutTime;
          date = new Date(date); //获取当前时间后14分钟
          var outtime = dataUtils.changedatetype(date, "YYYY-MM-DD HH:mm:ss"); //将时间转化格式
          this.timeoutdate = outtime;
          return;
        });
      }
    },
    //医保支付
    YbPay() {
      //alert("进入医保支付");
      this.loadingShow = true;
      var user = JSON.parse(storage.session.get("user"));
      var patcard = JSON.parse(storage.session.get("patcard"));
      var cfList = JSON.parse(storage.session.get("cfList"));
      var requestcontentData = storage.session.get("requestcontent");
      var date = new Date();
      function pad2(n) {
        return n < 10 ? "0" + n : n;
      }
      var time =
        date.getFullYear().toString() +
        pad2(date.getMonth() + 1) +
        pad2(date.getDate()) +
        pad2(date.getHours()) +
        pad2(date.getMinutes()) +
        pad2(date.getSeconds());
      var data = {
        order_type: "DiagPay", //RegPay=挂号支付 DiagPay=诊间支付 InHospPay=住院支付
        appid: "wx5342950e076a0d26", //微信分配的公众账号 ID
        mch_id: "**********", //微信支付分配的商户号
        openid: user.accountCode, //openid 和 sub_openid 可以选传其中之一，如果选择传 sub_openid,则必须传 sub_appid。
        hosp_out_trade_no: cfList[0].orderid, //第三方服务商平台自动生成的一个订单号
        hospital_name: "东莞市桥头医院", //医院名称
        total_fee: Math.round(this.GetYBinfoModel.totalPayAmount * 100), //总共需要支付金额
        cash_fee: Math.round(this.GetYBinfoModel.ownPayAmt * 100), //现金需要支付的金额
        allow_fee_change: 1, //是否允许预结算费用发生变化
        spbill_create_ip: "**************", //"**************",//用户端 ip
        notify_url:
          "https://qtyywx-api.qtyy.com/HisNotify/YBregPatPayNotifyUrl", //回调 url  API
        //notify_url: "http://michole.wicp.net/HisNotify/YBregPatPayNotifyUrl",//回调 url  API
        body: "处方缴费", //this.listDatastr,//商品描述
        return_url:
          "https://qtyywx.qtyy.com/ybchargeSuccess?orderid=" +
          cfList[0].orderid, //支付后回跳的页面，不论成功或者失败均会回跳
        pay_type: this.GetYBinfoModel.ownPayAmt == 0 ? 2 : 3, //支付方式
        city_id: "441902",
        consume_type: 0, //医保部分扣费类型(正式环境)
        insurance_fee:
          Math.round(this.GetYBinfoModel.recPayAmount * 100) +
          Math.round(this.GetYBinfoModel.payAmout * 100), //医保支付金额
        user_card_type: 1, //证件类型
        user_card_no: patcard.patIdCard, //证件号码
        user_name: patcard.patName, //真实姓名
        is_dept: "4", //医保电子凭证支付；
        serial_no: this.GetYBinfoModel.medOrgOrd, //医院 HIS 系统订单号
        org_no: "H44190100094", //医疗机构编码（医保局分配给机构）
        gmt_out_create: time, //医院下单时间
        request_content: requestcontentData, //参考医保结构体（医疗机构透传医保）
        channel_no: "AAEVuXphB5vVP6b9SJ0ItMAX" //渠道号
        // channel_no: "AAHAu_Ba3W8RQBbw2T-SLLHb" //测试渠道号
      };
      var pdata = {
        orderid: cfList[0].orderid,
        payOrdId: this.requestcontent.payOrdId
      };
      if (patcard.patIdCard == null) {
        toolsUtils.alert("您的诊疗卡身份证号为空，请把诊疗卡解绑再绑定!");
        return;
      }
      // alert("更新订单的医保订单号入参：" + JSON.stringify(pdata));
      // return
      ajax
        .post(apiUrls.updateYBorder, pdata)
        .then(r => {
          if (r.data.returnData != true) {
            // alert(
            //   JSON.stringify(
            //     "更新订单的医保订单错误，请重新生成订单:" + r.data.returnMsg
            //   )
            // );
            this.$router.push({
              path: "/cfLists"
            });
            return;
          } else {
            // alert("唤起医保支付入参：" + JSON.stringify(data));
            //return
            //下单,跳转支付接口
            ajax
              .post(apiUrls.unifiedOrder, data)
              .then(r => {
                //alert("唤起医保支付返参：" + JSON.stringify(r.data));
                if (r.data.success == true) {
                  if (r.data.returnData.result_code == "FAIL") {
                    alert(JSON.stringify(r.data.returnData.err_code_des));
                    alert(JSON.stringify("唤起医保支付错误，请重新生成订单"));
                    this.$router.push({
                      path: "/cfLists"
                    });
                    return;
                  }
                  // alert(JSON.stringify(r.data.returnData.pay_url));
                  window.location.href = r.data.returnData.pay_url;
                } else {
                  this.loadingShow = false;
                  toolsUtils.alert("生成支付订单失败" + r.data.returnMsg);
                  return;
                }
              })
              .catch(e => {
                this.loadingShow = false;
                toolsUtils.alert("网络异常" + e);
                return;
              });
          }
        })
        .catch(e => {
          this.loadingShow = false;
          toolsUtils.alert("网络异常" + e);
          return;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.cfpayInfo_page {
  height: 100%;
  background: #fff;
  overflow: auto;
  padding-bottom: 2rem;
  header {
    height: 3.24rem;
    background: #3b71e8;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    padding: 0 0.6rem;
    display: flex;
    font-size: 0;
    justify-content: space-between;
    h1 {
      font-weight: normal;
      height: auto;
      padding-top: 0.5rem;
    }
    span {
      font-size: 0.4rem;
      color: #a6c1ff;
    }
    p {
      font-size: 0.5rem;
      color: #fff;
      line-height: 0.5rem;
    }
    .login_img {
      margin-top: 0.56rem;
      width: 1.2rem;
      height: 1.2rem;
    }
  }
  .content {
    margin: -1.1rem 0.4rem 0 0.4rem;
    .cost_info {
      border-radius: 16px;
      background: #fff;
      box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
      padding: 0 0.4rem;
      margin-bottom: 0.56rem;
      h3 {
        font-size: 0.42rem;
        display: flex;
        height: 1.1rem;
        font-weight: normal;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px dashed #eff0f4;
      }
      ul {
        padding: 0.4rem 0;
        border-bottom: 1px dashed #eff0f4;
        li {
          display: flex;
          justify-content: space-between;
          font-size: 0.36rem;
          color: #909399;
          line-height: 0.36rem;
          & + li {
            margin-top: 0.36rem;
          }
          &:last-child {
            margin-top: 0.44rem;
            font-size: 0.44rem;
            color: #3b71e8;
          }
        }
      }
      .look_btn {
        height: 1.08rem;
        text-align: center;
        line-height: 1.08rem;
        font-size: 0.4rem;
        color: #606266;
      }
    }
    .payMode_wrap {
      margin-bottom: 0.56rem;
      .pay_title {
        font-size: 0.4rem;
        margin-bottom: 0.2rem;
        color: #666;
      }
      .payMode_btn {
        display: flex;
        justify-content: space-between;
        font-size: 0.34rem;
        color: #606266;
        span {
          text-align: center;
          border: 2px solid #ccc;
          flex: 1;
          flex-shrink: 0;
          height: 0.72rem;
          border-radius: 8px;
          line-height: 0.68rem;
          & + span {
            margin-left: 0.3rem;
          }
        }
        .span_active {
          color: #3b71e8;
          border-color: #3b71e8;
        }
      }
      ul {
        border: 2px solid #e6e6e6;
        border-radius: 16px;
        li {
          display: flex;
          align-items: center;
          height: 1.3rem;
          img {
            height: 0.6rem;
            width: 0.6rem;
            margin-left: 0.3rem;
          }
          p {
            display: flex;
            flex: 1;
            flex-shrink: 0;
            justify-content: space-between;
            align-items: center;
            font-size: 0.4rem;
            color: #303133;
            padding: 0 0.24rem;
            height: 100%;
          }
          & + li p {
            border-top: 2px solid #e6e6e6;
          }
        }
      }
    }
    .foot_tips {
      text-align: center;
      font-size: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        font-size: 0.3rem;
        color: #666;
      }
      img {
        height: 0.3rem;
        margin-right: 0.12rem;
      }
    }
  }
  footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1.5rem;
    border-top: 2px solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0.4rem;
    background: #fff;
    p {
      color: #666;
      font-size: 0.3rem;
      span {
        color: #3b71e8;
        font-size: 0.5rem;
      }
    }
    .payBtn {
      font-size: 0.4rem;
      color: #fff;
      width: 2.4rem;
      height: 1rem;
      text-align: center;
      line-height: 1rem;
      background: #3b71e8;
      border-radius: 1rem;
    }
  }
  .popup_content {
    h1 {
      font-size: 0.45rem;
      font-weight: normal;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #303133;
      padding: 0.6rem 0.4rem;
    }
    .every_wrap {
      padding: 0 0.2rem 0.2rem;
    }
    .every_detail {
      border: 2px solid #e6e6e6;
      border-radius: 12px;
      & + .every_detail {
        margin-top: 0.2rem;
      }
      h3 {
        font-size: 0.38rem;
        color: #303133;
        padding: 0.46rem 1px;
        border-bottom: 2px solid #e6e6e6;
        span {
          border-left: 0.08rem solid #3b71e8;
          padding: 0 0.24rem;
        }
      }
      ul {
        padding: 0.4rem;
      }
      li {
        display: flex;
        justify-content: space-between;
        font-size: 0.32rem;
        color: #606266;
        & + li {
          margin-top: 0.2rem;
        }
      }
      .cost_ul {
        i {
          border: 2px solid #606266;
          border-radius: 100%;
          width: 8px;
          height: 8px;
          display: inline-block;
          margin-right: 5px;
        }
        p {
          padding-left: 15px;
          color: #909399;
        }
      }
    }
  }
  .overlay_mask {
    background: rgba(255, 255, 255, 0.8);
    padding-top: 2rem;
    img {
      display: block;
      margin: 0 auto;
    }
    p {
      text-align: center;
      max-width: 5.6rem;
      margin: 0.4rem auto 0;
      color: #3b71e8;
      font-size: 0.4rem;
    }
  }
}
</style>
