import Vue from "vue";
import Router from "vue-router";
import homepage from "@/pages/inHospital/homepage";
import store from "../store";
import Hospitalization from "@/components/inHospital/Hospitalization";
import Detailedlist from "@/components/inHospital/Detailedlist";
import Payment from "@/components/inHospital/Payment";
import ChargeRecord from "@/pages/inHospital/chargeRecord";
import Charge from "@/pages/inHospital/charge";
import ChargeSuccess from "@/pages/inHospital/chargeSuccess";
import NoPay from "@/pages/outpatient/NoPaySuccess";
import index from "@/pages/index";
import Details from "@/pages/inHospital/details";
import Register from "@/pages/register";
import Usercenter from "@/pages/userCenter";
import sendMessage from "@/pages/sendMessage";
import OutpatientPage from "@/pages/outpatient/OutpatientPage";
import AddPatient from "@/pages/addPatient";
import PatientManage from "@/pages/patientManage";
// import personal from '@/pages/personal'
// import cardList from '@/pages/cardList'
import BindPage from "@/pages/inHospital/bindPage";
import oauth from "@/pages/oauth";
import { storage } from "../common/index";
import ChoiceDepartment from "@/pages/outpatient/choiceDepartment"; //选科室
import ChoiceDoctor from "@/pages/outpatient/choiceDoctor"; //选医生
import AboutTime from "@/pages/outpatient/aboutTime"; //预约时间
import SubmitAgo from "@/pages/outpatient/submitAgo";
import NowPay from "@/pages/outpatient/nowPay";
import CancelState from "@/pages/outpatient/cancelState";
import ActiveCancelState from "@/pages/outpatient/activeCancelState";
import ChoiceMethod from "@/pages/outpatient/choiceMethod";
import RegisterRecord from "@/pages/outpatient/registerRecord";
import cfregisterRecord from "@/pages/outpatient/cfregisterRecord"; //处方订单
import cfPaySuccess from "@/pages/outpatient/cfPaySuccess"; //处方订单详情
import CfList from "@/pages/outpatient/cfList";
import CfDetail from "@/pages/outpatient/cfDetail";
import PaySuccess from "@/pages/outpatient/PaySuccess";
import CfMingxi from "@/pages/outpatient/cfMingxi";
import outbindpages from "@/pages/outpatient/outbindpages";
import SeeReport from "@/pages/outpatient/seeReport";
import ReportDetail from "@/pages/outpatient/reportDetail";
import PayCancel from "@/pages/outpatient/PayCancel";
import CfNowPay from "@/pages/outpatient/cfNowpay";
import reportCheckDetail from "@/pages/outpatient/reportCheckDetail";
import RefundPay from "@/pages/outpatient/RefundPay";
import appointment from "@/pages/outpatient/appointment";
import AddPatients from "@/pages/addPatients";
import questions from "@/pages/outpatient/questions";
import checkInList from "@/pages/outpatient/checkInList";

import cfLists from "@/pages/ybpatient/cfLists";
import cfDetails from "@/pages/ybpatient/cfDetails";
import cfMingxis from "@/pages/ybpatient/cfMingxis";
import cfpayInfo from "@/pages/ybpatient/cfpayInfo";
import cfNowPays from "@/pages/ybpatient/cfNowPays";
import PaySuccesses from "@/pages/ybpatient/PaySuccesses";
import oauthret from "@/pages/ybpatient/oauthret";
import ybchargeSuccess from "@/pages/ybpatient/ybchargeSuccess";

import applyForm from "@/pages/HealthCard/applyForm";
import cardList from "@/pages/HealthCard/cardList";
import cardTemp from "@/pages/HealthCard/cardTemp";
import personal from "@/pages/HealthCard/personal";

import medicalInsurance from "@/pages/HealthCard/medicalInsurance";
import medicalInsuranceBinding from "@/pages/HealthCard/medicalInsuranceBinding";
import medicalInsuranceModify from "@/pages/HealthCard/medicalInsuranceModify";

import applyFormV2 from "@/pages/HealthCardV2/applyFormV2";
import cardListV2 from "@/pages/HealthCardV2/cardListV2";
import cardTempV2 from "@/pages/HealthCardV2/cardTempV2";
import personalV2 from "@/pages/HealthCardV2/personalV2";
import applyFormV3 from "@/pages/HealthCardV2/applyFormV3";
import cardListV3 from "@/pages/HealthCardV2/cardListV3";

Vue.use(Router);

const router = new Router({
  mode: "history",
  routes: [
    {
      path: "/",
      component: index,
      meta: {
        index: 0,
        Auth: true,
      },
    },
    {
      path: "/homepage",
      name: "index",
      meta: {
        Auth: true, // 需要登录
        index: 1,
      },
      component: homepage,
      children: [
        {
          path: "/homepage",
          name: "hospitalization",
          meta: {
            Auth: true, // 需要登录
            index: 1,
          },
          components: {
            chargeView: Hospitalization,
          },
        },
        {
          path: "/detailedlist",
          name: "detailedlist",
          meta: {
            Auth: true, // 需要登录
            index: 3,
          },
          components: {
            chargeView: Detailedlist,
          },
        },
        {
          path: "/payment",
          name: "payment",
          meta: {
            Auth: true, // 需要登录
            index: 2,
          },
          components: {
            chargeView: Payment,
          },
        },
      ],
    },
    {
      path: "/chargeRecord",
      meta: {
        Auth: true, // 需要登录
        index: 4,
      },
      component: ChargeRecord,
    },
    {
      path: "/charge",
      meta: {
        index: 4,
        Auth: true, // 需要登录
      },
      component: Charge,
    },
    {
      path: "/chargeSuccess",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: ChargeSuccess,
    },
    {
      path: "/NoPay",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: NoPay,
    },
    {
      path: "/details",
      meta: {
        Auth: true, // 需要登录
        index: 4,
      },
      component: Details,
    },
    {
      path: "/register",
      meta: {
        index: 0,
      },
      component: Register,
    },
    {
      path: "/usercenter",
      meta: {
        Auth: true, // 需要登录
        index: 1,
      },
      component: Usercenter,
    },
    {
      path: "/sendMessage",
      meta: {
        Auth: false, // 需要登录
        index: 1,
      },
      component: sendMessage,
    },
    {
      path: "/outpatientPage",
      meta: {
        index: 1,
      },
      component: OutpatientPage,
    },
    {
      path: "/addPatient",
      meta: {
        Auth: true, // 需要登录
        index: 8,
      },
      component: AddPatient,
    },
    {
      path: "/addPatients",
      meta: {
        Auth: true, // 需要登录
        index: 8,
      },
      component: AddPatients,
    },
    {
      path: "/patientManage",
      name: "patientManage",
      meta: {
        Auth: true, // 需要登录
        index: 7,
      },
      component: PatientManage,
    },
    {
      path: "/bindPage",
      meta: {
        Auth: true, // 需要登录
      },
      component: BindPage,
    },
    {
      path: "/oauth",
      component: oauth,
    },
    {
      path: "/registerRecord",
      name: "registerRecord",
      meta: {
        Auth: true,
        index: 2,
      },
      component: RegisterRecord,
    },
    {
      path: "/cfregisterRecord",
      name: "cfregisterRecord",
      meta: {
        Auth: true,
        index: 2,
      },
      component: cfregisterRecord,
    },
    {
      path: "/choiceMethod",
      name: "choiceMethod",
      meta: {
        index: 2,
        Auth: true,
      },
      component: ChoiceMethod,
    },
    {
      path: "/choiceDepartment",
      component: ChoiceDepartment,
      meta: {
        index: 3,
        keep: true,
      },
      name: "choiceDepartment",
    },
    {
      path: "/choiceDoctor",
      component: ChoiceDoctor,
      meta: {
        index: 4,
        Auth: true,
      },
      name: "choiceDoctor",
    },
    {
      path: "/aboutTime",
      component: AboutTime,
      meta: {
        index: 5,
        Auth: true,
      },
      name: "aboutTime",
    },
    {
      path: "/submitAgo",
      meta: {
        index: 6,
        Auth: true,
      },
      name: "submitAgo",
      component: SubmitAgo,
    },
    {
      path: "/nowPay",
      meta: {
        index: 7,
        Auth: true,
      },
      name: "nowPay",
      component: NowPay,
    },
    {
      path: "/cancelState",
      meta: {
        index: 8,
      },
      name: "cancelState",
      component: CancelState,
    },
    {
      path: "/activeCancelState",
      name: "activeCancelState",
      meta: {
        index: 9,
      },
      component: ActiveCancelState,
    },
    {
      path: "/PaySuccess",
      name: "PaySuccess",
      meta: {
        index: 10,
        Auth: true,
      },
      component: PaySuccess,
    },
    {
      path: "/cfPaySuccess",
      name: "cfPaySuccess",
      meta: {
        index: 10,
        Auth: true,
      },
      component: cfPaySuccess,
    },
    {
      path: "/PayCancel",
      name: "PayCancel",
      meta: {
        index: 11,
        Auth: true,
      },
      component: PayCancel,
    },
    {
      path: "/RefundPay",
      name: "RefundPay",
      meta: {
        index: 12,
        Auth: true,
      },
      component: RefundPay,
    },
    {
      path: "/questions",
      name: "questions",
      meta: {
        index: 13,
      },
      component: questions,
    },
    {
      path: "/checkInList",
      name: "checkInList",
      meta: {
        index: 14,
        Auth: true,
      },
      component: checkInList,
    },
    {
      path: "/cfList",
      name: "cfList",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: CfList,
    },
    {
      path: "/cfDetail",
      name: "cfDetail",
      meta: {
        Auth: true, // 需要登录
        index: 4,
      },
      component: CfDetail,
    },
    {
      path: "/cfMingxi",
      name: "cfMingxi",
      meta: {
        Auth: true, // 需要登录
        index: 5,
      },
      component: CfMingxi,
    },
    {
      path: "/outbindpages",
      name: "outbindpages",
      meta: {
        Auth: true, // 需要登录
        index: 10,
      },
      component: outbindpages,
    },
    {
      path: "/seeReport",
      name: "seeReport",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: SeeReport,
    },
    {
      path: "/reportDetail",
      name: "reportDetail",
      meta: {
        index: 3,
      },
      component: ReportDetail,
    },
    {
      path: "/cfNowpay",
      name: "cfNowpay",
      meta: {
        index: 4,
        Auth: true,
      },
      component: CfNowPay,
    },
    {
      path: "/reportCheckDetail",
      name: "reportCheckDetail",
      meta: {
        index: 3,
      },
      component: reportCheckDetail,
    },

    {
      path: "/cfLists",
      name: "cfLists",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: cfLists,
    },
    {
      path: "/cfpayInfo",
      name: "cfpayInfo",
      meta: {
        Auth: true, // 需要登录
        index: 4,
      },
      component: cfpayInfo,
    },
    {
      path: "/cfDetails",
      name: "cfDetails",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: cfDetails,
    },
    {
      path: "/cfMingxis",
      name: "cfMingxis",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: cfMingxis,
    },
    {
      path: "/cfNowPays",
      name: "cfNowPays",
      meta: {
        Auth: true, // 需要登录
        index: 3,
      },
      component: cfNowPays,
    },
    {
      path: "/PaySuccesses",
      name: "PaySuccesses",
      meta: {
        index: 3,
      },
      component: PaySuccesses,
    },
    {
      path: "/oauthret",
      name: "oauthret",
      meta: {
        index: 3,
      },
      component: oauthret,
    },
    {
      path: "/ybchargeSuccess",
      name: "ybchargeSuccess",
      meta: {
        index: 3,
      },
      component: ybchargeSuccess,
    },
    {
      path: "/appointment",
      name: "appointment",
      meta: {
        index: 4,
      },
      component: appointment,
    },

    //  ,{
    //    path:"/personal",
    //    name:"personal",
    //    meta:{
    //      Auth: true,
    //      index:4
    //    },
    //    component:personal
    //  },
    //  ,{
    //    path:"/cardList",
    //    name:"cardList",
    //    meta:{
    //      Auth:true,
    //      index:4
    //    },
    //    component:cardList
    //  },

    // 电子健康卡
    {
      path: "/applyForm",
      name: "applyForm",
      meta: {
        Auth: true,
        index: 4,
      },
      component: applyForm,
    },
    {
      path: "/cardList",
      name: "cardList",
      meta: {
        Auth: true,
        index: 4,
      },
      component: cardList,
    },
    {
      path: "/cardTemp",
      name: "cardTemp",
      meta: {
        Auth: true,
        index: 4,
      },
      component: cardTemp,
    },
    {
      path: "/personal",
      name: "personal",
      meta: {
        Auth: true,
        index: 4,
      },
      component: personal,
    },
    {
      path: "/medicalInsurance",
      name: "medicalInsurance",
      meta: {
        Auth: true,
        index: 4,
      },
      component: medicalInsurance,
    },
    {
      path: "/medicalInsuranceBinding",
      name: "medicalInsuranceBinding",
      meta: {
        Auth: true,
        index: 4,
      },
      component: medicalInsuranceBinding,
    },
    {
      path: "/medicalInsuranceModify",
      name: "medicalInsuranceModify",
      meta: {
        Auth: true,
        index: 4,
      },
      component: medicalInsuranceModify,
    },

    // 电子健康卡V2
    {
      path: "/applyFormV2",
      name: "applyFormV2",
      meta: {
        Auth: true,
        index: 4,
      },
      component: applyFormV2,
    },
    {
      path: "/cardListV2",
      name: "cardListV2",
      meta: {
        Auth: true,
        index: 4,
      },
      component: cardListV2,
    },
    //测试
    {
      path: "/applyFormV3",
      name: "applyFormV3",
      meta: {
        Auth: true,
        index: 5,
      },
      component: applyFormV3,
    },
    {
      path: "/cardListV3",
      name: "cardListV3",
      meta: {
        Auth: true,
        index: 5,
      },
      component: cardListV3,
    },
    {
      path: "/cardTempV2",
      name: "cardTempV2",
      meta: {
        Auth: true,
        index: 4,
      },
      component: cardTempV2,
    },
    {
      path: "/personalV2",
      name: "personalV2",
      meta: {
        Auth: true,
        index: 4,
      },
      component: personalV2,
    },
  ],
});

//路由过滤
router.beforeEach((to, from, next) => {
  if (to.meta.Auth) {
    if (storage.session.get("user") != null) {
      // console.log(store.state.baseServe.islogin);
      next();
    } else {
      // router.push({ name: 'login' })
      storage.session.set("redirect", to.fullPath);
      router.replace({
        path: "/oauth",
        query: { type: "jump" },
      });
      // next({
      //   path: '/oauth',
      //   query: { type:'jump' }
      // })
    }
  } else {
    next();
  }
});

export default router;
