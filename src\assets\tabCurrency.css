@charset "utf-8";
.hs-news .content-wrap {
    width: 100%;
    border-radius: 5px;
    background: #fff;
    overflow: hidden;
    padding-bottom: 0.2rem;
  }
  .hs-news .content-head {
    display: flex;
    background: #fdfbfb;
    font-size: 0.32rem;
    padding: 0 0.2rem;
    height: 1.16rem;
    align-items: center;
  }
  .hs-news .content-head .head-icon {
    width: 0.72rem;
    height: 0.72rem;
    border-radius: 100%;
    overflow: hidden;
    margin-right: 0.1rem;
  }
  .hs-news .content-head .head-icon img{
    height:100%;
  }
  .hs-news .content-head span {
    flex-grow: 1;
    color: #24bab8;
  }
  .hs-news .content-head p {
    flex-grow: 1;
    text-align: right;
    color: #7F8FA4;
  }