export default {
  AccountByOpenId: '/hisaccount/AccountByOpenId',//openid自动登录
  GetVerifyCode: '/common/GetVerifyCode',//短信验证
  AddUsers: '/hisaccount/AddUsers',//注册提交用户数据
  GetUserFamily: '/hisaccount/getUserFamily',//获取就诊家人列表数据
  BindAdmissionCard: '/HisAdmissionCard/bindAdmissionCard',//绑定开启住院功能
  GetBedFee: '/HisAdmissionCard/getBedFee',//缴费信息
  GetPerBedFeeList: '/HisAdmissionCard/getPerBedFeeList',//一日清单列表
  GetPerBedFeeDetailList: '/HisAdmissionCard/getPerBedFeeDetailList',//清单明细
  GetWxConfig: '/hiswechat/getWxConfig',//获取微信配置
  AddOrders: '/hisOrder/addOrders', //生成住院订单
  UpdateRegOrders: '/hisOrder/updateRegOrders',//更新挂号订单
  LockRegSeqToday: 'hisPatcard/lockRegSeqToday',//当天锁号接口
  UnlockRegSeqToday: 'hisPatcard/unlockRegSeqToday',//当天锁号取消接口
  AddRegOrders: 'hisOrder/addRegOrder',//当天挂号接口
  UpdateOrders: '/hisOrder/updateOrders',//更新住院订单
  RefundRegOrders: '/hisOrder/RefundRegOrders',//订单退费
  CancelRegOrders: '/hisOrder/cancelRegOrders', //订单取消
  getSuccessPayType: '/hisOrder/getSuccessPayType',//获取订单支付方式
  GetadmissionFamilyInfo: 'HisAdmissionCard/GetadmissionFamilyInfo',//获取病人信息
  GetDepositList: '/hisOrder/getDepositList',//获取缴费记录
  GetCfOrderList: '/hisOrder/getCfOrderList',//获取处方记录
  GetpatientOrderList: '/hisOrder/getpatientOrderList',//获取挂号订单
  GetpatientOrderInfo: '/hisOrder/getpatientOrderInfo', //获取订单详情
  AddFamilyType: 'HisAdmissionCard/AddFamilyType',//添加家人账号
  Deletefamilyrelation: 'HisAdmissionCard/Deletefamilyrelation',//删除家人账号
  GetDeptRegInfo: 'hisPatcard/getDeptInfo', //获取科室信息
  GetDoctorInfo: 'hisPatcard/getDrInfo',//获取科室医生信息
  GetDoctorTimeInfo: 'hisPatcard/getDrTimeInfo',//获取分时医生信息
  bindPatCard: '/hisPatcard/BindPatCard',//绑定门诊号
  getcfList: '/hisPatcard/getcfList',//获取处方列表
  AddPreRegOrders: '/hisOrder/addRegPreOrders', //生成预约订单
  GetpatFamilyInfo: '/hisPatcard/GetpatFamilyInfo',//获取家人详细信息
  DeletePatfamilyrelation: '/hisPatcard/Deletefamilyrelation',//删除家人信息
  AddPatFamilyType: '/hisPatcard/AddFamilyTypes',//添加家人关系
  AddFamilyTypesForChildren: '/hisPatcard/AddFamilyTypesForChildren',//添加家人关系AddFamilyTypesForChildren
  getNoPayDetailInfo: '/hisPatcard/getNoPayDetailInfo',//获取患者待缴费明细记录
  getNoPayRecipeInfo: '/hisPatcard/getNoPayRecipeInfo',//获取患者待缴费明细记录
  addRegPatPreOrders: '/hisOrder/addRegPatPreOrders',//生成处方缴费订单
  UpdateRegPatOrders: '/hisOrder/UpdateRegPatOrders',//处方微信缴费
  getLaboratoryList: '/hisPatcard/getLaboratoryList',//检验报告
  getInspectionList: '/hisPatcard/getInspectionList',//检查报告
  getLaboratory: '/hisPatcard/getLaboratory',//检验报告明细
  getInspection: '/hisPatcard/getInspection',//检查报告明细
  getDeptParentClassify: '/hisOrder/getDeptParentClassify',//一级科室
  getDeptClassify: '/hisOrder/getDeptClassify',//二级科室
  // addgetsiginfo:'/hisOrder/addgetsiginfo',//签到
  cfCancelRegOrders: '/hisOrder/cfCancelRegOrders', //处方订单取消
  //getMedInfo:'/miPay/getMedInfo',//查询医保信息
  noWxPayRegOrders: '/hisOrder/noWxPayRegOrders',//预约挂号不支付订单
  addHSRegPreOrders: 'hisOrder/addHSRegPreOrders',//核酸检测生成处方订单
  //getToken:'/miPay/getToken',//跳转绑定医保链接
  userQuery: '/MIPay/userQuery',//查询医保信息
  SetYBinfo:'/MIPay/SetYBinfo',//插入医保信息
  unifiedOrder: '/MIPay/unifiedOrder',//医保支付下单
  GetHisCfDetail: '/MIPay/GetHisCfDetail',//获取his处方订单医保信息
  GetHisCfDetails: '/MIPay/GetHisCfDetails',//获取his处方订单医保补充信息
  updateYBorder: '/hisOrder/updateYBorder', //更新订单的医保订单号
  endtimeOrder: '/hisOrder/getendtimeOrder',//获取订单倒计时结束时间
  // registerHealthCard:'/HisPatCard/registerHealthCard',// 申领电子健康码
  EncoderAES: '/HisPatCard/EncoderAES',//解密AES密文
  bindHealthCardqrCode: '/HisPatCard/bindHealthCardqrCode',//绑定电子二维码
  getcardList: '/HisPatCard/getcardList',//绑定电子二维码
  getUserCard: '/HisPatCard/GetUserCard',//获取用户所有门诊卡
  UserIsNotTure: '/HisPatCard/UserIsNotTure',//判断用户卡是否正确
  IsLockPatOrder:'/hisOrder/IsLockPatOrder',//锁定或解锁处方订单

  IDcardGetYBUserInfo:'/HisPatCard/IDcardGetYBUserInfo',//身份证获取定点信息（1101+90110）
  GetPsnno:'/HisPatCard/GetPsnno',//用户基础信息1101
  GetYBUserInfo:'/HisPatCard/GetYBUserInfo',//用户医保查询90110
  UserYBbinding:'/HisPatCard/UserYBbinding',//用户医保绑定90210
  UserYBUpdate:'/HisPatCard/UserYBUpdate',//用户医保修改90211

  registerHealthCard: '/healthCard/registerHealthCard',//注册健康卡
  yjregisterHealthCard: '/healthCard/registerHealthCardByHealthCode',//注册健康卡
  getCardListHealthCard: '/healthCard/getCardListHealthCard',//获取健康卡信息
  getCardListOldCard: '/healthCard/getCardListOldCard',//获取需要升级的门诊卡列表
  getMyHealthCard: '/healthCard/getMyHealthCard',//查看健康卡详情
  delHealthCard: '/healthCard/delHealthCard',//解绑健康卡
  getOrderId: '/healthCard/getOrderId',//获取OrderId
  upHealthCard: '/healthCard/upHealthCard',//升级健康卡

  registerHealthCardV2: '/healthCardV2/registerHealthCard',//注册健康卡
  getCardListHealthCardV2: '/healthCardV2/getCardListHealthCard',//获取健康卡信息
  getMyHealthCardV2: '/healthCardV2/getMyHealthCard',//查看健康卡详情

  GetWxMeassage:'/hisOrder/GetWxMeassage',//查询微信汇总数据
  SetQuestionsAns:'/hisOrder/SetQuestionsAns',//门诊问卷调查统计
  ArchiveOauth:'/hisOrder/LQQQ',//电子病例授权

  GetUserHisOrder:'/hisOrder/getUserHisOrder',//扫码获取用户预约信息
  userReported:'/hisOrder/userReported',//扫码报道

  GetFaceAuthorUrl: '/healthCardV2/GetFaceAuthorUrl',//获取人脸识别地址
  FaceVerifyNotice: '/healthCardV2/FaceVerifyNotice',//人脸识别认证结果通知
  GetFaceVerifyResult: '/healthCardV2/GetFaceVerifyResult',//查询人脸识别结果
};
