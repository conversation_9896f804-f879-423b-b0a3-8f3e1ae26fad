<template>
  <!--授权登录-->
  <div>
    <!-- {{title}} -->
    <loading :show="true" text="正在登陆"></loading>
  </div>
</template>

<script>
import { toolsUtils, storage, ajax } from "../common";
import { authService } from "../services/index";
import apiUrls from "../config/apiUrls";
import { setTimeout } from "timers";
import { Loading } from "vux";

export default {
  components: {
    Loading
  },
  data() {
    return {
      title: "正在授权中...",
      success: "",
      type: "",
      openid: ""
    };
  },
  created() {},
  mounted() {
    this.success = this.$route.query["success"];
    this.type = this.$route.query["type"] || "login";

    if (this.type == "OAuthCallback" || this.success == 0) {
      this.title = this.$route.query["returnMsg"];
      toolsUtils.alert(this.title);
      return;
    }
    if (this.success == undefined || this.success == "" || this.success != 1) {
      this.toWeChatOauth(this.type);
      return;
    }
    this.weChatAuthorized();
  },
  methods: {
    //401 接口授权

    //微信授权登录
    toWeChatOauth(type) {
      window.location.replace(authService.toWeChatOauth(type));
      return true;
    },

    //获取到的openid做的业务
    weChatAuthorized() {
      //获取用户信息
      this.openid = this.$route.query["returnData"];
      storage.session.set("openid", this.openid);
      //openid登录方法

      switch (this.type) {
        case "aboutTime":
          this.openidlogin(this.openid, "/aboutTime"); //未登录时在门诊预约的医生排班页面授权后跳转回原页面
          // this.$router.push({ path: "/login" });
          break;
        case "register":
          window.location.replace("/register");
          // this.$router.push({ path: "/register" });
          break;
        case "inhospital":
          window.location.replace("/homepage");
          // this.$router.push({ path: "/homepage" });
          break;
        case "outpatient":
          window.location.replace("/userCenter");
          // this.$router.push({ path: "/outbindpages" });
          break;
        case "jump":
          this.openidlogin(this.openid, storage.session.get("redirect"));
          break;
        default:
          this.openidlogin(this.openid, "/");
          break;
      }
    },
    openidlogin(openid, tourl) {
      if (openid == null || openid == "" || openid == undefined) {
        alert("openid授权失败！当前为" + openid);
        window.location.replace("/oauth?type=index");
        return;
      }

      var data = {
        openid: openid
      };
      ajax
        .post(apiUrls.AccountByOpenId, data)
        .then(r => {
          r = r.data;
          if (!r.success) {
            toolsUtils.alert(r.returnMsg);
            if (r.returnMsg == "暂未注册") {
              window.location.replace("/register");
              // window.location.href='/register';
              // this.$router.push({ path: "/register" });
            }
            return;
          }
          storage.session.set("user", JSON.stringify(r.returnData.user));

          //住院
          if (r.returnData.admissioncard == null) {
            storage.session.delete("admissionCard");
          } else {
            storage.session.set(
              "admissionCard",
              JSON.stringify(r.returnData.admissioncard)
            );
          }

          //门诊
          if (r.returnData.patcard == null) {
            //没有绑定自己的门诊卡默认绑定家人门诊卡列表的第一张卡
            storage.session.delete("patcard");
            var data = {
              type: "1",
              id_card: JSON.parse(storage.session.get("user")).idCard,
              openid: JSON.parse(storage.session.get("user")).accountCode
            };
            ajax
              .post(apiUrls.GetUserFamily, data)
              .then(r2 => {
                // console.log(r);
                var r2 = r2.data;
                if (r2.success == false) {
                  toolsUtils.alert(r2.returnMsg);
                  return;
                }
                if (r2.returnData.length != 0) {
                  storage.session.set(
                    "patcard",
                    JSON.stringify(r2.returnData[0])
                  ); //默认绑定第一张
                }
                authService
                  .getToken(
                    r.returnData.user.accountCode,
                    r.returnData.user.pwd
                  )
                  .then(t => {
                    // alert(JSON.stringify(t));
                    window.location.replace(tourl);
                    // window.location.href=tourl;
                  });
                // window.location.href=tourl;
                return;
              })
              .catch(e => {
                toolsUtils.alert("程序异常:" + JSON.stringify(e));
              });
          } else {
            storage.session.set(
              "patcard",
              JSON.stringify(r.returnData.patcard)
            );
            authService
              .getToken(r.returnData.user.accountCode, r.returnData.user.pwd)
              .then(t => {
                // alert(JSON.stringify(t));
                window.location.replace(tourl);
                // window.location.href=tourl;
              });
            // window.location.href=tourl;
            return;
          }
        })
        .catch(e => {
          console.log(e);
          toolsUtils.alert("系统异常:" + JSON.stringify(e));
          return;
        });
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped></style>
