<template>
<!--轮播组件-->
    <div class="record-page">
        轮播组件
    </div>
</template>

<script>
export default {
  name: "index",
  components: {
    // HeaderBar
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
    
    };
  },
  methods:{
    
  },
  computed:{
      
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

</style>

