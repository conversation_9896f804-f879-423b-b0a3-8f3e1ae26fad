<template>
  <!--用户中心-->
  <div class="userCenter-page">
    <!-- head -->
    <div class="head-logo">
      <img src="../assets/user_face.png" alt />
      <span>{{ userInfo.name }}</span>
    </div>

    <!-- 门诊住院 -->
    <div class="routerList">
      <div @click="transferFun(1)">
        <router-link to="" class="listOne">
          <div class="listImg">
            <img src="../assets/icon/menzhen01.png" alt="" />
          </div>
          <div class="listText"><span>门诊（绑定门诊卡）</span></div>
          <div class="listImgage">
            <img src="../assets/right.png" alt="" />
          </div>
        </router-link>
      </div>

      <div @click="transferFun(2)">
        <router-link to="" class="listTwo">
          <div class="listImg">
            <img src="../assets/icon/zhuyuan01.png" alt="" />
          </div>
          <div class="listText"><span>住院（绑定住院卡）</span></div>
          <div class="listImgage">
            <img src="../assets/right.png" alt="" />
          </div>
        </router-link>
      </div>

      <!-- <div >
              <router-link to=""  class="listTwo" @click.native="jumptoOutUrl('http://qtyywx.qtyy.com/cardList')"> 
           <div class="listImg">
             <img src="../assets/icon/gerenxinxi.png" alt="">
           </div>
           <div class="listText"><span>电子健康卡</span></div>
           <div class="listImgage">
             <img src="../assets/right.png" alt="">
           </div>
            </router-link>
         </div>-->
      <div></div>
    </div>
    <!-- 退出账号 -->
    <div class="routerList">
      <div>
        <router-link to="" class="listOne" @click.native="outAccout">
          <div class="listImg">
            <img src="../assets/icon/tuichudenglu.png" alt="" />
          </div>
          <div class="listText">
            <span>
              <div v-transfer-dom>
                <confirm
                  v-model="accountShow"
                  title="温馨提示："
                  @on-cancel="onCancel"
                  @on-confirm="onConfirm"
                >
                  <p style="text-align:center;">是否确认退出当前账号</p>
                </confirm>
              </div>
              <div class="out-account">退出账号</div>
            </span>
          </div>
          <div class="listImgage">
            <img src="../assets/right.png" alt="" />
          </div>
        </router-link>
      </div>
    </div>
    <!-- 返回首页 -->
    <div>
      <router-link to="/" class="GoHome">
        <div class="GoHomeImg">
          <img src="../assets/icon/GoHome.png" alt="" />
          <span>返回首页</span>
        </div>
      </router-link>
    </div>
  </div>
</template>

<script>
import { Group, Cell, XButton, Confirm } from "vux";
import router from "../router";
import { ajax, toolsUtils, storage } from "../common";
export default {
  name: "index",
  components: {
    // HeaderBar
    Group,
    Cell,
    XButton,
    Confirm
  },
  data() {
    return {
      title: "用户中心",
      mz_data: "",
      zy_data: "",
      ty_data: "",
      contentData: "",
      tab_active: "",
      srcNum: "",
      userInfo: [],
      accountShow: false //确认退出当前账号
    };
  },
  methods: {
    jumptoOutUrl(url) {
      window.location.href = url;
      return;
    },
    outAccout() {
      this.accountShow = true;
    },
    onCancel() {
      this.accountShow = false;
    },
    onConfirm() {
      sessionStorage.clear();
      this.$router.push({
        path: "/oauth?type=register"
      });
    },
    transferFun(type) {
      if (type == "1") {
        this.$router.push({
          pash: "/cardListV2",
          name: "cardListV2",
          query: {
            type: type,
            method: "user"
          }
        });
      } else {
        this.$router.push({
          pash: "/patientManage",
          name: "patientManage",
          query: {
            id_card: this.userInfo.idCard,
            type: type,
            method: "user"
          }
        });
      }
      // console.log(type)
    },
    defaultData() {
      //用户
      var info = JSON.parse(storage.session.get("user"));
      this.userInfo = info;
    }
  },

  created() {
    this.defaultData();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.userCenter-page {
  height: 100%;
  width: 100%;
  background: #f0eff6;
  display: flex;
  flex-flow: column;
}
.head-logo {
  display: flex;
  width: 100%;
  height: 3.92rem;
  color: #fff;
  align-items: center;
  border-bottom: 1px solid #fff;
  background-image: url("../assets/icon/bg01.png");
  background-repeat: no-repeat;
  background-size: cover;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.head-logo img {
  display: block;
  width: 1.6rem;
  height: 1.6rem;
  margin: 0 auto 0.2rem;
}
.head-logo span {
  font-size: 0.4rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  /* text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5); */
  text-shadow: 1px -2px 1px rgba(0, 0, 0, 0.9);
  text-align: center;
}
.routerList {
  width: 100%;
  background: #ffffff;
  font-size: 0.32rem;
  margin-top: 0.36rem;
}
.listOne {
  width: 100%;
  height: 1.16rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.listTwo {
  margin-top: 1px;
  width: 100%;
  height: 1.16rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f0eff6;
}
.listImg {
  height: 1.16rem;
  width: 11%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.listText {
  height: 1.16rem;
  width: 78%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.listText span {
  margin-left: 0.2rem;
  color: #354052;
}
.out-account {
  color: #354052;
  font-size: 0.32rem;
}
.listImgage {
  height: 1.16rem;
  width: 11%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.GoHome {
  width: 100%;
  height: 1.16rem;
  background: #2dc4c2;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
}
.GoHomeImg {
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 2rem;
  height: 1.16rem;
}
.GoHomeImg img {
  width: 0.4rem;
  height: 0.4rem;
}
</style>
