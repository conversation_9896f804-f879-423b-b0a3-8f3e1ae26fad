<template>
  <div class="hs-news hs-payment">
    <div class="content-wrap">
      <div class="content-head">
        <div class="head-icon">
          <img src="../../assets/jiaofeixinxi.png" alt="" />
        </div>
        <span class="">缴费信息</span>
      </div>
      <!--  -->
      <div class="cost-wrap">
        <div class="cost-item">
          <div class="cost-img">
            <img src="../../assets/yijiaofei.png" alt="" />
          </div>
          <p>截止昨日<br />- 已缴费</p>
          <span>￥{{ (parseFloat(leftFee) / 100).toFixed(2) }}</span>
        </div>
        <div class="cost-item">
          <div class="cost-img">
            <img src="../../assets/daijiaofei.png" alt="" />
          </div>
          <p>截止昨日<br />-待缴费</p>
          <span class="color-red"
            >￥{{ (parseFloat(payedFee) / 100).toFixed(2) }}</span
          >
        </div>
        <div class="cost-item">
          <div class="cost-img">
            <img src="../../assets/zongfeiyong.png" alt="" />
          </div>
          <p>截止昨日<br />-总费用</p>
          <span>￥{{ (parseFloat(totalFee) / 100).toFixed(2) }}</span>
        </div>
      </div>
      <!--  -->
      <div class="payment-footer">
        <!-- <div class="balance">
              <p>我的余额</p>
              <span>￥5000</span>
          </div> -->
        <router-link to="/chargeRecord" class="payment-link">
          <p>缴费记录</p>
          <img src="../../assets/more.png" alt="" class="more-img" />
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  components: {},
  data() {
    return {
      leftFee: 0,
      payedFee: 0,
      totalFee: 0,
      payData: ""
    };
  },
  mounted() {
    this.paymentData();
  },
  methods: {
    paymentData() {
      var info = JSON.parse(storage.session.get("admissionCard"));
      // console.log(info);
      // var payData=JSON.parse(storage.session.get("pydata"));
      var payData = this.payData;
      if (payData != null && payData != "") {
        this.leftFee = payData.leftFee;
        this.payedFee = payData.payedFee;
        this.totalFee = payData.totalFee;
        this.$parent.getPaymentMoney(this.payedFee);
        return;
      }
      var data = {
        admissionCardNo: info.admissionNo
      };
      // console.log(data)
      ajax
        .post(apiUrls.GetBedFee, data)
        .then(r => {
          var r = r.data;
          //  console.log(r)
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.leftFee = parseFloat(r.returnData.leftFee).toFixed(2);
          this.payedFee = parseFloat(r.returnData.payedFee).toFixed(2);
          this.totalFee = parseFloat(r.returnData.totalFee).toFixed(2);
          var pydata = {
            leftFee: this.leftFee,
            payedFee: this.payedFee,
            totalFee: this.totalFee
          };
          this.$parent.getPaymentMoney(this.payedFee);
          console.log(this.payedFee);
          storage.session.set("pydata", JSON.stringify(pydata));
        })
        .catch(e => {
          toolsUtils.alert("系统异常");
        });
    }
  }
};
</script>

<style scoped>
@import url("../../assets/tabCurrency.css");
.hs-payment .content-wrap {
  padding-bottom: 0 !important;
}
.cost-wrap {
  display: flex;
  border-bottom: 1px solid #dfe3e9;
}
.cost-wrap .cost-item {
  display: flex;
  align-items: center;
  border-right: 1px solid #dfe3e9;
  flex-flow: column;
  flex-grow: 1;
  padding: 0.3rem 0;
}
.cost-wrap .cost-item:last-child {
  border: none;
}
.cost-wrap .cost-item .cost-img {
  height: 0.56rem;
  width: 0.56rem;
  overflow: hidden;
}
.cost-wrap .cost-item .cost-img img {
  height: 100%;
  display: block;
}
.cost-wrap .cost-item p {
  font-size: 0.32rem;
  color: #354052;
}
.cost-wrap .cost-item span {
  font-size: 0.32rem;
  color: #7f8fa4;
}
.color-red {
  color: #fd7070 !important;
}
.payment-footer .payment-link,
.payment-footer .balance {
  display: flex;
  padding: 0 0.2rem;
  font-size: 0.32rem;
  height: 1.08rem;
  line-height: 1.08rem;
  border-bottom: 1px solid #dfe3e9;
  align-items: center;
}
.payment-footer .payment-link p,
.payment-footer .balance p {
  color: #354052;
  flex-grow: 1;
}
.payment-footer .balance span {
  color: #7f8fa4;
  flex-grow: 2;
  text-align: right;
}
.payment-footer .payment-link {
  border: none;
}
.payment-footer .payment-link .more-img {
  height: 0.3rem;
}
</style>
