// 基础服务
const state = {
    all: [],
    islogin:false,
    adminCode:"admin",
    adminPwd:"admin",
    adminName:"xx",
    adminId:0
  }
  
  // getters 状态
  const getters = {
    // allProducts: state => state.all
  }
  
  // actions 异步方法
  const actions = {
    getAllProducts ({ commit }) {
      // shop.getProducts(products => {
      //   commit('setProducts', products)
      // })
    }
  }
  
  // mutations 同步方法
    // this.$store.commit('addCardItem', 1);
  const mutations = {
    setData(state, newData) {
      state.islogin=true;
      state.adminCode=newData.code;
      state.adminPwd=newData.pwd;
      state.adminName=newData.Admin_Name;
      state.adminId=newData.ID;
    },
    saftoutlogin(state,newvue)
    {
      state.islogin=newvue;
    }
  
    // decrementProductInventory (state, { id }) {
    //   const product = state.all.find(product => product.id === id)
    //   product.inventory--
    // }
  }
  
  export default {
    state,
    getters,
    actions,
    mutations
  }