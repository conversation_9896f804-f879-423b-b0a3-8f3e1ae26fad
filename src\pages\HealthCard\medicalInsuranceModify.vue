<template>
  <div class="medicalInsuranceModify">
    <h3 class="title">[90211]人员定点备案变更</h3>
    <div>
      <van-field
        v-model="form.number"
        label="人员编号"
        placeholder="请输入人员编号"
        disabled
      />
      <van-field
        readonly
        clickable
        :value="format(form.startTime)"
        label="开始日期"
        placeholder="点击选择日期"
        @click="showPicker = true"
      />
      <van-popup v-model="showPicker" position="bottom">
        <van-datetime-picker
          v-model="currentDate"
          type="date"
          title="选择年月日"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="onConfirm"
          @cancel="showPicker = false"
        />
      </van-popup>
      <van-field
        v-model="form.endTime"
        label="结束日期"
        placeholder="请输入结束日期"
        disabled
      />
      <div class="custom-field">
        <div class="field-label">定点事件类型</div>
        <div class="field-content">
          <van-radio-group v-model="form.type" direction="horizontal" icon-size="18px">
            <van-radio :name="2" checked-color="#24bab8">变更</van-radio>
            <van-radio :name="3" checked-color="#24bab8">暂停</van-radio>
          </van-radio-group>
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">主辅标志</div>
        <div class="field-content">
          <van-radio-group v-model="form.flag" direction="horizontal" icon-size="18px">
            <van-radio :name="1" checked-color="#24bab8">主就医点</van-radio>
            <van-radio :name="2" checked-color="#24bab8">辅就医点</van-radio>
          </van-radio-group>
        </div>
      </div>
      <div class="custom-field">
        <div class="field-label">就医点类型</div>
        <div class="field-content">
          <van-radio-group v-model="form.radio" direction="horizontal" icon-size="18px">
            <van-radio :name="1" checked-color="#24bab8">工作地</van-radio>
            <van-radio :name="2" checked-color="#24bab8">居住地</van-radio>
          </van-radio-group>
        </div>
      </div>
      <van-field
        v-model="form.organizationNo"
        label="定点医药机构编号"
        placeholder="请输入定点医药机构编号"
        disabled
      />
      <van-field
        v-model="form.organizationName"
        label="定点医药机构名称"
        placeholder="请输入定点医药机构名称"
        disabled
      />
      <van-field v-model="form.note" label="备注" placeholder="请输入备注" />
      <div class="submit">
        <van-button round block color="#24bab8" @click="onSubmit"
          >修改</van-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { ajax, storage } from "../../common";
import "vant/lib/index.css";
import apiUrls from "../../config/apiUrls";
export default {
  name: "medicalInsuranceModify",
  data() {
    return {
      showPicker: false,
      form: {
        number: "",
        startTime: new Date(),
        endTime: "2099-12-31",
        type: 2,
        flag: 1,
        radio: 1,
        organizationNo: "H44190100094",
        organizationName: "东莞市桥头医院",
        note: ""
      },
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(2099, 12, 30),
      currentDate: new Date()
    };
  },
  created() {
    this.form.number = JSON.parse(storage.session.get("psn_no"));
  },
  methods: {
    onConfirm(time) {
      this.form.startTime = time;
      this.showPicker = false;
    },
    onSubmit() {
      var psn_no = JSON.parse(storage.session.get("psn_no")),
        data = {
          psn_no: psn_no,
          begndate: this.format(this.form.startTime),
          fix_evt_type: String(this.form.type),
          fix_main_scd_flag: String(this.form.flag),
          fixmedins_code: String(this.form.radio),
          memo: this.form.note
        };
      // console.log(data);
      // return;
      ajax.post(apiUrls.UserYBUpdate, data).then(r => {
        r = r.data;
        console.log(r);
        if (r.success == false) {
          // toolsUtils.alert(r.returnMsg);
          //window.location.replace('/medicalInsurance');
          this.$dialog
            .alert({
              title: "提示",
              message: r.returnMsg,
              confirmButtonColor: "#24bab8"
            })
            .then(() => {
              this.$router.replace({ path: "/medicalInsurance" });
            });
        } else {
          // toolsUtils.alert(r.returnData);
          //window.location.replace('/medicalInsurance');
          this.$dialog
            .alert({
              title: "提示",
              message: r.returnData,
              confirmButtonColor: "#24bab8"
            })
            .then(() => {
              this.$router.replace({ path: "/medicalInsurance" });
            });
        }
      });
      // .catch(e => {
      //  toolsUtils.alert("程序异常:" + JSON.stringify(e));
      // });
    },
    format(time) {
      //获取年月日，时间
      let year = time.getFullYear();
      let mon =
        time.getMonth() + 1 < 10
          ? "0" + (time.getMonth() + 1)
          : time.getMonth() + 1;
      let date = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
      let newDate = year + "-" + mon + "-" + date;
      return newDate;
    }
  }
};
</script>

<style lang="less" scoped>
.medicalInsuranceModify {
  width: 100%;
  height: 100%;
  background: #fff;
  font-size: 0.32rem;
  .title {
    font-size: 0.36rem;
    text-align: center;
    padding: 0.4rem 0;
  }
  .submit {
    margin: 0.32rem;
  }
  /deep/.van-field--disabled .van-field__label {
    color: #646566;
  }

  .custom-field {
    position: relative;
    display: flex;
    box-sizing: border-box;
    width: 100%;
    padding: 10px 16px;
    overflow: hidden;
    color: #323233;
    font-size: 14px;
    line-height: 24px;
    background-color: #fff;
    border-bottom: 1px solid #ebedf0;
  }

  .field-label {
    width: 90px;
    margin-right: 12px;
    color: #646566;
  }

  .field-content {
    flex: 1;
    display: flex;
    align-items: center;
  }

  /deep/.van-radio {
    margin-right: 16px;
  }

  /deep/.van-radio__icon {
    display: inline-block !important;
    height: 18px;
    line-height: 18px;
    vertical-align: middle;
  }

  /deep/.van-radio__label {
    line-height: 18px;
    vertical-align: middle;
  }

  /deep/.van-radio-group {
    display: flex;
    flex-wrap: wrap;
  }
}
</style>
