<template>
  <!--清单详情页-->
  <div class="about-time">
    <chufangInfo></chufangInfo>
    <div class="list-group">
      <group class="cell-group">
        <cell title="支付方式" value="微信支付">
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            src="../../assets/weixinzhifu.png"
          />
        </cell>
      </group>
    </div>
    <p v-if="orderinfo.status == 1">
      <img src="../../assets/warn.png" alt />温馨提示请在
      <clocker
        :time="timeoutdate"
        format="%M 分 %S 秒"
        @on-finish="lq()"
      ></clocker
      >内完成支付，逾期订单将会作废，需重新支付
    </p>
    <div v-if="orderinfo.status == 1" class="bottom-btn">
      <button @click="cancelFun()" v-dbClick>取消订单</button>
      <button @click="WxPay()" v-dbClick>立即支付</button>
    </div>
    <div v-if="orderinfo.status != 1" class="bottom-btn">
      <button @click="closefun()">确 认</button>
    </div>
  </div>
</template>

<script>
import { Cell, Group, Clocker } from "vux";
import { ajax, storage, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
const chufangInfo = () => import("../../components/outpatient/chufangInfo");
// import chufangInfo from '../../components/outpatient/chufangInfo';
import baseData from "../../config/baseData";
import $ from "jquery";

export default {
  name: "index",
  components: {
    Cell,
    Group,
    chufangInfo,
    Clocker
  },
  data() {
    return {
      orderinfo: {},
      orderid: "",
      paymoney: "", //支付金额
      timeoutdate: ""
    };
  },
  created() {
    this.orderid = this.$route.query.orderid;
    var pdata = { orderid: this.orderid };
    var that = this;
    console.log(pdata);
    ajax
      .post(apiUrls.GetpatientOrderInfo, pdata)
      .then(r => {
        var data = r.data;
        if (!data.success) {
          toolsUtils.alert(data.returnMsg);
          return;
        }
        console.log(data.returnData);
        that.orderinfo = data.returnData;
        var datas = [
          {
            deptId: that.orderinfo.deptId,
            deptName: that.orderinfo.deptName,
            doctorId: that.orderinfo.doctorId,
            doctorName: that.orderinfo.doctorName,
            orderid: that.orderinfo.orderNo,
            patName: that.orderinfo.patName,
            patientidpid: that.orderinfo.patientidpid,
            payAmount: that.orderinfo.payAmout,
            settleMethodId: that.orderinfo.settleMethodId,
            settleMethodName: that.orderinfo.settleMethodName,
            visitId: that.orderinfo.visitId,
            status: that.orderinfo.status,
            hisMsg: that.orderinfo.hisMsg
          }
        ];
        storage.session.set("cfList", JSON.stringify(datas));
        this.paymoney = datas[0].payAmount;
        //如果订单状态为6(异常订单) 则显示异常信息
        if (datas[0].status == 6) {
          alert(datas[0].hisMsg);
        }
      })
      .catch(e => {
        toolsUtils.alert("程序异常:" + JSON.stringify(e));
      });
    return;
  },
  mounted() {
    this.getdate();
    // this.WxConfig();
  },
  methods: {
    lq() {
      alert("订单超时，请重新预约!");
      WeixinJSBridge.call("closeWindow");
    },
    getdate() {
      if (this.$route.query.orderid != null) {
        var date = new Date(new Date().getTime() + 1000 * 60 * 14);
        var outtime = dataUtils.changedatetype(date, "YYYY-MM-DD HH:mm:ss");
        this.timeoutdate = outtime;
        return;
      }
    },
    WxConfig() {
      var url = "https://qtyywx.qtyy.com/cfNowPay";
      var that = this;
      ajax
        .get(apiUrls.GetWxConfig + "?url=" + url.toString())
        .then(r => {
          var Data = r.data.returnData;
          // console.log(Data);
          console.log(r);
          that.$wechat.config({
            debug: false, // 开启调试模式,开发时可以开启
            appId: Data.appid, // 必填，公众号的唯一标识   由接口返回
            timestamp: Data.timeStamp, // 必填，生成签名的时间戳 由接口返回
            nonceStr: Data.nonceStr, // 必填，生成签名的随机串 由接口返回
            signature: Data.Signature, // 必填，签名 由接口返回
            jsApiList: ["checkJsApi", "chooseWXPay"]
          });
        })
        .catch(e => {
          toolsUtils.alert(e.toString());
        });
    },
    WxPay() {
      var money = this.payamout / 100;

      var user = JSON.parse(storage.session.get("user"));
      var cfList = JSON.parse(storage.session.get("cfList"));
      var that = this;
      //debugger;
      var data = {
        openid: user.accountCode,
        orderid: cfList[0].orderid,
        payamout: cfList[0].payAmount
      };
      console.log(data);
      ajax
        .post(apiUrls.UpdateRegPatOrders, data)
        .then(r => {
          r = r.data;
          console.log(r.returnData);
          if (!r.success) {
            toolsUtils.alert(r.returnMsg);
            //失败后跳转处方缴费页面重新生成订单
            this.$router.push({
              path: "/cfList"
            });
            return;
          }
          if (r.returnData == null) {
            alert("网络异常！" + r.returnMsg);
            return;
          }
          //  console.log(money);
          //  that.$wechat.chooseWXPay({
          //  timestamp:r.returnData.timeStamp,
          //  nonceStr:r.returnData.nonceStr,
          //  package:r.returnData.package,
          //  signType:'MD5',
          //  paySign:r.returnData.paySign,
          //  success:function(res){
          //   //  storage.session.delete('drInfo');
          //    that.$router.push({path:"/chargeSuccess",query:{money:that.paymoney/100}})
          //      return;
          //  }
          // })
          if (typeof WeixinJSBridge == "undefined") {
            alert("请在微信端打开!" + JSON.stringify(WeixinJSBridge));
            return;
          }
          WeixinJSBridge.invoke(
            "getBrandWCPayRequest",
            {
              appId: r.returnData.appid, //公众号名称，由商户传入
              timeStamp: r.returnData.timeStamp, //时间戳
              nonceStr: r.returnData.nonceStr, //随机串
              package: r.returnData.package, //扩展包
              signType: "MD5", //微信签名方式:MD5
              paySign: r.returnData.paySign //微信签名
            },
            function(res) {
              switch (res.err_msg) {
                case "get_brand_wcpay_request:cancel":
                  alert("取消支付");
                  break;
                case "get_brand_wcpay_request:fail":
                  alert(
                    "支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。"
                  );
                  break;
                case "get_brand_wcpay_request:ok":
                  that.paysuccess();
                  break;
              }
            }
          );
        })
        .catch(e => {
          toolsUtils.alert(e.toString());
        });
    },

    cancelFun() {
      var data = {
        orderid: this.orderid
      };
      if (
        !confirm(
          "请注意，已经支付的订单不要点击取消，会导致支付订单失效！是否确认取消？"
        )
      ) {
        return;
      }
      //debugger
      ajax.post(apiUrls.cfCancelRegOrders, data).then(r => {
        var r = r.data;
        if (r.success != true) {
          toolsUtils.alert(r.returnMsg);
          return;
        }
        toolsUtils.alert("取消订单成功！");
        this.$router.push("cfregisterRecord");
      });
    },
    //成功后调用
    paysuccess() {
      alert("正在处理订单...");
      storage.session.delete("drInfo");

      var that = this;
      //10秒后回调
      setTimeout(function() {
        $.ajax({
          type: "get",
          url:
            baseData.apiHost.replace(
              "/api",
              "/HisNotify/PatOrdersPayNotify?isupdate=true&orderno="
            ) + that.orderid,
          data: {},
          success: function(data) {
            console.log(data);
          },
          error: function(e) {
            console.log(e);
          }
        });
      }, 10000);

      this.$router.push({
        path: "/chargeSuccess",
        query: { money: this.paymoney / 100, orderid: this.orderid, iscf: "1" }
      });
      return;
      // alert("正在查询订单...");
      // var data={orderno:this.orderid};
      // ajax.post(baseData.apiHost.replace('/api','/')+"HisNotify/regPatPayNotifyUrl",data).then(r=>
      // {
      //    r=r.data;
      //  if(!r.success)
      //  {
      //       alert(r.returnMsg);
      //       this.$router.replace({path:"/cfregisterRecord"});
      //       return;
      //  }
      //   storage.session.delete('drInfo');
      //   this.$router.push({path:"/chargeSuccess",query:{money:this.paymoney/100}})
      // }).catch(e=>{alert(e.toString());return;})
    },
    closefun() {
      // this.paysuccess();
      this.$router.go(-1);
      return;
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 80px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
  position: fixed;
  bottom: 10px;
  width: 95%;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #000;
  border: 1px solid #24bab8;
}
.bottom-btn button:first-child {
  background: #fff;
  color: #000;
  margin-right: 0.1rem;
}
.bottom-btn button:last-child {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  margin-left: 0.1rem;
}
.pay-methodImg {
  height: 0.48rem;
}
</style>
