<template>
  <div v-if="!canload" class="page-loading">
    <van-loading />

    <van-loading type="spinner" />
  </div>
  <!--首页-->
  <div class="success-page" v-else>
    <!-- head -->
    <div class="success-head">
      <img src="../../assets/chongzhichenggong.png" alt="" />
      <h3>{{ listData.isPay }}</h3>
    </div>
    <!-- content -->
    <div class="success-content">
      <div class="list-wrap">
        <p>支付方式</p>
        <span>{{ listData.payCode }}</span>
      </div>
      <!-- <div class="list-wrap">
                <p>缴费金额</p>
                <span>{{listData.payMoney}}元</span>
            </div> -->
      <!-- <div class="list-wrap">
                <p>当前余额</p>
                <span>{{listData.nowMoney}}</span>
            </div> -->
      <!-- <div class="list-wrap">
                <p>交易流水号</p>
                <span>{{listData.flowNum}}</span>
            </div> -->
      <div class="list-wrap">
        <p>交易时间</p>
        <span>{{ listData.tradTime }}</span>
      </div>
    </div>
    <!-- button -->
    <div class="footer-btn">
      <!-- <button><router-link to="/">完成</router-link></button> -->
      <button @click="close()"><font color="white">完成</font></button>
    </div>
  </div>
</template>

<script>
import { ajax, storage, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "index",
  components: {
    // HeaderBar
  },
  data() {
    var now = new Date();
    return {
      canload: false,
      title: "首页",
      IsBack: false,
      orderid: "",
      listData: {
        isPay: "支付成功",
        payCode: "医保支付",
        tradTime: toolsUtils.getNow()
      }
    };
  },
  created() {
    setTimeout(() => {
      this.getOrderState();
    }, 5000);
  },
  methods: {
    getOrderState() {
      if (this.$route.query.orderid != null) {
        this.orderid = this.$route.query.orderid;
        var pdata = { orderid: this.orderid };
        // alert("查询医保订单号：" + this.$route.query.orderid);
        var that = this;
        ajax
          .post(apiUrls.GetpatientOrderInfo, pdata)
          .then(r => {
            var data = r.data;
            if (!data.success) {
              toolsUtils.alert(data.returnMsg);
              this.listData.isPay = "订单错误";
              return;
            }
            that.orderinfo = data.returnData;
            var datas = [
              {
                status: that.orderinfo.status
              }
            ];
            // alert("订单状态" + datas[0].status);
            //如果订单状态为6(异常订单) 则显示异常信息
            if (datas[0].status == 6) {
              alert(datas[0].hisMsg);
              this.listData.isPay = "订单错误";
            }
            //如果订单状态为2(已支付) 则显示支付成功
            if (datas[0].status == 2) {
              this.listData.isPay = "支付成功";
            }
            //其余订单显示支付失败
            else if (datas[0].status != 6 && datas[0].status != 2) {
              this.listData.isPay = "支付失败";
            }
            this.canload = true;
            return;
          })
          .catch(e => {
            toolsUtils.alert("程序异常:" + JSON.stringify(e));
          });
      } else {
        this.listData.isPay = "订单错误";
        this.canload = true;
      }
    },
    // selectorder(){
    //   //查询医保支付单状态接口
    //   var pdata =
    //   {
    //     appid: "",//微信分配的公众账号 ID
    //     mch_id: "", //微信支付分配的商户号
    //     med_trans_id: yborderid,//3.3返回
    //     hosp_out_trade_no: cfList[0].orderid,//第三方服务商平台自动生成的一个订单号
    //   }
    //   ajax
    //     .post(apiUrls.unifiedOrder,pdata)
    //     .then(r=>{
    //       if(r.success){
    //         switch(r.returnData.insurance_trade_status){
    //           case "SUCCESS":
    //             console.log("支付成功")
    //             break;
    //           case "REFUND":
    //             console.log("转入退款")
    //             break;
    //           case "PAYING":
    //             console.log("支付中")
    //             break;
    //           case "SYS_REFUNDED":
    //             console.log("系统退款")
    //             break;
    //           case "SYS_REFUNDING":
    //             console.log("系统退款中")
    //             break;
    //           case "NOTPAY":
    //             console.log("未支付")
    //             break;
    //           case "CLOSED":
    //             console.log("已关闭")
    //             break;
    //           case "PAYERROR":
    //             console.log("支付失败")
    //             break;
    //           case "INITIAL":
    //             console.log("未绑卡")
    //             break;
    //           default :
    //             break;
    //         }
    //       }
    //     })
    //     .catch(e=>{
    //       toolsUtils.alert("网络异常");
    //       console.log(e);
    //       return
    //     })
    // },
    close() {
      WeixinJSBridge.call("closeWindow");
      return;
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
.page-loading {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  > * {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.success-page {
  padding: 0.2rem;
  background: #f0eff6;
  height: 100%;
  width: 100%;
  overflow: auto;
}
.success-head {
  padding: 0.36rem 0 0.44rem 0;
}
.success-head img {
  width: 1.52rem;
  height: 1.52rem;
  display: block;
  margin: 0 auto 0.2rem;
}
.success-head h3 {
  text-align: center;
  font-size: 0.4rem;
  color: #354052;
  font-weight: normal;
}
.success-content {
  width: 100%;
  background: #fff;
  border-radius: 5px;
  padding: 0.4rem 0.28rem;
  margin-bottom: 0.48rem;
}
.success-content .list-wrap {
  display: flex;
  height: 0.44rem;
  align-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.success-content .list-wrap:last-child {
  margin-bottom: 0;
}
.success-content .list-wrap span {
  color: #354052;
  flex-grow: 1;
  text-align: right;
}
.footer-btn button {
  width: 100%;
  height: 1.08rem;
  border-radius: 5px;
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  border: 1px solid #24bab8;
  display: block;
  font-size: 0.36rem;
  color: #ffffff;
}
</style>
