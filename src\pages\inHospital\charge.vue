<template>
  <!--首页-->
  <!-- <div>
        {{title}}
    </div> -->
  <div class="charge_page">
    <!--充值金额-->
    <div class="moneyWrap">
      <div class="charge-obj">
        充值对象<span>{{ user }}</span>
      </div>
      <h3>
        缴费金额
        <span>￥{{ checkNum }}</span>
      </h3>
      <div class="allMoneyWrap">
        <div class="wait-money" @click="clickAll()">
          待缴金额 ¥{{ paymentMoney }}<a>全部缴费</a>
        </div>
        <div class="moneyNum cl">
          <div
            v-for="(item, index) in moneyData.checkMoney"
            v-bind:key="index"
            @click="checkFee(item.money, index)"
          >
            <span :class="indexMoney === index ? 'active' : ''"
              >{{ item.money }}元</span
            >
          </div>
        </div>
      </div>
    </div>
    <!--充值方式-->
    <div class="moneyWrap">
      <div class="charge-obj">缴费方式</div>
      <div class="allMoneyWrap modeWrap">
        <div class="payMode">
          <div class="modeGroup">
            <input type="radio" style="display: none;" id="weixin" checked />
            <label class="flex" for="weixin">
              <span class="modeImg"
                ><img src="../../assets/weixinzhifu.png" alt=""
              /></span>
              <span class="modeMethod">微信支付</span>
            </label>
          </div>
        </div>
        <div></div>
      </div>
    </div>
    <button class="confirmBtn" @click="WxPay()">确认支付</button>
  </div>
</template>

<script>
import { ajax, toolsUtils, storage } from "../../common";
import apiUrls from "../../config/apiUrls";

export default {
  data() {
    return {
      title: "首页",
      IsBack: false,
      indexMoney: "",
      checkNum: "",
      paymentMoney: "",
      user: "",
      //充值金额
      moneyData: {
        checkMoney: [
          {
            money: "100"
          },
          {
            money: "200"
          },
          {
            money: "500"
          },
          {
            money: "1000"
          },
          {
            money: "2000"
          },
          {
            money: "5000"
          }
        ]
      }
    };
  },
  mounted() {
    this.WxConfig();
  },
  methods: {
    saftTologin: function() {
      this.$router.push({ path: "/login" });
      return;
    },

    //获取充值对象和待缴费金额
    userMoney() {
      var info = JSON.parse(storage.session.get("user"));
      this.paymentMoney = parseFloat(
        this.$route.query.paymentMoney / 100
      ).toFixed(2);
      this.checkNum = parseFloat(this.$route.query.paymentMoney / 100).toFixed(
        2
      );
      this.user = info.name;
    },
    //选择充值的金额
    checkFee(money, index) {
      this.checkNum = money;
      this.indexMoney = index;
    },

    WxConfig() {
      var url = "https://qtyywx-api.qtyy.com/charge";
      var that = this;
      ajax
        .get(apiUrls.GetWxConfig + "?url=" + url.toString())
        .then(r => {
          var Data = r.data.returnData;
          that.$wechat.config({
            debug: false, // 开启调试模式,开发时可以开启
            appId: Data.appid, // 必填，公众号的唯一标识   由接口返回
            timestamp: Data.timeStamp, // 必填，生成签名的时间戳 由接口返回
            nonceStr: Data.nonceStr, // 必填，生成签名的随机串 由接口返回
            signature: Data.Signature, // 必填，签名 由接口返回
            jsApiList: ["checkJsApi", "chooseWXPay"]
          });
        })
        .catch(e => {
          toolsUtils.alert(e.toString());
        });
    },
    tz() {
      this.$router.push("/chargeSuccess");
    },
    WxPay() {
      var CardInfo = JSON.parse(storage.session.get("admissionCard"));
      var user = JSON.parse(storage.session.get("user"));
      var that = this;
      if (this.checkNum == "0") {
        toolsUtils.alert("请选择金额！");
        return;
      }
      storage.session.delete("pydata");
      var data = {
        branchCode: CardInfo.branchCode,
        admissionNo: CardInfo.admissionNo,
        patientId: CardInfo.patientNo,
        inTimes: CardInfo.inTimes,
        payAmout: this.checkNum * 100,
        accountCode: user.accountCode,
        idCard: user.idCard
      };

      if (data.payAmout == "0.00") {
        alert("请核对金额");
      } else {
        ajax
          .post(apiUrls.AddOrders, data)
          .then(r => {
            r = r.data;
            data = r.returnData;
            if (!r.success) {
              toolsUtils.alert(r.returnMsg);
              return;
            }
            var money = that.checkNum;
            this.$wechat.chooseWXPay({
              timestamp: data.timeStamp,
              nonceStr: data.nonceStr,
              package: data.package,
              signType: "MD5",
              paySign: data.paySign,
              success: function(res) {
                that.$router.push({
                  path: "/chargeSuccess",
                  query: { money: money }
                });
                return;
              }
            });
            ``;
          })
          .catch(e => {
            toolsUtils.alert("支付失败");
          });
      }
    },
    //点击全部缴费
    clickAll() {
      this.checkNum = this.paymentMoney;
    }
  },
  computed: {},
  created() {
    this.userMoney();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
@import url("../../assets/charge.css");
.charge-obj {
  font-size: 0.32rem;
  color: #7f8fa4;
  height: 1.08rem;
  background: #fdfbfb;
  border-radius: 4px;
  line-height: 1.08rem;
  padding: 0 0.24rem;
}
.charge-obj span {
  font-size: 0.4rem;
  color: #354052;
  margin-left: 0.28rem;
}
.wait-money {
  font-size: 0.28rem;
  color: #7f8fa4;
  margin-top: -0.1rem;
  margin-bottom: 0.24rem;
}
.wait-money a {
  color: #6895ff;
  margin-left: 0.16rem;
}
</style>
