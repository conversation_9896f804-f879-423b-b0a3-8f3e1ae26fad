<template>
  <div class="hs-news">
    <div class="content-wrap">
      <div class="content-head">
        <div class="head-icon">
          <img src="../../assets/yiriqingdan.png" alt="" />
        </div>
        <span class="">一日清单</span>
        <div @click="showPicker = true">{{ dateValue }}</div>
        <van-popup v-model="showPicker" position="bottom" round>
          <van-datetime-picker
            v-model="currentDate"
            type="date"
            title="选择年月日"
            :max-date="maxDate"
            @confirm="onConfirm"
            @cancel="showPicker = false"
          />
        </van-popup>
      </div>
      <div class="list-wrap">
        <ul>
          <li
            v-for="(list_1, index) in listData"
            :key="index"
            @click="listClick(index)"
          >
            <div class="list-item-1">
              <div class="date-wrap">
                {{ list_1.costDate
                }}<span>{{ todayDate(list_1.costDate) }}</span>
              </div>
              <div class="money-wrap">
                <span
                  >￥{{ parseFloat(list_1.todayAmout / 100).toFixed(2) }}</span
                >
                <img
                  :class="activeFun === index ? 'rotate_90' : ''"
                  src="../../assets/more.png"
                  alt=""
                />
              </div>
            </div>
            <div
              class="list-item-2"
              :class="activeFun === index ? 'show' : ''"
              @click.stop
            >
              <div
                class="item-2-group"
                v-for="(list_2, key) in list_1.costlist"
                :key="key"
              >
                <div>
                  <div>
                    {{ list_2.costTypeName }}（￥{{
                      parseFloat(list_2.costAmout / 100).toFixed(2)
                    }}）
                  </div>
                  <a
                    @click="
                      transmitData(
                        list_1.cid,
                        list_2.costTypeId,
                        list_2.costAmout
                      )
                    "
                    >查询详情</a
                  >
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  components: {},
  data() {
    return {
      msg: "Hello World!",
      indexNum: "",
      // 清单列表数据
      allDate: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      listData: [],
      dateValue: "选择日期",
      showPicker: false,
      currentDate: new Date(),
      maxDate: new Date()
    };
  },
  methods: {
    // 展开列表的点击事件
    listClick(index) {
      if (this.indexNum === index) {
        return (this.indexNum = "");
      } else {
        return (this.indexNum = index);
      }
    },
    //-----------
    detailsList(value) {
      var info = JSON.parse(storage.session.get("admissionCard"));
      var indexDate = new Date(),
        year = indexDate.getFullYear(),
        money = indexDate.getMonth() + 1,
        day = indexDate.getDate();
      var newDate =
        year +
        "-" +
        (money < 10 ? "0" + money : money) +
        "-" +
        (day < 10 ? "0" + day : day);
      var data = {
        branchCode: "",
        admissionCardNo: info.admissionNo,
        patientId: info.patientNo,
        inTimes: info.inTimes,
        time: value
      };
      console.log(data);
      ajax.post(apiUrls.GetPerBedFeeList, data).then(r => {
        var r = r.data;
        console.log(r);
        if (r.success == false) {
          toolsUtils.alert(r.returnMsg);
          return;
        }
        this.listData = r.returnData;
        //  console.log(this.listData)
      });
    },
    //转化成星期
    todayDate(index) {
      var today = new Date(index);
      return this.allDate[today.getDay()];
    },
    //传递数据到详情页
    transmitData(cid, costTypeId, money) {
      debugger;
      costTypeId = costTypeId.trim();
      console.log(cid);
      var data = {
        cid,
        costTypeId,
        money
      };
      this.$router.push({
        path: "/details",
        query: {
          data
        }
      });
    },
    onConfirm(value) {
      let year = value.getFullYear();
      let month = value.getMonth() + 1;
      let day = value.getDate();
      value =
        year +
        "-" +
        (month < 10 ? "0" + month : month) +
        "-" +
        (day < 10 ? "0" + day : day);
      this.dateValue = value;
      this.detailsList(this.dateValue);
      this.showPicker = false;
    }
  },
  computed: {
    activeFun: function() {
      return this.indexNum;
    }
  },
  created() {
    this.maxDate = new Date(this.maxDate.getTime() - 24 * 60 * 60 * 1000);
    let date = new Date();
    date = new Date(date.getTime() - 24 * 60 * 60 * 1000);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    date =
      year +
      "-" +
      (month < 10 ? "0" + month : month) +
      "-" +
      (day < 10 ? "0" + day : day);
    this.detailsList(date);
  }
};
</script>

<style scoped>
@import url("../../assets/tabCurrency.css");
.list-wrap ul li {
  overflow: hidden;
  position: relative;
}
.list-wrap ul li .list-item-1 {
  display: flex;
  height: 1.08rem;
  align-items: center;
  border-bottom: 1px solid #dfe3e9;
  font-size: 0.32rem;
  color: #7f8fa4;
  padding: 0 0.2rem;
  background: #fff;
}
.list-wrap ul li .list-item-1 .date-wrap span {
  margin-left: 0.24rem;
}
.list-wrap ul li .list-item-1 .money-wrap {
  flex-grow: 1;
  text-align: right;
  display: flex;
  align-items: center;
}
.list-wrap ul li .list-item-1 .money-wrap img {
  height: 0.3rem;
  margin-left: 0.1rem;
}
.list-wrap ul li .list-item-1 .money-wrap span {
  flex-grow: 1;
}
.list-item-2 {
  display: none;
}
.item-2-group {
  background: #fdfbfb;
  height: 1.08rem;
  padding-left: 0.6rem;
}
.item-2-group > div {
  display: flex;
  align-items: center;
  font-size: 0.32rem;
  color: #7f8fa4;
  border-bottom: 1px solid #dfe3e9;
  height: 100%;
  padding-right: 0.6rem;
}
.item-2-group > div div {
  flex-grow: 1;
}

.item-2-group > div a {
  font-size: 0.28rem;
  color: #f6912c;
}
.show {
  display: block;
}
.rotate_90 {
  transform: rotate(90deg);
}
</style>
