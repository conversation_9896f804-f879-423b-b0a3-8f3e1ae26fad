<template>
  <!--清单详情页-->
  <div class="about-time-page">
    <div class="about-time">
      <div class="tip">
        预约挂号成功，就诊时间{{ tipDate
        }}{{
          tipTime[1] !== ""
            ? `${tipTime[0]}点～${tipTime[1]}点`
            : `${tipTime[0]}`
        }}，请在当天{{
          tipTime[1] !== "" ? `${tipTime[0]}点` : `${tipTime[0]}`
        }}前完成报到，如无法就诊请提早一天取消。报到地点:{{
          deptName
        }}(需携带就诊卡/号和门诊病历)。
      </div>
      <dingdanxinxi @paytype="getPaytype"></dingdanxinxi>
      <div class="list-group">
        <group class="cell-group">
          <cell title="支付方式" value="微信支付">
            <img
              slot="icon"
              class="pay-methodImg"
              style="display:block;margin-right:5px;"
              src="../../assets/weixinzhifu.png"
            />
          </cell>
        </group>
      </div>

      <!-- <p><img src="../../assets/warn.png" alt="">温馨提示：请在30分钟内完成支付，逾期号将会作废，需重新挂号</p> -->
      <!--status==8 是挂号没有支付的版本-->
      <!-- <div
        v-if="status == 8 && isShow"
        class="bottom-btn"
        style="margin-bottom:10px"
      >
        <button @click="qusetion()">问卷调查</button>
      </div> -->
      <div v-if="status == 2 || (status == 8 && isShow)" class="bottom-btn">
        <button @click="cancelFun()">取消挂号</button>
      </div>
    </div>
    <AuthorizationLetter
      @cancel="onCancel"
      @agree="onAgree"
      v-if="isShowAuthorizationLetter"
      class="authorization-letter"
    ></AuthorizationLetter>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
import { ajax, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import dingdanxinxi from "../../components/outpatient/dingdanxinxi";
import AuthorizationLetter from "../../components/AuthorizationLetter";
export default {
  name: "index",
  components: {
    Cell,
    Group,
    dingdanxinxi,
    AuthorizationLetter
  },
  data() {
    return {
      title: "选择科室",
      pattype: "",
      pactTime: "",
      patCode: "",
      status: 0,
      isShow: true,
      deptName: "",
      tipDate: "",
      tipTime: "",
      isShowAuthorizationLetter: true
    };
  },

  mounted() {
    this.initCancel();
  },
  methods: {
    getPaytype(val) {
      // debugger
      console.log(val);
      this.pattype = val.val;
      this.pactTime = val.Regtime;
      this.patCode = val.patcode;
      this.status = val.status;
      this.deptName = val.deptName;
      let strArr = val.Regtime.split("-");
      let year = strArr[0];
      let month = strArr[1];
      let day = strArr[2];
      this.tipDate = `${year}年${month}月${day}日`;
      this.tipTime = val.time.split(" ");
    },
    cancelFun() {
      //预约挂号线上取消时限为就诊前晚上21:00
      // var ptime = this.pactTime;
      // var dateday = this.getptinfo();
      // var dd = new Date(ptime);
      // dd.setDate(dd.getDate() - 1);
      // var y = dd.getFullYear();
      // var m =
      //   dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
      // var d =
      //   dd.getDate() + 1 < 10 ? "0" + (dd.getDate() + 1) : dd.getDate() + 1;
      // var timeout = y + "-" + m + "-" + d + " " + "21:00:00";
      // if (dateday > timeout) {
      //   this.isShow = false;
      //   toolsUtils.alert("已超过规定取消预约时间");
      //   return;
      // }
      if (this.pattype == "0") {
        //当天预约不允许退款
        this.isShow = false;
        toolsUtils.alert("无法取消当天预约");
        return;
      }
      var data = {
        orderid: this.$route.query.orderid,
        patCardNo: this.patCode
      };
      ajax
        .post(apiUrls.RefundRegOrders, data)
        .then(r => {
          var r = r.data;
          console.log(r);
          if (r.success != true) {
            this.isShow = false;
            toolsUtils.alert(JSON.stringify(r.returnMsg));
            return;
          }
          // toolsUtils.alert('退费成功！');
          toolsUtils.alert("取消成功！");
          setTimeout(function() {
            WeixinJSBridge.call("closeWindow");
          }, 1000);
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    qusetion() {
      window.location.href = "/questions?orderid=" + this.$route.query.orderid;
      return;
    },
    initCancel() {
      var ptime = this.pactTime;
      var dateday = this.getptinfo();
      var dd = new Date(ptime);
      dd.setDate(dd.getDate() - 1);
      var y = dd.getFullYear();
      var m =
        dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
      var d =
        dd.getDate() + 1 < 10 ? "0" + (dd.getDate() + 1) : dd.getDate() + 1;
      var timeout = y + "-" + m + "-" + d + " " + "21:00:00";
      if (dateday > timeout) {
        this.isShow = false;
        return;
      }
      if (this.pattype == "0") {
        //当天预约不允许退款
        this.isShow = false;
        return;
      }
    },
    getptinfo() {
      var day = new Date();
      var month =
        day.getMonth() + 1 < 10
          ? "0" + (day.getMonth() + 1)
          : day.getMonth() + 1;
      var datem = day.getDate() < 10 ? "0" + day.getDate() : day.getDate();
      var daytime =
        day.getFullYear() +
        "-" +
        month +
        "-" +
        datem +
        " " +
        day.getHours() +
        ":" +
        day.getMinutes() +
        ":" +
        day.getSeconds();
      return daytime;
    },
    onCancel() {
      this.isShowAuthorizationLetter = false;
    },
    async onAgree() {
      try {
        const { data } = await ajax.post(apiUrls.ArchiveOauth, {
          orderid: this.$route.query.orderid
        });
        const { returnMsg } = data;
        toolsUtils.alert(returnMsg);
      } catch (error) {
        toolsUtils.alert("程序异常:" + JSON.stringify(e));
      } finally {
        this.isShowAuthorizationLetter = false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.about-time-page {
  height: 100%;
  width: 100%;
  position: relative;
  .about-time {
    height: 100%;
    width: 100%;
    overflow: auto;
    background: #f0eff6;
    padding: 0.24rem;
    .tip {
      background: #fff;
      font-size: 0.32rem;
      margin-bottom: 0.2rem;
      padding: 0.2rem 0.3rem;
      color: #999999;
      border-radius: 0.08rem;
    }
    .about-time .list-group {
      background: #fff;
      border-radius: 5px;
      font-size: 0.32rem;
      overflow: hidden;
      margin-bottom: 0.24rem;
    }
    .weui-cell:first-child::before {
      border: none;
    }
    .weui-cell {
      height: 0.96rem;
      padding: 0 0.3rem;
    }
    .about-time > p {
      font-size: 14px;
      color: #b5b5b5;
      margin: 9px 0 23px;
    }
    .about-time > p img {
      margin-right: 5px;
      vertical-align: middle;
      margin-top: -3px;
    }
    .bottom-btn {
      display: flex;
    }
    .bottom-btn button {
      border-radius: 5px;
      height: 1.08rem;
      flex-grow: 1;
      font-size: 0.36rem;
      display: block;
      color: #fff;
      border: 1px solid #24bab8;
    }
    .bottom-btn button:first-child {
      background: #fff;
      /* color: #24BAB8; */
      margin-right: 0.1rem;
    }
    .bottom-btn button:last-child {
      background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
      margin-left: 0.1rem;
    }
    .pay-methodImg {
      height: 0.48rem;
    }
  }
  .authorization-letter {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #fff;
  }
}
</style>
