<template>
<!--首页-->
    <div class="record-page">
        <!-- 充值记录列表 -->
        <ul>
            <li :key="index" v-for="(item,index) in recordData">
                <div class="left-wrap">
                    <!-- <h3>{{item.payMode}}：56464</h3><span>{{item.date}}</span><span>{{item.time}}</span> -->
                    <h3>{{item.tradeMode==="WECHAT"?'微信支付':'其他支付'}}：{{item.orderNo.substring(item.orderNo.length-7,8)}}</h3><span>{{item.payTime.replace("T"," ").substring(0,16)}}</span>
                </div>
                <div class="right-wrap">￥{{(parseFloat(item.payAmout)/100).toFixed(2)}}</div>
            </li>

        </ul>
    </div>
</template>

<script>
import { ajax, dataUtils, storage, toolsUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "index",
  components: {
    // HeaderBar
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      //   record列表数据
      recordData: []
    };
  },
  methods: {
    getRecord() {
      var that = this;
      var info = JSON.parse(storage.session.get("user")),
        openId = storage.session.get("openid"),
        data = {
          openid: openId, //'oFPKh1b24kYa1C1bwSnJv6DJAoOA',
          id_card: info.idCard //'******************'
        };
      console.log(data);
      ajax
        .post(apiUrls.GetDepositList, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无订单~~",
              onShow() {

              },
              onHide() {
                that.$router.push("/payment");
              }
            });
          }
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          this.recordData = r.returnData;
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    }
  },
  computed: {},
  created() {
    this.getRecord();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.record-page {
  width: 100%;
}
.record-page ul li {
  display: flex;
  align-items: center;
  height: 1.28rem;
  border-bottom: 1px solid #dfe3e9;
  padding: 0 0.3rem;
  background: #fff;
}
.record-page ul li > div {
  flex-grow: 1;
}
.record-page ul li .left-wrap {
  font-size: 0.32rem;
  color: #354052;
}
.record-page ul li .left-wrap h3 {
  font-weight: normal;
  font-size: 0.32rem !important;
  line-height: 0.32rem;
}
.record-page ul li .left-wrap span {
  font-size: 0.28rem;
  color: #7f8fa4;
  margin-right: 0.2rem;
}
.record-page ul li .right-wrap {
  text-align: right;
  font-size: 0.4rem;
  color: #354052;
  font-weight: normal;
}
</style>

