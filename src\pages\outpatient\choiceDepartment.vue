<template>
  <!--清单详情页-->
  <div class="choice-department">
    <div class="header">
      <a @click="goBackFun()"><img src="../../assets/more.png" alt="" />返回</a>
      <span>{{ webtag }}</span>
    </div>
    <!--侧边框 -->
    <div class="big-box">
      <div class="left">
        <div
          :class="{
            Tips_left: true,
            divcolor: Code == itemlist.DeptParentClassify.parentCode
          }"
          v-for="(itemlist, index) in depList"
          :key="index"
          @click="ListBtn(itemlist.DeptParentClassify.parentCode)"
        >
          {{ itemlist.DeptParentClassify.parentName }}
        </div>
      </div>
      <div class="right">
        <div
          class="dep-list"
          v-for="(item, demo) in Submenus"
          :key="demo"
          @click="
            checkDep(item.DeptClassify.deptCode, item.DeptClassify.deptName)
          "
        >
          <span>
            {{ item.DeptClassify.deptNameFirst }}<br />{{
              item.DeptClassify.deptNameSecond
            }}</span
          >
          <img src="../../assets/right.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ajax, toolsUtils, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import baseData from "../../config/baseData";
import index from "../../store";
export default {
  components: {},
  data() {
    return {
      title: "选择科室",
      depList: [],
      Submenus: [],
      deptNameBtn: "内科",
      webtag: baseData.webtag,
      Code: ""
    };
  },
  created() {
    this.getDeptRegInfojsss();
    if (this.$route.query.method == 1) {
      storage.local.set("chooseCode", "02");
      this.Code = "02";
    } else {
      storage.local.set("chooseCode", "01");
      this.Code = "01";
    }
    this.ListBtn();
  },
  // mounted(){
  //   this.getDeptRegInfo();
  // },
  methods: {
    //跳转到科室医生
    checkDep(deptId, deptName) {
      // alert("微信正在修复中...");return;
      var that = this;
      // console.log(deptId,deptName)
      storage.session.set("deptName", deptName);
      // window.location.href="/choiceDoctor?method="+this.$route.query.method+"&deptId="+deptId;
      this.$router.push({
        path: "/choiceDoctor",
        query: {
          method: this.$route.query.method,
          deptId: deptId
        }
      });
      // this.$router.go(0);
    },
    //返回
    goBackFun() {
      this.$router.back(-1);
    },
    //获取一级科室列表
    getDeptRegInfojsss() {
      // alert("黄码人员不需微信预约缴费，请走黄码通道窗口办理。");
      var patType = this.$route.query.method; //当天挂号为0，预约挂号为1
      //  if(storage.session.get("DeptInfo")!=null)
      //  {
      //     this.depList=JSON.parse(storage.session.get("DeptInfo"));
      //     return;
      //  }
      ajax
        .get(apiUrls.getDeptParentClassify + "?patType=" + patType)
        .then(r => {
          var Data = r.data;
          if (Data.returnData == null) {
            toolsUtils.alert("暂无数据！");
            return;
          }
          storage.session.set("DeptInfo", JSON.stringify(Data.returnData));
          this.depList = Data.returnData;
          if (this.$route.query.method == 1) {
            this.depList = this.depList.filter(
              x => x.DeptParentClassify.parentCode != "01"
            );
          }
          console.log(this.depList);
        })
        .catch(e => {
          //  console.log(e);
          toolsUtils.alert("系统异常！");
        });
    },
    //二级科室
    ListBtn(chooseCode) {
      this.Code = chooseCode;
      if (this.Code == null || this.Code == "") {
        if (
          storage.local.get("chooseCode") == null ||
          storage.local.get("chooseCode") == undefined ||
          storage.local.get("chooseCode") == ""
        ) {
          if (this.$route.query.method != 1) {
            this.Code = "01";
          } else {
            this.Code = "02";
          }
        } else {
          this.Code = storage.local.get("chooseCode");
        }
      }
      storage.local.set("chooseCode", this.Code);
      ajax
        .get(apiUrls.getDeptClassify + "?Code=" + this.Code)
        .then(r => {
          var Data = r.data;
          if (Data.returnData == null) {
            toolsUtils.alert("暂无数据");
            this.Submenus = [];
            return;
          }
          var SubmenusL = Data.returnData;
          // this.Submenus = Data.returnData;

          for (let i = 0; i < SubmenusL.length; i++) {
            var stringState = SubmenusL[i].DeptClassify.deptName;
            var SubmenusMap = SubmenusL[i].DeptClassify;
            if (stringState.indexOf("（") !== -1) {
              var key = "deptNameFirst",
                keys = "deptNameSecond";
              SubmenusMap[key] = stringState.substring(
                0,
                stringState.indexOf("（")
              );
              SubmenusMap[keys] = stringState.substring(
                stringState.indexOf("（"),
                stringState.length
              );
              console.log(SubmenusMap);
            } else {
              var key = "deptNameFirst",
                keys = "deptNameSecond";
              SubmenusMap[key] = stringState;
              SubmenusMap[keys] = "";
            }
          }
          this.Submenus = SubmenusL;
          console.log("this.Submenus", this.Submenus);
        })
        .catch(e => {
          console.log(e);
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.choice-department {
  height: 100%;
  width: 100%;
  background: #fff;
  overflow: auto;
}
.choice-department .header {
  display: flex;
  height: 1.04rem;
  background: #fdfbfb;
  font-size: 0.32rem;
  color: #354052;
  align-items: center;
  padding: 0 0.3rem;
}
.choice-department .header a {
  color: #a1a1a1;
}
.choice-department .header a img {
  height: 0.4rem;
  transform: rotate(180deg);
  vertical-align: middle;
  margin-top: -3px;
  margin-right: 5px;
}
.choice-department .header span {
  flex-basis: 0;
  flex-grow: 1;
  text-align: right;
}
.choice-department .dep-list {
  padding: 0 0.3rem;
}
.choice-department .dep-list > div {
  height: 0.96rem;
  width: 6.9rem;
  border-bottom: 1px solid #dfe3e9;
  font-size: 0.28rem;
  color: #354052;
  display: flex;
  align-items: center;
}
.choice-department .dep-list > div:last-child {
  border: none;
}
.choice-department .dep-list > div span {
  flex-basis: 0;
  flex-grow: 1;
}
.choice-department .dep-list > div img {
  height: 0.4rem;
  display: block;
}
/* 二级菜单 */
.big-dep {
  width: 100%;
  /* background: red; */
  background: #fdfbfb;
  display: flex;
  flex-direction: row;
}
.left {
  width: 24%;
  height: 100%;
  margin-left: 16px;
}
.Tips_left {
  width: 100%;
  height: 1rem;
  border-right: 1px solid #dfe3e9;
  font-size: 0.28rem;
  display: flex;
  align-items: center;
}
.right {
  width: 86%;
  height: 11rem;
  overflow-y: auto;
  font-size: 20px;
}
.big-box {
  width: 100%;
  display: flex;
  flex-direction: row;
}
.dep-list {
  width: 100%;
  min-height: 0.96rem;
  border-bottom: 1px solid #dfe3e9;
  font-size: 0.28rem;
  color: #354052;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.divcolor {
  background: #accadf;
}
</style>
