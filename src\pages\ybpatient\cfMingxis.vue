<template>
  <!--清单详情页-->
  <div class="about-time">
    <chufangInfo></chufangInfo>
    <div class="list-group">
      <!-- <ul class="list-ul">
             <li :key="index" v-for="(list,index) in cfListData">
                <div class="item-1" @click="showFun(index)">
                    <span>{{list.detailFee+':'+list.detailName}}</span>
                    <img src="../../assets/down.png" alt="" :class="list.show?'img-rotate':''">
                </div>
                <div class="item-2" :class="list.show?'':'item-2_toggle'">
                    <div v-if="list.detailSpec!=null">规格:<span>{{list.detailSpec}}</span></div>
                    <div>单价:<span>{{String(list.detailPrice/100).replace(/^(.*\..{4}).*$/,"$1")}}</span></div>
                    <div>数量:<span>{{list.detailCount}}</span></div>
                    <div>单位:<span>{{list.detailUnit}}</span></div>
                </div>
             </li>
           </ul> -->
    </div>
    <group>
      <radio
        title="支付方式"
        :options="options"
        @on-change="payMode == 'WECHAT' ? bindYBUrl() : null"
        v-model="payMode"
        ref="test1"
      ></radio>
    </group>

    <div class="bottom-btn">
      <button @click="submitFun()" v-dbClick>提交订单</button>
    </div>
  </div>
</template>

<script>
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Cell, Group, Radio } from "vux";
import chufangInfo from "../../components/outpatient/chufangInfo";

export default {
  name: "index",
  components: {
    Cell,
    Group,
    Radio,
    chufangInfo
  },
  data() {
    return {
      title: "选择科室",
      show: [],
      cfListData: [],
      checkimg: require("../../assets/weixinzhifu.png"),
      payMode: "",
      isType: "",
      options: [
        {
          icon: require("../../assets/weixinzhifu.png"),
          value: "自费支付",
          key: "WECHAT"
        },
        {
          icon: require("../../assets/yibaozhifu.png"),
          value: "医保支付",
          key: "YBPAY"
        }
      ],
      requestcontent: []
    };
  },
  methods: {
    getHashParams() {
      return new Promise((resolve, reject) => {
        //获取HashParams参数的接口
        var patcard = JSON.parse(storage.session.get("patcard")),
          visitId = JSON.parse(storage.session.get("cfList"))[0].visitId,
          HashParams = {};

        ajax
          .post(apiUrls.GetHisCfDetails, JSON.stringify(visitId))
          .then(r => {
            HashParams = {
              FPHM: r.data.returnData[0].prescription.FPHM,
              JBR: r.data.returnData[0].prescription.JBR,
              CFH: visitId,
              YYRYKS: r.data.returnData[0].prescription.YYRYKS,
              GMSFHM: r.data.returnData[0].prescription.GMSFHM,
              YLFYZE: r.data.returnData[0].prescription.YLFYZE,
              ZYH: r.data.returnData[0].prescription.ZYH,
              JZRQ: r.data.returnData[0].prescription.JZRQ,
              YYBH: r.data.returnData[0].prescription.YYBH,
              CYZD: r.data.returnData[0].prescription.CYZD,
              CYBQDM: r.data.returnData[0].prescription.CYBQDM,
              BZ: r.data.returnData[0].prescription.BZ || "",
              LXDH: r.data.returnData[0].prescription.LXDH || "",
              JZYYBH: r.data.returnData[0].prescription.JZYYBH
            };
            resolve(HashParams);
          })
          .catch(e => {
            reject();
          });
      });
    },
    getMZCFXMDR() {
      //获取MZCFXMDR参数的接口
      return new Promise((resolve, reject) => {
        var patcard = JSON.parse(storage.session.get("patcard")),
          detailId = JSON.stringify(this.$route.query.recipeId),
          visitId = JSON.parse(storage.session.get("cfList"))[0].visitId,
          MZCFXMDR = [];

        ajax
          .post(apiUrls.GetHisCfDetail, detailId)
          .then(r => {
            var data = r.data.returnData;
            for (var i = 0; i < r.data.returnData.length; i++) {
              MZCFXMDR[i] = {
                ZYH: r.data.returnData[i].rescription.zyh, //挂号编号
                CFH: visitId, //处方号
                GMSFHM: patcard.patIdCard, //身份证
                JZLB: r.data.returnData[i].rescription.jzlb, //就诊类别
                FYRQ: r.data.returnData[i].rescription.fyrq, //费用日期
                XMXH: r.data.returnData[i].rescription.xmxh, //医院项目编号
                XMBH: r.data.returnData[i].rescription.xmbh, //医院项目编号
                XMMC: r.data.returnData[i].rescription.xmmc, //项目名称
                JG: r.data.returnData[i].rescription.jg, //单价
                MCYL: r.data.returnData[i].rescription.mcyl, //数量
                JE: r.data.returnData[i].rescription.je, //金额
                ZFBL: r.data.returnData[i].rescription.zfbl, //自费比例
                YSGH: r.data.returnData[i].rescription.ysgh, //医师工号
                BZ: r.data.returnData[i].rescription.bz || "", //备注
                FHXZBZ: r.data.returnData[i].rescription.fhxzbz //符合限制标志
              };
            }
            resolve(MZCFXMDR);
            // data.forEach(function (item, index){
            //   console.log(item.rescription)
            //   MZCFXMDR[index] =
            //   {
            //     ZYH:item.rescription.zyh,//挂号编号
            //     CFH:item.rescription.cfh,//处方号
            //     GMSFHM:patcard.patIdCard,//身份证
            //     JZLB:item.rescription.jzlb,//就诊类别
            //     FYRQ:item.rescription.fyrq,//费用日期
            //     XMXH:item.rescription.xmxh,//医院项目编号
            //     XMBH:item.rescription.xmbh,//医院项目编号
            //     XMMC:item.rescription.xmmc,//项目名称
            //     JG:item.rescription.jg,//单价
            //     MCYL:item.rescription.mcyl,//数量
            //     JE:item.rescription.je,//金额
            //     ZFBL:item.rescription.zfbl,//自费比例
            //     YSGH:item.rescription.ysgh,//医师工号
            //     BZ:item.rescription.bz,//备注
            //     FHXZBZ:item.rescription.fhxzbz//符合限制标志
            //   }
            // });
          })
          .catch(e => {
            reject();
            toolsUtils.alert("未获取到订单信息");
          });
      });
    },
    bindYBUrl() {
      this.payMode = "YBPAY";
    },
    submitFun() {
      var cfList = JSON.parse(storage.session.get("cfList"))[0],
        openid = storage.session.get("openid"),
        patcard = JSON.parse(storage.session.get("patcard")),
        user = JSON.parse(storage.session.get("user")),
        ListDatastr = JSON.stringify(this.cfListData),
        data = {
          openid: openid,
          id_card: user.idCard,
          patCardNo: patcard.patCode,
          orderid: cfList.orderid,
          payAmout: cfList.payAmount,
          visitId: cfList.visitId,
          settleMethodId: cfList.settleMethodId,
          settleMethodName: cfList.settleMethodName,
          patientidpid: cfList.patientidpid,
          deptName: cfList.deptName,
          deptId: cfList.deptId,
          doctorId: cfList.doctorId,
          doctorName: cfList.doctorName,
          patName: cfList.patName,
          patTel: user.telePhone,
          detailId: this.$route.query.recipeId,
          listDatastr: ListDatastr,
          payMode: this.payMode
        };
      storage.session.set("cfMingxis", JSON.stringify(data));
      if (this.payMode == "YBPAY") {
        if (data.settleMethodId == "0101") {
          alert("该处方仅支持自费支付，请选重新选择支付方式");
          return;
        } else {
          var datas = {
            visitId: data.visitId,
            type: "1", //锁定处方
            detailId: data.detailId
          };
          ajax
            .post(apiUrls.IsLockPatOrder, datas)
            .then(r => {
              console.log(r);
              var r = r.data;
              if (r.success == false) {
                toolsUtils.alert(r.returnMsg);
                return;
              }
              window.location.href =
                //正式授权地址
                "https://card.wecity.qq.com/oauth/code?authType=2&isDepart=2&bizType=04107&appid=wx5342950e076a0d26-1&cityCode=441900&channel=AAEVuXphB5vVP6b9SJ0ItMAX&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxsesnoTha%2BR6zWixIskn2P7&orgCodg=H44190100094&orgAppId=1HHHAQOGR0PNE1470B0A00006CDA167C&redirectUrl=https%3A%2F%2Fqtyywx.qtyy.com%2Foauthret";
              //测试授权地址
              //"https://card.wecity.qq.com/oauth/code?authType=2&isDepart=2&bizType=04107&appid=wx5342950e076a0d26-1&cityCode=441900&channel=AAEVuXphB5vVP6b9SJ0ItMAX&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxsesnoTha%2BR6zWixIskn2P7&orgCodg=H44190100094&orgAppId=1HF97JGIB00Q3F60C80A000099C74F23&redirectUrl=http%3A%2F%2Fqtyywx.qtyy.com%2Foauthret";
              return;
            })
            .catch(e => {
              toolsUtils.alert("网络异常");
              console.log(e);
            });
        }
      } else {
        //生成his处方订单
        ajax
          .post(apiUrls.addRegPatPreOrders, data)
          .then(r => {
            r = r.data;
            storage.session.set("cfpayMode", this.payMode);
            storage.session.set("orderid", r.returnData);
            if (r.success == false) {
              toolsUtils.alert(r.returnMsg);
              if (r.returnMsg == "您有未支付订单，请取消或支付上个处方订单") {
                this.$router.push({
                  path: "/cfregisterRecord"
                });
              }
              return;
            }
            this.$router.push({
              path: "/cfnowPays",
              query: {
                orderid: r.returnData,
                detailId: this.$route.query.recipeId
              }
            });
          })
          .catch(e => {
            toolsUtils.alert("程序异常:" + JSON.stringify(e));
          });
      }
    },
    showFun(index) {
      this.cfListData[index].show = !this.cfListData[index].show;
    },
    getNoPayDetailInfo() {
      var data = {
        //patCardNo: this.$route.query.code,
        hospitalId: "",
        visitId: this.$route.query.recipeId,
        deptId: this.$route.query.deptId,
        doctorId: this.$route.query.doctorId
      };
      if (
        data.visitId == null ||
        data.deptId == null ||
        data.doctorId == null
      ) {
        toolsUtils.alert("系统异常，请返回上个页面");
      }
      console.log(data);
      ajax
        .post(apiUrls.getNoPayDetailInfo, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          this.listData = [];
          if (r.success == false) {
            if (
              r.returnMsg ==
              "his接口异常连接数据库失败,错误信息:该患者无未缴费信息"
            ) {
              return;
            }

            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无订单~~"
            });
            return;
          }
          this.cfListData = r.returnData;
          console.log(r.returnData.length);
          var listLength = this.cfListData.length;
          for (var i = 0; i < listLength; i++) {
            this.show.push(true);
          }
          console.log(this.show);
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    },
    getHisCfDetail() {
      //获取订单数据
      Promise.all([this.getHashParams(), this.getMZCFXMDR()])
        .then(res => {
          this.requestcontent = {
            HashParams: res[0],
            DataPackage: {
              MZCFXMDR: res[1]
            }
          };
          console.log(this.requestcontent);
          storage.session.set(
            "requestcontent",
            JSON.stringify(this.requestcontent)
          );
        })
        .catch(err => {
          // 抛出错误信息
          console.log("获取订单数据失败");
          console.log(err);
        });
    },
    getIsMedicalInsurance() {
      // storage.session.set("IsMedicalInsurance", JSON.stringify(true));
      this.isType = storage.session.get("IsMedicalInsurance");
      if (this.isType == "true") {
        this.payMode = "YBPAY";
        // toolsUtils.alert("授权成功");
      } else if (this.isType == "false") {
        this.payMode = "WECHAT";
        //toolsUtils.alert("授权失败，请选择微信支付!");
      } else {
        this.payMode = "WECHAT";
      }
    }
  },
  computed: {},
  created() {
    // this.getNoPayDetailInfo(); //获取订单数据
    // this.getHisCfDetail(); //获取his订单数据
    this.getIsMedicalInsurance(); //获取医保状态
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem 0.24rem 1.56rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 23px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0.24rem;
  background: #f0eff6;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #fff;
  border: 1px solid #24bab8;
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
}
.pay-methodImg {
  height: 0.48rem;
}
.list-ul .item-1 {
  background: #fff;
  display: flex;
  align-items: center;
  height: 0.96rem;
  border-bottom: 1px solid #f5f5f5;
  padding: 0 0.24rem;
}
.list-ul li:last-child .item-1 {
  border: none;
}
.list-ul .item-1 span {
  flex: 1;
}
.list-ul .item-1 img {
  height: 0.4rem;
}
.list-ul .item-2 {
  background: #f5f5f5;
}
.list-ul .item-2 div {
  height: 0.8rem;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
}
.list-ul .item-2 div span {
  flex: 1;
  text-align: right;
}
.list-ul .item-2_toggle {
  display: none;
}
.list-ul .item-1 .img-rotate {
  transform: rotate(180deg);
}
</style>
