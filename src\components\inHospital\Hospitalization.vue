<template>
  <div class="hs-news">
    <div class="content-wrap">
      <div class="content-head">
        <div class="head-icon">
          <img src="../../assets/zhuyuanxinxi.png" alt="" />
        </div>
        <span class="">住院信息</span>
        <p></p>
      </div>
      <div class="hs-content">
        <div>
          主治医生:<span>{{ BedRecords.doctorName | capitalize }}</span>
        </div>
        <!-- <div>住院费用:<span>￥{{BedRecords.totalFee|capitalize}}</span></div> -->
        <!-- <div>住院病房:<span>{{BedRecords.room|capitalize}}</span></div> -->
        <div>
          住院床号:<span>{{ BedRecords.bedNo | capitalize }}</span>
        </div>
        <!-- <div>诊断类型:<span>{{BedRecords.diaType|capitalize}}</span></div> -->
        <!-- <div>危急程度:<span>{{BedRecords.critical|capitalize}}</span></div> -->
        <div>
          入院日期:<span>{{ BedRecords.inDate | capitalize }}</span>
        </div>
        <div>
          入院科室:<span>{{ BedRecords.deptName | capitalize }}</span>
        </div>
        <!-- <div>出院日期:<span>{{BedRecords.outDate|capitalize}}</span></div> -->
        <div>出院日期:<span>暂无信息</span></div>
        <!-- <div class="hs-sketch"><p>病情简述:</p><span>{{BedRecords.sketch|capitalize}}</span></div> -->
        <!-- <div class="hs-sketch hs-remarks"><p>备<i>注:</i></p><span>{{BedRecords.remarks|capitalize}}</span></div> -->
      </div>
    </div>
  </div>
</template>

<script>
import Vheader from "@/components/inHospital/header";
import { storage, ajax, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
import { isNull, isNullOrUndefined } from "util";
export default {
  components: {},
  data() {
    return {
      msg: "Hello World!",
      number: "545464864Tsdhuidsfh",
      BedRecords: {
        cardNo: "",
        doctorName: "",
        totalFee: "",
        room: "",
        bedNum: "",
        diaType: "",
        critical: "",
        inDate: "",
        outDate: "",
        sketch: "",
        remarks: ""
      }
    };
  },
  filters: {
    capitalize: function(value) {
      if (!value) {
        return "暂无信息";
      }
      return value;
    }
  },
  mounted: function() {
    this.getBedRecords();
  },
  methods: {
    getBedRecords: function() {
      var info = JSON.parse(storage.session.get("user"));
      if (storage.session.get("admissionCard") == null) {
        toolsUtils.alert("没有住院数据或没有获取到admissionCard!");
      }
      //判断是否有住院信息
      if (storage.session.get("admissionCard") != null) {
        this.BedRecords = JSON.parse(storage.session.get("admissionCard"));
        this.BedRecords.inDate = dataUtils.interceptDate(
          this.BedRecords.inDate
        );
        this.BedRecords.outDate = dataUtils.interceptDate(
          this.BedRecords.outDate
        );
        console.log("🔥不需再获取");
        return;
      }

      //通过诊疗卡或者住院号选择一个进行绑定
      //  if(info.admissionCardNo==null&&info.patCardNo==null)
      //   {
      //       toolsUtils.alert('请绑定住院号')
      //       //这里跳转到绑定住院号信息界面
      //       this.$router.push({ path: "/bindPage" })
      //       return;
      //   }
      //      var data={
      //        admissionCardNo:info.admissionCardNo||'',
      //        patcardNo:info.patCardNo||'',
      //        name:info.name
      //       };
      //     // this.BedRecords.cardNo=info.patCardNo;
      //     ajax.post("/hospitalization/getBedRecord",data).then(r=>{
      //       r=r.data;
      //       if(!r.success)
      //       {
      //         toolsUtils.alert(r.returnMsg);
      //         return;
      //       }
      //     this.BedRecords=r.returnData;
      //     storage.session.set("admissionCard", JSON.stringify(r.returnData));
      //     }).catch(e=>{console.log(e),toolsUtils.alert("程序异常:" + JSON.stringify(e))});

      //切换病人，卡号更新，重新读信息通过卡号
    },
    changeCard: function() {}
  }
};
</script>

<style scoped>
@import url("../../assets/tabCurrency.css");
.hs-news .hs-content {
  padding: 0.2rem;
  display: flex;
  flex-wrap: wrap;
}
.hs-news .hs-content div {
  flex-basis: 50%;
  font-size: 0.32rem;
  color: #354052;
  margin-bottom: 0.28rem;
}
.hs-news .hs-content div span {
  color: #7f8fa4;
  padding-left: 5px;
}
.hs-news .hs-content .hs-sketch {
  flex-basis: 100% !important;
  display: flex;
}
.hs-news .hs-content .hs-sketch span {
  flex-basis: 0;
  flex-grow: 1;
}
.hs-news .hs-content .hs-remarks p i {
  font-style: inherit;
  margin-left: 2em;
}
</style>
