<template>
  <div class="medicalInsurance">
    <div class="user-info">
      <img src="../../assets/user_face.png" alt="" />
      <div class="info">
        <h3>
          <group>
            <popup-picker
              ref="picker"
              class="picker"
              title=""
              :show-name="true"
              @on-change="updateList"
              @on-hide="test"
              v-model="patName"
              :data="patListData"
            ></popup-picker>
          </group>
          <span class="change" @click="change"
            ><van-icon name="exchange" />切换就诊人</span
          >
        </h3>
        <p>医院名称：{{ hospName }}</p>
        <p>诊疗卡号：{{ patCode }}</p>
      </div>
    </div>
    <!-- <div v-if="showInfoSearch"> -->
    <div>
      <h3 class="title">[90110]人员定点信息查询</h3>
      <div>
        <van-field
          readonly
          clickable
          :value="cardType"
          label="证件类型"
          placeholder="请选择证件类型"
          @click="cardTypeClick"
          :disabled="isLogin"
        />
        <van-popup v-model="showCardType" position="bottom">
          <van-picker
            show-toolbar
            :columns="columns"
            @confirm="onConfirmCardType"
            @cancel="showCardType = false"
          />
        </van-popup>
        <van-field
          v-model="idCard"
          label="证件号"
          placeholder="请输入身份证"
          maxlength="18"
          disabled
        />
        <div class="search-btn">
          <van-button color="#24bab8" block round @click="loginClick">{{
            isLogin ? "退出" : "查询"
          }}</van-button>
          <div class="text" v-if="!isLogin">
            本院目前已定点备案:1人<br />剩余可定点数:99998
          </div>
        </div>
      </div>
    </div>
    <div>
      <div v-if="isLogin && infoList.length > 0">
        <div class="list-item" v-for="(item, index) in infoList" :key="index">
          <div>人员编号：{{ item.psn_no }}</div>
          <div>险种类型：职工基本医疗保险</div>
          <div>定点排序号：{{ item.fix_srt_no }}</div>
          <div>定点医药机构编号：{{ item.fixmedins_code }}</div>
          <div>定点医药机构名称：{{ item.fixmedins_name }}</div>
          <div>开始日期：{{ item.begndate }}</div>
          <div>结束日期：{{ item.enddate }}</div>
          <div>待遇申报明细流水号：{{ item.trt_dcla_detl_sn }}</div>
          <div v-if="item.fix_local_type">
            就医点类型：{{ fixLocal[item.fix_local_type] }}
          </div>
          <div v-if="item.fix_main_scd_flag">
            主辅标志：{{ fixMain[item.fix_main_scd_flag] }}
          </div>
          <div>备注：{{ item.memo }}</div>
        </div>
      </div>
      <van-empty
        v-if="isLogin && infoList.length === 0"
        image="search"
        description="暂无数据"
      />
    </div>
    <div class="footer-btn" v-if="isLogin && infoList.length > 0">
      <van-button color="#24bab8" block class="btn-fixed" @click="fixedClick"
        >医保定点</van-button
      >
      <van-button color="#24bab8" block @click="modifyClick"
        >医保修改</van-button
      >
    </div>
  </div>
</template>

<script>
import { PopupPicker, Group, Cell } from "vux";
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import baseData from "../../config/baseData";
export default {
  name: "medicalInsurance",
  components: {
    PopupPicker,
    Group,
    Cell
  },
  data() {
    return {
      hospName: baseData.webtag,
      patName: [],
      patListData: [
        [
          {
            name: "",
            value: ""
          }
        ]
      ],
      patCode: "",
      listData: [],
      patFamily: [], //家人就诊卡信息列表

      idCard: "",
      show: false,
      isLogin: false,
      disabled: true,
      infoList: [],
      cardType: "身份证号", //选择器选择的文字
      cardTypeValue: "02", //选择器选择的值
      showCardType: false,
      columns: [
        {
          value: "01",
          text: "电子凭证令牌"
        },
        {
          value: "02",
          text: "身份证号"
        },
        {
          value: "03",
          text: "社保卡号"
        },
        {
          value: "99",
          text: "港澳或外籍通行证"
        }
      ],
      fixLocal: {
        "1": "工作地",
        "2": "居住地"
      },
      fixMain: {
        "1": "主就医点",
        "2": "辅就医点"
      }
      // showInfoSearch:false
    };
  },
  created() {
    //判断是否有住院信息
    if (storage.session.get("patcard") == null) {
      alert("请先绑定门诊卡");
      this.$router.push("/oauth?type=outpatient");
      return;
    }
    this.getPatFamily();
    // this.getcfList();
  },
  mounted() {
    this.test(true);
  },
  methods: {
    getPatFamily() {
      var userinfo = JSON.parse(storage.session.get("user"));
      var data = {
        type: "1",
        id_card: userinfo.idCard,
        openid: userinfo.accountCode
      };
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无门诊卡~~"
            });
            this.$router.push({
              path: "/addPatient"
            });
            return;
          }
          this.patListData = [[]];
          this.patFamily = r.returnData;

          for (var i = 0; i < r.returnData.length; i++) {
            this.patListData[0].push({
              name: r.returnData[i].patName,
              value: r.returnData[i].patCode
            });
          }
          console.log(this.patListData);
          this.patCode = r.returnData[0].patCode;
          // this.patCode = JSON.parse(storage.session.get("patcard")).patCode;//设置处方的初始就诊人
          this.patName = [];
          this.patName.push(this.patCode);
          //this.patName.push(r.returnData[0].patCode);
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    },

    updateList(item) {
      this.isLogin = false;
      this.infoList = [];
      this.patCode = item[0];
      for (var i = 0; i < this.patFamily.length; i++) {
        //将选中的就诊卡写入缓存
        if (this.patCode == this.patFamily[i].patCode) {
          storage.session.set("patcard", JSON.stringify(this.patFamily[i]));
        }
      }
      //  this.getcfList();
    },
    test(closeType) {
      if (closeType) {
        var patcard = JSON.parse(storage.session.get("patcard"));
        if (patcard.patIdCard == "" || patcard.patIdCard == null) {
          toolsUtils.toast("该诊疗卡的证件信息为空，请到医院收费处补录");
          //  alert("该用户证件信息为空，请到医院收费处补录");
          this.idCard = "请选择诊疗卡";
          //  this.showInfoSearch =false
        } else {
          this.idCard = patcard.patIdCard;
          // this.showInfoSearch =true
        }
      }
    },
    change() {
      this.$refs.picker.onClick();
    },

    loginClick() {
      if (!this.idCard) {
        toolsUtils.toast("请输入身份证号");
        return;
      }
      if (this.idCard == "请选择诊疗卡") {
        toolsUtils.toast("请选择诊疗卡");
        return;
      }
      if (this.idCard.length !== 18) {
        toolsUtils.toast("输入的身份证号长度有误");
        return;
      }
      if (dataUtils.isCardID(this.idCard) != true) {
        toolsUtils.toast(dataUtils.isCardID(this.idCard));
        return;
      }
      this.isLogin = !this.isLogin;
      if (!this.isLogin) {
        this.idCard = "";
        return;
      }
      var data = {
        id_card: this.idCard,
        mdtrt_cert_type: this.cardTypeValue
      };
      ajax
        .post(apiUrls.IDcardGetYBUserInfo, data)
        .then(r => {
          r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          storage.session.set("id_card", JSON.stringify(this.idCard));
          storage.session.set(
            "mdtrt_cert_type",
            JSON.stringify(this.cardTypeValue)
          );
          storage.session.set("psn_no", JSON.stringify(r.returnData[0].psn_no));
          this.infoList = r.returnData;
          if (this.infoList.length > 0) {
            this.disabled = false;
          }
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    fixedClick() {
      this.$router.push({ path: "/medicalInsuranceBinding" });
    },
    modifyClick() {
      this.$router.push({ path: "/medicalInsuranceModify" });
    },
    cardTypeClick() {
      if (this.isLogin) {
        return;
      }
      this.showCardType = true;
    },
    onConfirmCardType(val) {
      this.cardType = val.text;
      this.cardTypeValue = val.value;
      this.showCardType = false;
    }
  }
};
</script>

<style lang="less" scoped>
.user-info {
  width: 100%;
  padding: 0.24rem;
  background: #fff;
  display: flex;
  align-items: center;
}
.user-info img {
  display: block;
  width: 2rem;
  height: 2rem;
  border-radius: 100%;
}
.user-info .info {
  flex-basis: 0;
  flex-grow: 1;
  padding: 0 0.24rem;
}
.user-info .info h3 {
  text-align: left;
  font-size: 0.28rem;
  font-weight: normal;
  display: flex;
  align-items: center;
}
.user-info .info p {
  font-size: 0.22rem;
  color: #a2a2a2;
}
.user-info .daijiaoNum {
  font-size: 0.22rem;
  color: #a2a2a2;
  text-align: center;
}
.user-info .daijiaoNum div {
  font-size: 0.5rem;
  color: red;
}
/deep/.weui-cell {
  padding: 0;
}
// .picker /deep/.weui-cell_access /deep/.weui-cell__ft{
//   padding-right: 0.14rem;
// }
/deep/.vux-cell-value {
  color: #1a1a1a;
  font-size: 0.32rem;
}
/deep/.weui-cells:after {
  border: 0;
}
/deep/.weui-cells:before {
  border: 0;
}
/deep/.weui-cell_access .weui-cell__ft:after {
  border-color: #fff;
}
.change {
  font-size: 0.3rem;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 0.04rem;
  color: #fff;
  background: #24bab8;
}
.medicalInsurance {
  font-size: 0.32rem;
  overflow: auto;
  padding-bottom: 1.28rem;
  .title {
    font-size: 0.36rem;
    text-align: center;
    padding: 0.4rem 0;
  }
  .search-btn {
    margin: 0.32rem;
  }
  .text {
    margin-top: 0.6rem;
    text-align: center;
  }
  .list-item {
    margin: 0 0.2rem;
    padding: 0.2rem 0.32rem;
    background: #fff;
    border-radius: 0.16rem;
    margin-bottom: 0.4rem;
  }
  .footer-btn {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.2rem;
    background: #fff;
  }
  .btn-fixed {
    margin-right: 0.32rem;
  }
  .dialog-info {
    padding: 0.4rem;
  }
  /deep/.van-field--disabled .van-field__label {
    color: #646566;
  }
}
</style>
