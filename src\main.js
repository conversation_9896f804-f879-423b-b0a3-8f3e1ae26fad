// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import FastClick from "fastclick";
import VueRouter from "vue-router";
import App from "./App";
import router from "./router";
import store from "./store";
import baseData from "./config/baseData";
import { TransferDom } from "vux";
import { ajax } from "./common";
import { exportHttp } from "./common";
import { LoadingPlugin } from "vux";
import { AlertPlugin } from "vux";
import { ToastPlugin } from "vux";
import { WechatPlugin } from "vux";
import { DatePicker } from "element-ui";
import { DatetimePlugin } from "vux";
import {
  Field,
  Area,
  Picker,
  Popup,
  Button,
  Toast,
  Icon,
  NoticeBar,
  DatetimePicker,
  Dialog,
  Empty,
  RadioGroup,
  Radio,
  Form,
  Overlay,
  Swipe,
  SwipeItem,
} from "vant";
import "vant/lib/index.css";

Vue.use(Popup)
  .use(Picker)
  .use(Field)
  .use(Area)
  .use(Button)
  .use(Toast)
  .use(Icon)
  .use(NoticeBar)
  .use(DatetimePicker)
  .use(Dialog)
  .use(Empty)
  .use(RadioGroup)
  .use(Radio)
  .use(Form)
  .use(Overlay);

Vue.use(DatePicker);
Vue.use(DatetimePlugin);

Vue.use(ToastPlugin);
Vue.use(AlertPlugin);

Vue.use(LoadingPlugin);

Vue.use(WechatPlugin);
Vue.use(Swipe);
Vue.use(SwipeItem);

Vue.directive("transfer-dom", TransferDom);

Vue.use(VueRouter);

FastClick.attach(document.body);

Vue.config.productionTip = false;

// // Use config
// if (process.env.NODE_ENV === 'development') {
//   window.AppConf = require('./config/config.dev').default;
// } else {
//   window.AppConf = require('./config/config.prod').default;
// }

ajax.setBaseUrl(baseData.apiHost);
exportHttp.setBaseUrl(baseData.apiHost);

Vue.directive("dbClick", {
  inserted(el, binding) {
    el.addEventListener("click", (e) => {
      if (!el.disabled) {
        el.disabled = true;
        let timer = setTimeout(() => {
          el.disabled = false;
        }, 1000);
      }
    });
  },
});

/* eslint-disable no-new */
new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app-box");
