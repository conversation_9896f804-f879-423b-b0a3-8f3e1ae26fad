<template>
  <div class="warpList">
    <!-- <div class="mrginTop" @click="topersonal(key)" :key="index" v-for="(key,index) in cardData">
      <cardTemp
      :cardDatas='key'>
      </cardTemp>
    </div> -->

    <!-- <div class="submitBtn" @click="toapplyForm('wechatCode')">
      <span>+ 添加健康卡</span>
    </div>
     <p class="tips">{{tips}}</p> -->
    <div v-if="cardData.length === 0 && oldCard.length === 0" class="imgNull">
      <img src="../../assets/NewImg/outpatientImg/nocard.png" alt="" />
      <span>暂无健康卡</span>
    </div>
    <div v-else class="card-all">
      <!-- 旧卡 -->
      <div>
        <div
          class="mrginTop old_card"
          v-for="(item, index) in oldCard"
          :key="index"
          @click="topersonal(item, index, 0)"
        >
          <div
            :class="topersonalIndexOld === index ? 'mrginTop03' : 'mrginTop04'"
          >
            <div class="titleCard">
              <div>东莞市桥头医院</div>
              <div>院内门诊卡</div>
            </div>
            <div class="textCard">
              <div class="cardL">{{ item.patName }}</div>
              <div class="cardR">卡号:{{ item.patCode }}</div>
            </div>
            <div class="footCard">
              <button class="del-btn" @click="delHealthCard(item)">
                删除此门诊卡
              </button>
              <!-- <button class="up-btn" @click="upCard(item)">升级为健康卡</button> -->
            </div>
          </div>
        </div>
      </div>
      <div
        :key="index"
        @click="topersonal(item, index, 1)"
        v-for="(item, index) in cardData"
        class="mrginTop"
      >
        <!-- 新卡 -->
        <div :class="topersonalIndex === index ? 'mrginTop02' : 'mrginTop01'">
          <cardTemp :cardDatas="item"> </cardTemp>
        </div>
      </div>
    </div>

    <p class="tips">{{ tips }}</p>
    <div class="foot-btn">
      <!-- <div class="submitBtn" @click="ConfirmPat()" v-show="checkShow">
        <span>确认就诊人</span>
      </div> -->
      <div class="submitBtn" @click="toapplyForm('wechatCode')">
        <img src="../../assets/addCard.png" alt="" />
        <span>添加健康卡 (能提供有效证件号码的选此项)</span>
      </div>
      <div class="submitBtn" @click="toaddPatient()">
        <img src="../../assets/addCard.png" alt="" />
        <span>绑定门诊卡 (不能提供有效证件号码的选此项)</span>
      </div>
      <!-- <div v-show="tipsShow"> -->
      <!-- <div class="submitBtn" @click="toapplyForm('healthCode')">
        <img src="../../assets/addCard.png" alt="" />
        <span>一键授权 (已有健康卡用户，一键快速关联)</span>
      </div> -->
      <!-- </div> -->
    </div>
  </div>
</template>
<script>
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import cardTemp from "../HealthCardV2/cardTempV2";
export default {
  name: "cardListV3",
  components: { cardTemp },
  data() {
    return {
      tips: "您还可以添加4张健康卡",
      checkShow: true,
      tipsShow: true,
      // 新卡数据
      cardData: [],
      // 旧卡数据
      oldCard: [],
      patCode: "",
      idCard: "",
      openid: "",
      type: "",
      choiceUser: "",
      method: "",
      topersonalIndex: null, //选择卡
      topersonalIndexOld: null
    };
  },
  created() {
    let that = this;
    let upCardstart = storage.session.get("upCardstart");
    that.method = that.$route.query.method;
    that.type = that.$route.query.type;

    if (that.method == "user") {
      that.checkShow = false;
    }

    // console.log(upCardstart)
    if (upCardstart == "begin") {
      let data = JSON.parse(storage.session.get("upCard"));
      data.wechatCode = that.$route.query.wechatCode || "";
      if (data.wechatCode == null) {
        toolsUtils.alert("wechatCode获取失败！请重试!");
        //修改升级门诊卡状态为结束
        storage.session.set("upCardstart", "end");
        setTimeout(function() {
          window.location.reload();
        }, 800);
        return;
      }

      // ajax.post(apiUrls.upHealthCard, data).then(r => {
      //   if (!r.data.success) {
      //     toolsUtils.alert(r.data.returnMsg);
      //   } else {
      //     toolsUtils.alert("升级成功");
      //   }
      //   //修改升级门诊卡状态为结束
      //   storage.session.set("upCardstart", "end");
      //   setTimeout(function() {
      //     window.location.reload();
      //   }, 800);
      //   return;
      // });
    } else {
      that.getHealthCardInfo();
      storage.session.set("method", that.method);
    }
  },
  methods: {
    ConfirmPat() {
      let that = this;
      //确认就诊人
      if (that.type == "1") {
        //console.log(this.patientData_menzhen);
        //console.log(this.choiceUser.name);
        //console.log(this.choiceIndex);
        if (that.choiceUser == "") {
          toolsUtils.alert("请选择就诊人");
          return;
          //  if(this.method=="order"){
          //    window.location.href="/submitAgo?note=no";
          //       // this.$router.replace({
          //         //       path: "/submitAgo"
          //       //     });
          //   }else{
          //      toolsUtils.alert("请选择就诊人");
          //   }
        } else {
          let info = JSON.parse(storage.session.get("user"));
          var data = {
            patcardNo: that.choiceUser.patCode,
            id_card: info.idCard
          };
          ajax
            .post(apiUrls.GetpatFamilyInfo, data)
            .then(r => {
              console.log(r);
              var r = r.data;
              if (r.success == false) {
                toolsUtils.alert(r.returnMsg);
                return;
              }
              storage.session.set("patcard", JSON.stringify(r.returnData));
              if (that.method == "order") {
                window.location.href = "/submitAgo?note=no";
                //   this.$router.replace({
                //   path: "/submitAgo"
                // });
              } else {
                that.$router.push({
                  path: "/userCenter"
                });
              }
            })
            .catch(e => {
              toolsUtils.alert("程序异常:" + JSON.stringify(e));
            });
        }
      }
    },
    getHealthCardInfo() {
      var that = this;
      let info = JSON.parse(storage.session.get("user"));
      that.idCard = info.idCard;
      that.openid = info.accountCode;

      let data = {
        idNumber: that.idCard,
        openid: that.openid
      };

      //获取电子健康卡表
      ajax
        .post(apiUrls.getCardListHealthCardV2, data)
        .then(r => {
          console.log(r);
          if (!r.data.success) {
            toolsUtils.alert(r.data.returnMsg);
          }
          // console.log(r.data.returnData);
          that.cardData = r.data.returnData;

          if (that.cardData.length >= 4) {
            that.tipsShow = false;
            that.tips = "您绑定的健康卡已达上限";
          } else {
            that.tips =
              "您还可以添加" + (4 - that.cardData.length) + "张健康卡";
          }
        })
        .catch(e => {
          console.log(e);
        });

      //获取就诊人列表
      var odata = {
        idNumber: that.idCard
      };

      ajax.post(apiUrls.getCardListOldCard, odata).then(r => {
        console.log(r.data.returnData);
        if (!r.data.success) {
          toolsUtils.alert(r.data.returnMsg);
        }
        that.oldCard = r.data.returnData;
      });
    },
    toapplyForm(index) {
      // let that = this;
      if (!this.tipsShow) {
        alert("您绑定的健康卡已达上限");
        return;
      }
      // let burl = encodeURIComponent("https://qtyywx.qtyy.com/applyFormV2");
      storage.session.set("registerType", index);
      // window.location.href = `https://health.tengmed.com/open/getUserCode?apiVersion=2&redirect_uri=${burl}`;

      let that = this;
      that.$router.push({
        path: "/applyFormV3",
      });

      // if(index=="healthCode"){
      //   window.location.href =
      //    `https://health.tengmed.com/open/getHealthCardList?redirect_uri=${burl}`;
      // }
    },
    toaddPatient() {
      let that = this;
      that.$router.push({
        path: "/addPatients",
        query: {
          num: "1"
        }
      });
    },
    topersonal(key, index, keys) {
      // console.log(key);
      let that = this;
      if (that.method == "user") {
        if (keys === 0) {
          return;
        }
        that.$router.push({
          path: "/personalV2",
          query: {
            qRCode: key.qRCode,
            patCardNo: key.patCode,
            relation: key.relationType,
            type: key.type
          }
        });
      }
      if (that.method == "order") {
        if (keys === 0) {
          that.topersonalIndexOld = index;
          that.topersonalIndex = null;
        } else {
          that.topersonalIndex = index;
          that.topersonalIndexOld = null;
        }

        that.choiceUser = key;
      }
    },
    upCard(item) {
      let that = this;
      let upCardstart = "begin";
      let upCard = {
        patCode: item.patCode,
        patMobile: item.patMobile,
        wechatCode: ""
      };
      storage.session.set("upCardstart", upCardstart); //开启升级门诊卡接口
      storage.session.set("upCard", JSON.stringify(upCard));

      let burl = encodeURIComponent(
        "https://qtyywx.qtyy.com/cardListV3?type=" +
          that.type +
          "&method=" +
          that.method
      );
      window.location.href = `https://health.tengmed.com/open/getUserCode?apiVersion=2&redirect_uri=${burl}`;
    },
    delHealthCard(user) {
      if (!confirm("确定解绑健康卡吗？")) {
        return;
      }
      var that = this;

      let info = JSON.parse(storage.session.get("user"));
      var data = {
        id_card: info.idCard,
        patCardNo: user.patCode,
        relation: user.relationType.trim(),
        type: user.type
      };

      console.log(data);
      ajax
        .post(apiUrls.DeletePatfamilyrelation, data)
        .then(r => {
          console.log(r);
          if (!r.data.success) {
            console.log(r.data.returnMsg);
            return;
          }
          setTimeout(function() {
            toolsUtils.alert("删除成功！");
            storage.session.set("upCardstart", "end");
            window.location.reload();
          }, 800);
        })
        .catch(e => {
          console.log(e);
        });
    }
  }
};
</script>
<style scoped>
.warpList {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.mrginTop {
  margin-top: 0.2rem;
}
.mrginTop01 {
  margin-top: 0.2rem;
  width: 6.2rem;
  height: 3.5rem;
  margin: auto;
}
.mrginTop02 {
  width: 6.2rem;
  height: 3.5rem;
  margin: 0.4rem auto 0;
  box-shadow: 2px 2px 4px #52675a;
}
.mrginTop04 {
  width: 6.2rem;
  height: 2.8rem;
  margin: 0.4rem auto;
  border-radius: 0.1rem;
  margin: auto;
}
.mrginTop03 {
  width: 6.2rem;
  height: 2.8rem;
  margin: 0.6rem auto;
  border-radius: 0.1rem;
  box-shadow: 2px 2px 4px #52675a;
}
/* 
.submitBtn {
  width: 90%;
  height: 1.08rem;
  border-radius: 8px;
  margin: 0.68rem auto 0;
  font-size: 0.36rem;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: linear-gradient(to right, #3dc0f3, #2dc4c2);
  box-shadow: 1px 1px 2px #52675a;
}


  .tips {
    text-align: center;
    line-height: 0.6rem;
    font-size: 0.26rem;
    color: #888888;
  } */
.imgNull {
  width: 100%;
  height: 8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  font-size: 0.32rem;
  background: white;
}
.imgNull span {
  margin-top: 0.2rem;
}
.foot-btn {
  width: 100%;
  min-height: 1rem;
  position: fixed;
  left: 0;
  bottom: 0;
  background: white;
}
.submitBtn {
  width: 96%;
  height: 0.6rem;
  border-radius: 4px;
  margin: 0.2rem auto 0.2rem;
  font-size: 0.28rem;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  /* // background-image: linear-gradient(to right, #3dc0f3, #2dc4c2); */
  background: #04be02;
  box-shadow: 1px 1px 2px #52675a;
}
.submitBtn img {
  width: 0.4rem;
}
.tips {
  text-align: center;
  line-height: 0.6rem;
  font-size: 0.26rem;
  color: #888888;
}
/* 旧卡部分 */
.old_card {
  width: 6.2rem;
  height: 2.8rem;
  margin: 0.2rem auto;
  /* background: #fffaf4; */
  background: linear-gradient(to right, #fffcef 0%, #f8daff 100%);
  /* box-shadow: 1px 1px 2px #52675a; */
  border-radius: 0.1rem;
  color: #4a4a4a;
}
.old_card .titleCard {
  flex: 1;
  height: 0.8rem;
  padding: 0 0.24rem;
  font-size: 0.18rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.titleCard div {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.titleCard div:first-child {
  justify-content: flex-start;
}
.old_card .textCard {
  flex: 1;
  padding: 0 0.24rem;
  height: 1rem;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 0.32rem;
}
.cardL {
  flex: 1;
}
.cardR {
  flex: 3;
  margin-left: 0.2rem;
}
.footCard {
  padding: 0 0.24rem;
  flex: 1;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.footCard button {
  padding: 5px 10px;
  border: 0;
  border-radius: 4px;
  font-size: 0.28rem;
  color: white;
}

.card-all {
  width: 100%;
  height: 75vh;
  overflow: hidden auto;
}
.up-btn {
  background: #409eff;
}
.del-btn {
  margin-right: 0.1rem;
  background: #e75c5c;
}
</style>
