<template>
  <div class="wrapTemp">
    <div class="card-face-container">
      <!-- <div class="card-top-info"> -->
      <div class="card-top-org cf">东莞市卫生健康委员会</div>
      <div class="card-top-title">
        <img src="../../assets/NewImg/outpatientImg/icon2.png" alt />
        <span>电子健康卡</span>
      </div>
      <!-- </div> -->

      <div class="card-detail-info">
        <div class="card-user-info">
          <div class="card-user-name">{{ cardDatas.patName }}</div>
          <div>{{ cardDatas.patIdCard.substring(0,4) }}**********{{cardDatas.patIdCard.substring(14,18)}}</div>
        </div>

        <div class="card-qrcode">
          <!-- <img src="../../assets/NewImg/outpatientImg/qrcode.png" alt /> -->
          <vue-qr :logoSrc="imgUrl" :text="codeUrl" :size="166" :margin="4" :logoMargin='4' logoBackgroundColor	 = "#fff" :logoScale="48" :logoCornerRadius="8"></vue-qr>
        </div>
      </div>

      <div class="card-footer">中华人民共和国国家卫生健康委员会监制</div>
    </div>
  </div>
</template>
<script>
import VueQr from "vue-qr";
export default {
  name: "cardTemp",
  props: ["cardDatas"],
  data() {
    return {
      imgUrl: require("../../assets/NewImg/outpatientImg/logo_.png"),
      codeUrl: "",
    };
  },
  components: {
    VueQr,
  },
  mounted() {
    this.codeUrl = this.cardDatas.qRCode;
  },
  methods: {},
};
</script>
<style  scoped>
.wrapTemp {
  width: 100%;
  height: 3.5rem;
  /* margin: 0.2rem auto 0; */
  display: flex;
  justify-content: center;
  align-items: center;
}
.wrapTemp .card-face-container {
  width: 6.2rem;
  height: 3.5rem;
  color: #000000;
  background: url("../../assets/NewImg/outpatientImg/cardnewbg.png") no-repeat;
  background-size: cover;
  /* box-shadow: 1px 1px 2px #52675a; */
  border-radius: 0.1rem;
}
/* .card-top-info {
  width: calc(100% - .61rem);
  height: 0.5rem;
  margin: 0.34rem auto 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.18rem;
} */
.card-top-org {
  font-family: "苹方中黑体";
  font-size: 0.18rem;
  height: 25px;
  margin-left: 0.34rem;
  margin-top: 0.32rem;
  float: left;
}
.card-top-title {
  display: flex;
  justify-content: center;
  align-items: center;
  /* width: 2.13rem; */
  height: 0.55rem;
  margin: 0.23rem 0 0 3.8rem;
  zoom: 0%;
}
.card-top-title img {
  width: 0.55rem;
  height: 0.55rem;
  margin-right: 0.08rem;
}
.card-top-title span {
  font-size: 0.3rem;
  font-family: "苹方中黑体";
  color: #2b2b2b;
}

.card-detail-info {
  width: calc(100% - 0.5rem);
  height: 1.58rem;
  margin: 0.53rem 0.15rem 0 0.35rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.card-user-info {
  /* height: 100%; */
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex-direction: column;
  color: #2b2b2b;
  font-size: 0.3rem;
  /* margin: 1.24rem 0 0 .35rem; */
  /* float: left; */
}
.card-user-info div {
  font-family: "苹方中黑体";
}
.card-user-name {
  margin-bottom: 0.02rem;
  font-size: 0.36rem;
}

.card-qrcode {
  width: 1.62rem;
  height: 1.62rem;
}
.card-qrcode img {
  width: 100%;
  height: 100%;
}
.card-footer {
  font-size: 0.18rem;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.13rem;
  letter-spacing: 0;
  font-family: "苹方中黑体";
}
.cf {
  zoom: 1;
}
.cf:before,
.cf:after {
  content: "";
  display: table;
}
.cf:after {
  clear: both;
}
</style>