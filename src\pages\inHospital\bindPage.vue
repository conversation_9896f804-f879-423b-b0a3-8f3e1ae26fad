<template>
<!--绑定页面-->
    <div class="bind-page">
        <div class="form-wrap">
           <form action="">
               <div class="input-wrap">
                   <h3>绑定开启住院功能</h3>
                   <label for=""><input type="text" v-model="bindMsg.bindName" placeholder="真实姓名"></label>
                   <label for=""><input type="text" v-model="bindMsg.bindAdmissCardNo" placeholder="住院号"></label>
                   <p>备注：请先关注公众号后再进行操作</p>
                   <p>请务必提供真实有效信息，否则将无法开启</p>
                    <input type="button" @click="bindInfo" value="确认"/>
               </div>

           </form>
       </div>
    </div>
</template>

<script>
import {ajax,toolsUtils,dataUtils, storage} from "../../common"
import apiUrls from '../../config/apiUrls'
import { isNullOrUndefined } from 'util';
export default {
  name: "index",
  components: {
    // HeaderBar
  },
  data() {
    return {
      title: "绑定页面",
      bindMsg:{
        bindName:'',
        bindAdmissCardNo:''
      }
    };
  },
  methods: {
    bindInfo:function(){
      // if(dataUtils.isPatName(this.bindMsg.bindName)!=true)
      // {
      //     toolsUtils.alert('请输入正确名字')
      //     return;
      // }
      if(!dataUtils.checkRate(this.bindMsg.bindAdmissCardNo))
      {
         toolsUtils.alert('卡号不正确')
         return;
      }
      var info=JSON.parse(storage.session.get("user"))
      var data={
        name:this.bindMsg.bindName,
        admissionCardNo:this.bindMsg.bindAdmissCardNo,
        id_card:info.idCard
      }
      console.log(data)
      //更新数据
      ajax
          .post(apiUrls.BindAdmissionCard, data)
          .then((r) => {
            console.log(r);
            r = r.data;
            if (!r.success) {
              // toolsUtils.alert(r.returnMsg);
              alert(r.returnMsg);
              return;
            }
            if (storage.session.get("admissionCard") == null) {
              storage.session.set(
                "admissionCard",
                JSON.stringify(r.returnData)
              );
            }
            this.$router.back(-1);
            return;
          })
          .catch((e) => {
            // toolsUtils.alert("网络异常");
            alert("网络异常");
            console.log(e);
          });
      //重新读取更新后的数据 保存到session
      var update={
         openid:info.id_card
      };
      // ajax.post(apiUrls.AccountByOpenId,data).then(r=>{
      //      r=r.data;
      //       if(!r.success)
      //       {
      //           toolsUtils.alert(r.returnMsg);
      //           return;
      //       }
      //       storage.session.set('user', JSON.stringify(r.returnData));
      //       this.$router.push('/homepage');
      //       return;
      //   }).catch(e=>{
      //       console.log(e);
      //       toolsUtils.alert('系统异常:'+JSON.stringify(e));
      //       return;
      //   })

    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.bind-page{
    height:100%;
    width:100%;
    background: #fff;
    overflow: auto;
}
.form-wrap {
  padding:1rem 0.48rem;
}
.form-wrap form > div {
  width: 100%;
  background: #ffffff;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.35),
    0 2px 6px 0 rgba(36, 186, 184, 0.4), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  border-radius: 5px;
  padding: 0.6rem 0.4rem;
  margin-bottom: 0.42rem;
}
.input-wrap {
  display: flex;
  flex-wrap: wrap;
}

.form-wrap form input[type="button"] {
  width: 100%;
  display: block;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 0.96rem;
  font-size: 0.32rem;
  color: #ffffff;
}

.input-wrap label {
  height: 0.96rem;
  background: #f6f6f6;
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  margin-bottom: 0.28rem;
  flex-basis: 100%;
  align-items: center;
}
.input-wrap label input {
  font-size: 0.4rem;
  flex-grow: 1;
  border: none;
  background: transparent;
  height: 100%;
  outline: none;
  padding: 0 0.28rem;
}

.form-wrap form input[type="submit"] {
  width: 100%;
  display: block;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 0.96rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.input-wrap h3 {
  font-size: 0.4rem;
  color: #354052;
  text-align: center;
  flex-basis: 100%;
  margin-bottom: 0.6rem;
  font-weight: normal;
}
.input-wrap p {
  font-size: 0.26rem;
  color: #fd7070;
  margin-bottom: 0.2rem
}
.input-wrap label input::-webkit-input-placeholder{
    color: #D9D9D9;
}
</style>

