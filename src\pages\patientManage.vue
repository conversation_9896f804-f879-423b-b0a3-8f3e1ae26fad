<template>
  <!--就诊人管理-->
  <div class="patientManage-page">
    <h3 v-show="type == '2'">就诊人信息</h3>
    <div class="head-btn" v-show="type == '1'" @click="linkPage()">
      <img src="../assets/Add.png" alt />
      <span>添加就诊人</span>
      <img src="../assets/right.png" alt />
    </div>
    <div class="head-btn" v-show="type == '1'" @click="bindCard()">
      <img src="../assets/Add.png" alt />
      <span>就诊卡绑定（仅限已知内院卡号用户）</span>
      <img src="../assets/right.png" alt />
    </div>
    <div class="patient-wrap">
      <ul v-if="type == '2'">
        <li :key="index" v-for="(item, index) in patientData">
          <span @click="seeData(item)"
            >{{ item.name }} - {{ item.admissionNo }}</span
          >
          <div @click="ShowFun(index)" class="moreImg-div">
            <img src="../assets/spot_more.png" alt />
          </div>
          <div class="btn-wrap" :class="activeFun === index ? 'btn-show' : ''">
            <button class="cancel-btn" @click="HideFun()">取消</button>
            <!-- <button class="edit-btn">编辑</button> -->
            <button
              class="delete-btn"
              @click="
                RemoveFun(index, item.admissionNo, item.relationType, item.type)
              "
            >
              删除
            </button>
          </div>
        </li>
      </ul>
      <ul v-if="type == '1'">
        <li
          :key="index"
          v-for="(item, index) in patientData_menzhen"
          @click="choiceUserFun(item.patName, index)"
          :class="choiceIndex === index ? 'li-active' : ''"
        >
          <span @click="seeData(item)"
            >{{ item.patName }} - {{ item.patCode }}</span
          >
          <div @click="ShowFun(index)" class="moreImg-div">
            <img src="../assets/spot_more.png" alt />
          </div>
          <div class="btn-wrap" :class="activeFun === index ? 'btn-show' : ''">
            <button class="cancel-btn" @click="HideFun()">取消</button>
            <!-- <button class="edit-btn">编辑</button> -->
            <button
              class="delete-btn"
              @click="
                RemoveFuns(index, item.patCode, item.relationType, item.type)
              "
            >
              删除
            </button>
          </div>
        </li>
      </ul>
    </div>
    <div class="addBtn-wrap">
      <button v-show="type == '1'" @click="ConfirmPat()">确认就诊人</button>
      <!-- 门诊按钮 -->
      <button v-show="type == '2'" @click="linkPages()">
        <span>+</span>添加就诊人
      </button>
      <!-- 查看条形码 -->
      <button @click="btnNew">查看二维码</button>
      <!-- 门诊按钮 -->
    </div>
    <!-- 条形码弹窗 -->
    <div class="popuper" v-show="bgShow">
      <div class="popupDiv">
        <div class="popupNew" style="font-size:.36rem;font-weight:600;">
          二维码信息
        </div>

        <div class="popupNew">
          <div class="popupL">姓名:</div>
          <div class="popupR">{{ patNameNow }}</div>
        </div>

        <div class="popupNew">
          <div class="popupL">卡号:</div>
          <div class="popupR">{{ patCodeNow }}</div>
        </div>

        <div class="popupNew" style="border:0;">
          <div class="popupL">二维码：</div>
          <div class="popupR"></div>
        </div>
        <div class="barCodeNew">
          <!-- 二维码 -->
          <div class="main-qrcode">
            <div><qrcode :value="patCodeNow"></qrcode></div>
          </div>
          <!-- 条形码 -->
          <!-- <svg class="barcode" :jsbarcode-value="patCodeNow" jsbarcode-width='1'></svg> -->
        </div>
        <div class="bgBtn">
          <div class="bgBtnL" @click="btnNew">关闭</div>
        </div>
      </div>
    </div>
    <!-- <p style="font-size:12px;position:fixed;bottom:100px;float:left;" @click='csbtn'>.</p> -->
  </div>
</template>

<script>
import { ajax, toolsUtils, storage } from "../common";
import { Qrcode } from "vux";
import JsBarcode from "jsbarcode";
import apiUrls from "../config/apiUrls";
import index from "../store";
export default {
  name: "index",
  components: {
    Qrcode
  },
  data() {
    return {
      title: "首页",
      //   病人列表数据
      id_card: "",
      type: "",
      patientData: [],
      patientData_menzhen: [],
      btnShow: "",
      choiceUser: {
        name: ""
      },
      choiceIndex: "",
      method: "", //用以判断上一个路由（order为订单页面跳转过来，user为用户中心页面跳转过来）
      bgShow: false, //条形码弹窗
      patNameNow: "",
      patCodeNow: ""
    };
  },
  methods: {
    // csbtn(){

    //   this.$router.push({
    //     path:'/personal',
    //     query:{
    //       type:0,
    //       patCode:this.patCodeNow
    //     }
    //   })
    // },
    //条形码弹窗
    btnNew() {
      // console.log(this.patCode);
      if (this.patNameNow === "") {
        confirm("请选择就诊人");
        return;
      }
      this.bgShow = !this.bgShow;
    },
    seeData(item) {
      // console.log('item',item);
      setTimeout(function() {
        JsBarcode(".barcode").init();
      }, 1000);
      this.patNameNow = item.patName;
      this.patCodeNow = item.patCode;
      console.log(item);
    },
    ShowFun(index) {
      return (this.btnShow = index);
    },
    HideFun() {
      return (this.btnShow = "");
    },
    RemoveFuns(index, patCode, relation, type) {
      var info = JSON.parse(storage.session.get("user"));
      var patcards = JSON.parse(storage.session.get("patcard"));
      console.log(patcards.patCode, patCode);
      if (patcards.patCode == patCode) {
        toolsUtils.alert("不能删除当前卡号");
        return;
      }

      var data = {
        id_card: info.idCard,
        patCardNo: patCode,
        relation: relation,
        type: type
      };
      ajax
        .post(apiUrls.DeletePatfamilyrelation, data)
        .then(r => {
          console.log(r);
          this.patientData_menzhen.splice(index, 1);
          this.btnShow = "";
          return;
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
        });
    },

    RemoveFun(index, admissionCardNo, relation, type) {
      var info = JSON.parse(storage.session.get("user"));
      var admissionCard = JSON.parse(storage.session.get("admissionCard"));
      // if (admissionCard.admissionNo == admissionCardNo) {
      //   toolsUtils.alert("不能删除当前卡号");
      //   return;
      // }

      var data = {
        id_card: info.idCard,
        admissionCardNo: admissionCardNo,
        relation: relation,
        type: type
      };
      ajax
        .post(apiUrls.Deletefamilyrelation, data)
        .then(r => {
          console.log(r);
          debugger;
          this.patientData.splice(index, 1);
          this.btnShow = "";
          return;
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
        });
    },
    obtain() {
      var info = JSON.parse(storage.session.get("user"));
      var id_card = this.$route.query.id_card,
        type = this.$route.query.type;
      this.id_card = id_card;
      this.method = this.$route.query.method;
      this.type = type;
      if (this.type == "1") {
        //获取就诊人列表
        var data = {
          id_card: id_card,
          type: type,
          openid: info.accountCode
        };
        console.log(data);
        ajax.post(apiUrls.GetUserFamily, data).then(r => {
          console.log(r);
          var data = r.data;
          if (!data.success) {
            toolsUtils.alert(data.returnMsg);
            return;
          }
          this.patientData_menzhen = data.returnData;
          return;
        });
      } else {
        //获取就诊人列表
        var data = {
          id_card: id_card,
          type: type
        };
        console.log(data);
        ajax.post(apiUrls.GetUserFamily, data).then(r => {
          console.log(r);
          var data = r.data;
          if (!data.success) {
            toolsUtils.alert(data.returnMsg);
            return;
          }
          this.patientData = data.returnData;
          return;
        });
      }
    },
    //跳转添加就诊人
    linkPage() {
      this.$router.push({
        path: "/cardListV2",
        query: {
          // id_card: JSON.parse(storage.session.get("user")).idCard,
          type: "1",
          method: "user"
        }
      });
    },
    linkPages() {
      this.$router.push({
        path: "/bindPage"
        // query: {
        //   num: this.type
        // }
      });
    },
    //跳转添加就诊人
    bindCard() {
      this.$router.push({
        path: "/addPatients",
        query: {
          num: this.type
        }
      });
    },
    //选中就诊人
    choiceUserFun(name, index) {
      this.choiceUser.name = name;
      this.choiceIndex = index;
    },
    ConfirmPat() {
      //确认就诊人
      if (this.type == "1") {
        console.log(this.patientData_menzhen);
        console.log(this.choiceUser.name);
        console.log(this.choiceIndex);
        if (this.choiceUser.name == "") {
          if (this.method == "order") {
            window.location.href = "/submitAgo?note=no";
            // this.$router.replace({
            //       path: "/submitAgo"
            //     });
          } else {
            toolsUtils.alert("请选择就诊人");
          }
        } else {
          var data = {
            patcardNo: this.patientData_menzhen[this.choiceIndex].patCode,
            id_card: this.id_card
          };
          ajax
            .post(apiUrls.GetpatFamilyInfo, data)
            .then(r => {
              var r = r.data;
              if (r.success == false) {
                toolsUtils.alert(r.returnMsg);
                return;
              }
              storage.session.set("patcard", JSON.stringify(r.returnData));
              if (this.method == "order") {
                window.location.href = "/submitAgo?note=no";
                //   this.$router.replace({
                //   path: "/submitAgo"
                // });
              } else {
                // this.$router.push({
                //   path: "/userCenter"
                // });
                this.$router.back(-1);
              }
            })
            .catch(e => {
              toolsUtils.alert("程序异常:" + JSON.stringify(e));
            });
        }
      }
    }
  },
  computed: {
    activeFun: function() {
      return this.btnShow;
    }
  },
  created() {
    this.obtain();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.patientManage-page {
  height: 100%;
  width: 100%;
  background: #ffffff;
  overflow: auto;
}
.patientManage-page h3 {
  font-size: 0.32rem;
  color: #354052;
  height: 1.16rem;
  line-height: 1.16rem;
  background: #fdfbfb;
  padding: 0 0.4rem;
  font-weight: normal;
}
.patient-wrap {
  margin-bottom: 0.8rem;
  overflow: hidden;
}
.patient-wrap ul li {
  display: flex;
  align-items: center;
  padding: 0 0.4rem;
  height: 1.16rem;
  line-height: 1.16rem;
  border-bottom: 1px solid #dfe3e9;
  position: relative;
}
.patient-wrap ul li span {
  flex-grow: 1;
  font-size: 0.32rem;
  color: #354052;
}
.moreImg-div {
  height: 100%;
  display: flex;
  align-items: center;
  width: 0.5rem;
}
.patient-wrap ul li img {
  width: 100%;
  display: block;
}
.addBtn-wrap {
  /* padding: 0 0.4rem; */
  width: 100%;
  height: 0.96rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
/* .addBtnR */
.addBtn-wrap button {
  display: block;
  height: 100%;
  width: 40%;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  color: #fff;
  font-size: 0.32rem;
}
.addBtn-wrap button span {
  display: inline-block;
  height: 0.4rem;
  width: 0.4rem;
  border-radius: 100%;
  border: 1px solid #fff;
  margin-right: 0.16rem;
}
.btn-wrap {
  position: absolute;
  right: -100%;
  height: 100%;
  transition: right 300ms ease-in;
}
.btn-wrap button {
  width: 1.44rem;
  height: 100%;
  display: block;
  float: left;
  border: none;
  color: #fff;
  font-size: 0.32rem;
}
.btn-wrap .cancel-btn {
  background: #bfbfbf;
}
.btn-wrap .edit-btn {
  background: #6895ff;
}
.btn-wrap .delete-btn {
  background: #fd7070;
}
.btn-show {
  right: 0 !important;
  transition: right 300ms ease-out;
}
.head-btn {
  height: 1.16rem;
  background: #fdfbfb;
  border-bottom: 1px solid #dfe3e9;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
}
.head-btn span {
  font-size: 0.32rem;
  color: #24bab8;
  flex: 1;
}
.head-btn img {
  height: 0.4rem;
}
.li-active {
  background: #ccc;
}

/* 条形码弹窗 */
.popuper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1200;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.32rem;
  color: #4a4a4a;
}
.popuper .popupDiv {
  width: 80%;
  min-height: 50%;
  background: white;
  border-radius: 8px;
}
.popupNew {
  width: 100%;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
}
.popupL {
  width: 26%;
  height: 100%;
  display: flex;
  justify-self: start;
  align-items: center;
  margin-left: 4%;
}
.popupR {
  width: 60%;
  height: 100%;
  display: flex;
  justify-self: start;
  align-items: center;
}
.barCodeNew {
  width: 100%;
  min-height: 1.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
}
.popupDiv .bgBtn {
  width: 100%;
  height: 1.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bgBtnL {
  width: 85%;
  height: 0.8rem;
  background: #24bab8;
  color: white;
  border: 1px solid #24bab8;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.2rem;
}
.barcode {
  width: 95%;
}
.main-qrcode {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  height: 90%;
  font-size: 0.4rem;
}
</style>
