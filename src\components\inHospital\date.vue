<template>
  <div class="index-content">
    <section>
      <h3>
        <span>选择时间</span
        ><span class="date-span"
          >{{ checkYear }}<img src="../../assets/more.png" alt=""
        /></span>
      </h3>
      <div class="date-wrap">
        <div class="week-wrap">
          <div :key="index" v-for="(week, index) in weekArr">{{ week }}</div>
        </div>
        <div class="day-wrap">
          <div
            :key="index"
            v-for="(list, index) in dayData"
            :class="[checkDate === index ? 'check-date' : '', noCheck(index)]"
            @click="clickDate(index, list.date)"
          >
            <span>{{ index == 0 ? list.dayName : list.day }}</span>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
export default {
  name: "index",
  components: {
    Cell,
    Group
  },

  data() {
    return {
      title: "日期组件",
      clickNum: "",
      methodNum: "",
      checkYear: "", //已选中的日期
      weekName: ["日", "一", "二", "三", "四", "五", "六"],
      weekArr: [],
      dayData: []
    };
  },
  mounted() {
    this.dayFun();
  },
  methods: {
    //获取今天之后未来七天的日期
    dayFun() {
      this.clickNum = parseInt(this.$route.query.method);
      this.methodNum = parseInt(this.$route.query.method);
      var dateObj = new Date(),
        year = dateObj.getFullYear(),
        month = dateObj.getMonth() + 1,
        day_1 = dateObj.getDate(),
        day_2 = dateObj.getDay();
      //获取今天的星期几
      this.weekArr.push(this.weekName[day_2]);
      var jintian = {
        date:
          year +
          "-" +
          (month < 10 ? "0" + month : month) +
          "-" +
          (day_1 < 10 ? "0" + day_1 : day_1),
        day: day_1,
        dayName: "今"
      };
      //每个月的天数
      var days = 0;
      if (month == 4 || month == 6 || month == 9 || month == 11) {
        //小月
        days = 30;
      } else if (month == 2) {
        if (year % 4 == 0 || year % 100 == 0) {
          //闰年是二月份天数
          days = 29;
        } else {
          days = 28;
        }
      } else {
        //大月
        days = 31;
      }
      var dateArr = [];
      for (var i = 0; i < 13; i++) {
        day_1++;
        if (day_1 > days) {
          day_1 = 1;
          if (month == 12) {
            month = 1;
            year = year + 1;
          } else {
            month = month + 1;
          }
        }
        dateArr.push({
          date:
            year +
            "-" +
            (month < 10 ? "0" + month : month) +
            "-" +
            (day_1 < 10 ? "0" + day_1 : day_1),
          day: day_1
        });
        var dd = new Date(
          year +
            "-" +
            (month < 10 ? "0" + month : month) +
            "-" +
            (day_1 < 10 ? "0" + day_1 : day_1)
        );
        if (this.weekArr.length < 7) {
          this.weekArr.push(this.weekName[dd.getDay()]);
        }
      }
      this.dayData = dateArr;
      this.dayData.unshift(jintian);
      //默认选中日期
      // this.checkYear = this.dayData[this.methodNum].date;
      //默认选中今天
      this.checkYear = this.dayData[0].date;
      this.clickNum = 0;
      this.$emit("onload", {
        deptid: this.$route.query.deptId,
        // date: this.dayData[this.methodNum].date
        //默认选中今天
        date: this.dayData[0].date
      });
    },

    //选择日期的点击事件
    clickDate(index, date) {
      if (index === this.methodNum) {
        this.clickNum = index;
        this.checkYear = date;
        this.$emit("getDrInfo", {
          deptid: this.$route.query.deptId,
          date: date
        });
      } else if (index >= this.methodNum && this.methodNum !== 0) {
        this.clickNum = index;
        this.checkYear = date;
        this.$emit("getDrInfo", {
          deptid: this.$route.query.deptId,
          date: date
        });
        //  this.getDrInfo(this.$route.query.deptId,date);
      }
      //默认选中今天
       this.clickNum = index;
       this.checkYear = date;
       this.$emit("getDrInfo", {
         deptid: this.$route.query.deptId,
         date: date
       });
    },
    //不能选中的样式
    noCheck(index) {
      // if (index !== this.methodNum && this.methodNum === 0) {
      //   return "no-check";
      // } else if (index < this.methodNum && this.methodNum !== 0) {
      //   return "no-check";
      // }
      //默认选中今天
       if (index !== this.methodNum && this.methodNum === 0) {
         return "no-check";
       }
    }
  },
  computed: {
    checkDate() {
      return this.clickNum;
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.index-content {
  padding: 0 0.4rem;
  margin-bottom: 0.24rem;
}
.index-content section {
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.15),
    0 2px 6px 0 rgba(36, 186, 184, 0.2), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  background: #fff;
}
.index-content section h3 {
  display: flex;
  font-weight: normal;
  height: 1.04rem;
  background: #fdfbfb;
  align-items: center;
  padding: 0 0.28rem;
}
.index-content section h3 span:first-child {
  flex-grow: 1;
  font-size: 0.32rem;
  color: #354052;
}
.index-content section h3 .date-span {
  font-size: 0.32rem;
  color: #7f8fa4;
}
.index-content section h3 span img {
  height: 0.3rem;
  vertical-align: middle;
  margin-left: 0.1rem;
  margin-top: -0.06rem;
}
.day-wrap,
.week-wrap {
  display: flex;
  flex-wrap: wrap;
}
.week-wrap div {
  height: 0.96rem;
  flex-grow: 1;
  line-height: 0.96rem;
  text-align: center;
  font-size: 0.32rem;
  color: #7f8fa4;
}
.day-wrap div {
  flex-basis: 14.28%;
  font-size: 0.32rem;
  color: #7f8fa4;
  height: 0.96rem;
  line-height: 0.96rem;
  text-align: center;
  position: relative;
}
.day-wrap .check-date span {
  position: relative;
  z-index: 2;
  color: #fff;
}
.day-wrap .check-date::after {
  display: block;
  content: "";
  height: 0.6rem;
  width: 0.6rem;
  border-radius: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  background: #24bab8;
  margin-left: -0.3rem;
  margin-top: -0.3rem;
}
.day-wrap .no-check {
  color: #ccc;
}
</style>
