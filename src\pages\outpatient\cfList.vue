<template>
  <!--预约的具体时间-->
  <div class="cf-list">
    <!-- 用户信息 -->
    <div class="user-info">
      <img src="../../assets/user_face.png" alt="" />
      <div class="info">
        <h3>
          <group>
            <popup-picker
              ref="picker"
              class="picker"
              title=""
              :show-name="true"
              @on-change="updateList"
              v-model="patName"
              :data="patListData"
            ></popup-picker>
          </group>
          <span class="change" @click="change"
            ><van-icon name="exchange" />切换就诊人</span
          >
        </h3>
        <p>医院名称：{{ hospName }}</p>
        <p>诊疗卡号：{{ patCode }}</p>
      </div>
      <div class="daijiaoNum">
        代缴单数
        <div>{{ listData.length }}</div>
      </div>
    </div>
    <!-- 提示 -->
    <div class="prompt">
      温馨提示：<br />
      <span
        >目前线上处方缴费仅支持自费患者及社区转诊、共济门诊医保结算类别的东莞市参保患者。</span
      >使用医保电子凭证移动支付前须先激活医保电子凭证。特定门诊、产前检查等医保结算类别或公费医疗患者请持相关有效证件（社保卡/公医证等）前往我院门诊楼1楼收费处缴费。
      <span></span>
    </div>
    <!-- 订单列表 -->
    <div class="list-wrap" v-show="listData.length > 0">
      <div
        class="every-record"
        :key="index"
        v-for="(list, index) in listData"
        v-show="listData.length > 0"
      >
        <group class="cell-group">
          <cell :title="list.deptName" class="cell cell-head">
            <!-- <div>
                         <span class="daizhifu">支付</span>
                      </div> -->
          </cell>
          <cell title="就诊编号" :value="list.visitId" class="cell"></cell>
          <cell title="开方时间" :value="list.visitNo" class="cell"></cell>
          <cell
            title="处方费别"
            :value="list.settleMethodName"
            class="cell"
          ></cell>
          <cell title="处方费" class="cell">
            <div class="pay">
              <span>￥{{ list.payAmount / 100 }}</span>
              <button @click="linkDetail(index)">支付</button>
            </div>
          </cell>
        </group>
      </div>
    </div>
    <!-- 底部的fixed -->
    <div class="footer">
      <div class="left-div">
        待缴总额<span>{{ sumpayAmount(listData) / 100 }}元</span>
      </div>
    </div>
  </div>
</template>

<script>
import { PopupPicker, Group, Cell } from "vux";
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import baseData from "../../config/baseData";
export default {
  name: "index",
  components: {
    PopupPicker,
    Group,
    Cell
  },
  data() {
    return {
      hospName: baseData.webtag,
      patName: [],
      patListData: [
        [
          {
            name: "",
            value: ""
          }
        ]
      ],
      patCode: "",
      listData: [],
      patFamily: [] //家人就诊卡信息列表
    };
  },
  created() {
    //判断是否有住院信息
    if (storage.session.get("patcard") == null) {
      alert("请先绑定门诊卡");
      this.$router.push("/oauth?type=outpatient");
      return;
    }
    this.getPatFamily();
    // this.getcfList();
  },
  methods: {
    getPatFamily() {
      var userinfo = JSON.parse(storage.session.get("user"));
      var data = {
        type: "1",
        id_card: userinfo.idCard,
        openid: userinfo.accountCode
      };
      ajax
        .post(apiUrls.GetUserFamily, data)
        .then(r => {
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无门诊卡~~"
            });
            this.$router.push({
              path: "/addPatient"
            });
            return;
          }
          this.patListData = [[]];
          this.patFamily = r.returnData;

          for (var i = 0; i < r.returnData.length; i++) {
            this.patListData[0].push({
              name: r.returnData[i].patName,
              value: r.returnData[i].patCode
            });
          }
          this.patCode = r.returnData[0].patCode;
          // this.patCode = JSON.parse(storage.session.get("patcard")).patCode;//设置处方的初始就诊人
          this.patName = [];
          this.patName.push(this.patCode);
          //this.patName.push(r.returnData[0].patCode);
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    },
    updateList(item) {
      this.patCode = item[0];
      for (var i = 0; i < this.patFamily.length; i++) {
        //将选中的就诊卡写入缓存
        if (this.patCode == this.patFamily[i].patCode) {
          storage.session.set("patcard", JSON.stringify(this.patFamily[i]));
        }
      }
      this.getcfList();
    },
    //跳转到处方缴费详情
    linkDetail(index) {
      storage.session.set("chooseCf", JSON.stringify(this.listData[index])); //将选中的处方放在缓存中
      this.$router.push({
        path: "/cfDetail",
        query: {
          hospitalId: "",
          visitNo: this.listData[index].recipeId,
          deptId: this.listData[index].deptId,
          doctorId: this.listData[index].doctorId
        }
      });
    },
    getcfList() {
      var data = {
        patCardNo: this.patCode
      };
      ajax
        .post(apiUrls.getcfList, data)
        .then(r => {
          var r = r.data;
          this.listData = [];
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无订单~~"
            });
            return;
          }
          this.listData = r.returnData;
          console.log(this.listData);
          storage.session.set("cfList", JSON.stringify(r.returnData));
          //  console.log(r.returnData);
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    },
    sumpayAmount(list) {
      var sum = 0.0;
      for (var i = 0; i < list.length; i++) {
        try {
          sum += new Number(list[i].payAmount);
        } catch (error) {
          continue;
        }
      }
      return sum.toFixed(2);
    },
    change() {
      this.$refs.picker.onClick();
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.cf-list {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding-bottom: 1rem;
}
.user-info {
  width: 100%;
  padding: 0.24rem;
  background: #fff;
  display: flex;
  align-items: center;
}
.user-info img {
  display: block;
  width: 2rem;
  height: 2rem;
  border-radius: 100%;
}
.user-info .info {
  flex-basis: 0;
  flex-grow: 1;
  padding: 0 0.24rem;
}
.user-info .info h3 {
  text-align: left;
  font-size: 0.28rem;
  font-weight: normal;
  display: flex;
  align-items: center;
}
.user-info .info p {
  font-size: 0.22rem;
  color: #a2a2a2;
}
.user-info .daijiaoNum {
  font-size: 0.22rem;
  color: #a2a2a2;
  text-align: center;
}
.user-info .daijiaoNum div {
  font-size: 0.5rem;
  color: red;
}
.prompt {
  padding: 0.24rem;
  font-size: 0.22rem;
  color: #656766;
  background: #f0faf9;
  border-top: 1px solid #d7dbda;
  border-bottom: 1px solid #d7dbda;
}
.prompt span {
  color: red;
}
.every-record {
  border-radius: 5px;
  background: #fff;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.every-record .cell {
  height: 0.96rem;
  font-size: 0.32rem;
  color: #354052;
}
.every-record .cell:nth-child(2)::before {
  border: none;
}
.every-record .cell-head {
  background: #fdfbfb;
}
.list-wrap {
  padding: 0.24rem 0.24rem 0;
}
.daizhifu {
  color: #24bab8;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1rem;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 0 0.24rem;
  border-top: 1px solid #f0f0f0;
}
.footer .left-div {
  flex: 1;
  font-size: 0.28rem;
  color: #000;
  text-align: right;
}
.footer .left-div span {
  color: red;
  margin-left: 0.3rem;
}
.pay {
  display: flex;
  align-items: center;
  font-size: 0.32rem;
}
.pay span {
  color: #ff0000;
}
.pay button {
  width: 1.4rem;
  height: 0.56rem;
  line-height: 0.56rem;
  border-radius: 3px;
  background: #24bab8;
  color: #fff;
  border: none;
  display: block;
  margin-left: 0.2rem;
}
</style>
<style lang="less" scoped>
.cf-list .info h3 /deep/.weui-cell__ft {
  margin-left: 0;
}
/deep/.weui-cell_access .weui-cell__ft {
  padding-right: 0.14rem;
}
.picker /deep/.weui-cell_access .weui-cell__ft:after {
  border-width: 0;
}
.change {
  font-size: 0.3rem;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 0.04rem;
  color: #fff;
  background: #24bab8;
}
</style>
