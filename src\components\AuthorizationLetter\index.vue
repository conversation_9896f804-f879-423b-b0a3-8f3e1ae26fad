<script>
export default {
  name: "AuthorizationLetter",
  data() {
    return {
      checked: false,
      title: "档案调阅授权同意书"
    };
  },
  computed: {
    content() {
      return `
本人充分理解和同意：东莞市桥头医院微信公众号在获得本人授权的前提下，可向申请调阅健康档案的医院提供本人的相关信息，用于辅助分析本人病情。

一、为此，本人做出以下授权：
（一）本人同意授权东莞市桥头医院微信公众号向医院提供以下数据：
    同意向申请调阅健康档案的医院提供我本人的诊疗信息（含本人住院、门诊/急诊、检查检验报告等），以便辅助分析本人病情。
（二）如本人账号已与亲属账号绑定的，本人同意由该亲属代替本人作出上述档案调阅授权。

二、本人已知悉并完全理解本授权书的所有内容。并自愿作出上述授权，本授权书是本人真实的意思表示，本人同意承担由此带来的一切法律责任后果。本授权书自本人在授权页面“勾选”同意之日起生效。

特此授权！

申请医院：东莞市桥头医院
申请时间：${new Date().toLocaleString()}
      `;
    }
  },
  methods: {
    onCancel() {
      this.$emit("cancel");
    },
    onAgree() {
      this.$emit("agree");
    }
  }
};
</script>

<template>
  <div class="authorization-letter">
    <h1 class="title">{{ title }}</h1>

    <pre class="content">{{ content }}</pre>

    <div class="button-group">
      <van-button round @click="onCancel" class="button">取消</van-button>
      <van-button type="primary" round @click="onAgree" class="button"
        >同意授权</van-button
      >
    </div>
  </div>
</template>

<style lang="less" scoped>
.authorization-letter {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;

  .title {
    font-size: 24px;
    margin-bottom: 8px;
    text-align: center;
  }

  .content {
    white-space: pre-wrap; /* CSS3 */
    white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
    white-space: -pre-wrap; /* Opera 4-6 */
    white-space: -o-pre-wrap; /* Opera 7 */
    word-wrap: break-word; /* Internet Explorer 5.5+ */
    font-family: inherit;
    font-size: 14px;
    margin-bottom: 16px;
  }
  .button-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .button {
      width: 48%;
    }
  }
}
</style>
