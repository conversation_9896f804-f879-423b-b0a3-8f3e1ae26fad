<template>
  <!--清单详情页-->
  <div class="about-time">
    <chufangInfo></chufangInfo>
    <div class="list-group">
      <group class="cell-group">
        <cell title="支付方式" value="微信支付" v-if="payMode == 'WECHAT'">
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            v-bind:src="checkedwx"
          />
        </cell>
        <cell title="支付方式" value="医保支付" v-else-if="payMode == 'YBPAY'">
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            v-bind:src="checkedwx"
          />
        </cell>
        <cell title="支付方式" value="未选择" v-else>
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            v-bind:src="checkedwx"
          />
        </cell>
      </group>
    </div>
    <p v-if="orderinfo.status == 1">
      <img src="../../assets/warn.png" alt />温馨提示请在
      <clocker
        :time="timeoutdate"
        format="%M 分 %S 秒"
        @on-finish="lq()"
      ></clocker
      >内完成支付，逾期订单将会作废，需重新支付
    </p>
    <div v-if="orderinfo.status == 1" class="bottom-btn">
      <button @click="cancelFun()" v-dbClick>取消挂号</button>
      <button @click="WxPay()" v-dbClick>立即支付</button>
    </div>
    <div v-if="orderinfo.status != 1" class="bottom-btn">
      <button @click="closefun()">确 认</button>
    </div>
  </div>
</template>

<script>
import { Cell, Group, Clocker } from "vux";
import { ajax, storage, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
const chufangInfo = () => import("../../components/outpatient/chufangInfo");
// import chufangInfo from '../../components/outpatient/chufangInfo';
import baseData from "../../config/baseData";
export default {
  name: "index",
  components: {
    Cell,
    Group,
    chufangInfo,
    Clocker
  },
  data() {
    return {
      orderinfo: {},
      orderid: "",
      paymoney: "", //支付金额
      timeoutdate: "",
      checkimg: require("../../assets/weixinzhifu.png"),
      checkedwx: "",
      checkedyb: "",
      payMode: "",
      listDatastr: ""
      // yborderid:""
    };
  },
  created() {
    this.getOrder(); //获取订单信息
  },
  mounted() {
    this.getdate();
    // this.WxConfig();
  },
  methods: {
    getOrder() {
      this.orderid = this.$route.query.orderid;
      this.checkedwx = this.checkimg;
      var pdata = { orderid: this.orderid };
      var that = this;
      console.log(pdata);
      ajax
        .post(apiUrls.GetpatientOrderInfo, pdata)
        .then(r => {
          var data = r.data;
          if (!data.success) {
            toolsUtils.alert(data.returnMsg);
            return;
          }
          //console.log(data.returnData);
          this.listDatastr = data.returnData.listDatastr;
          this.payMode = data.returnData.payMode.toUpperCase();
          that.orderinfo = data.returnData;
          var datas = [
            {
              deptId: that.orderinfo.deptId,
              deptName: that.orderinfo.deptName,
              doctorId: that.orderinfo.doctorId,
              doctorName: that.orderinfo.doctorName,
              orderid: that.orderinfo.orderNo,
              patName: that.orderinfo.patName,
              patientidpid: that.orderinfo.patientidpid,
              payAmount: that.orderinfo.payAmout,
              settleMethodId: that.orderinfo.settleMethodId,
              settleMethodName: that.orderinfo.settleMethodName,
              visitId: that.orderinfo.visitId,
              status: that.orderinfo.status,
              hisMsg: that.orderinfo.hisMsg
            }
          ];
          storage.session.set("cfList", JSON.stringify(datas));
          this.paymoney = datas[0].payAmount;
          //如果订单状态为6(异常订单) 则显示异常信息
          if (datas[0].status == 6) {
            alert(datas[0].hisMsg);
          }
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    lq() {
      alert("订单超时，请重新预约!");
      WeixinJSBridge.call("closeWindow");
    },
    getdate() {
      if (this.$route.query.orderid != null) {
        this.orderid = this.$route.query.orderid;
        var pdata = { orderid: this.orderid };
        ajax.post(apiUrls.GetpatientOrderInfo, pdata).then(r => {
          var data = r.data;
          if (!data.success) {
            toolsUtils.alert(data.returnMsg);
            return;
          }
          // console.log(data.returnData.timeoutTime);
          var date = data.returnData.timeoutTime;
          date = new Date(date); //获取当前时间后14分钟

          var outtime = dataUtils.changedatetype(date, "YYYY-MM-DD HH:mm:ss"); //将时间转化格式
          this.timeoutdate = outtime;
          return;
        });
      }
    },
    WxConfig() {
      var url = "https://qtyywx.qtyy.com/cfNowPay";
      var that = this;
      ajax
        .get(apiUrls.GetWxConfig + "?url=" + url.toString())
        .then(r => {
          var Data = r.data.returnData;
          // console.log(Data);
          console.log(r);
          that.$wechat.config({
            debug: false, // 开启调试模式,开发时可以开启
            appId: Data.appid, // 必填，公众号的唯一标识   由接口返回
            timestamp: Data.timeStamp, // 必填，生成签名的时间戳 由接口返回
            nonceStr: Data.nonceStr, // 必填，生成签名的随机串 由接口返回
            signature: Data.Signature, // 必填，签名 由接口返回
            jsApiList: ["checkJsApi", "chooseWXPay"]
          });
        })
        .catch(e => {
          toolsUtils.alert(e.toString());
        });
    },
    WxPay() {
      if (this.payMode == "WECHAT") {
        console.log("进入微信支付");
        var money = this.payamout / 100;

        var user = JSON.parse(storage.session.get("user"));
        var cfList = JSON.parse(storage.session.get("cfList"));
        var that = this;
        //debugger;
        var data = {
          openid: user.accountCode,
          orderid: cfList[0].orderid,
          payamout: cfList[0].payAmount
        };
        console.log(data);
        ajax
          .post(apiUrls.UpdateRegPatOrders, data)
          .then(r => {
            r = r.data;
            console.log(r.returnData);
            if (!r.success) {
              toolsUtils.alert(r.returnMsg);
              //失败后跳转处方缴费页面重新生成订单
              this.$router.push({
                path: "/cfLists"
              });
              return;
            }
            if (r.returnData == null) {
              alert("网络异常！" + r.returnMsg);
              return;
            }
            //  console.log(money);
            //  that.$wechat.chooseWXPay({
            //  timestamp:r.returnData.timeStamp,
            //  nonceStr:r.returnData.nonceStr,
            //  package:r.returnData.package,
            //  signType:'MD5',
            //  paySign:r.returnData.paySign,
            //  success:function(res){
            //   //  storage.session.delete('drInfo');
            //    that.$router.push({path:"/chargeSuccess",query:{money:that.paymoney/100}})
            //      return;
            //  }
            // })
            if (typeof WeixinJSBridge == "undefined") {
              alert("请在微信端打开!" + JSON.stringify(WeixinJSBridge));
              return;
            }
            WeixinJSBridge.invoke(
              "getBrandWCPayRequest",
              {
                appId: r.returnData.appid, //公众号名称，由商户传入
                timeStamp: r.returnData.timeStamp, //时间戳
                nonceStr: r.returnData.nonceStr, //随机串
                package: r.returnData.package, //扩展包
                signType: "MD5", //微信签名方式:MD5
                paySign: r.returnData.paySign //微信签名
              },
              function(res) {
                switch (res.err_msg) {
                  case "get_brand_wcpay_request:cancel":
                    alert("取消支付");
                    break;
                  case "get_brand_wcpay_request:fail":
                    alert(
                      "支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。"
                    );
                    break;
                  case "get_brand_wcpay_request:ok":
                    that.paysuccess();
                    break;
                }
              }
            );
          })
          .catch(e => {
            toolsUtils.alert(e.toString());
          });
      } else if (this.payMode == "YBPAY") {
        this.YbPay();
      }
    },
    YbPay() {
      console.log("进入医保支付");
      var user = JSON.parse(storage.session.get("user"));
      var patcard = JSON.parse(storage.session.get("patcard"));
      var cfList = JSON.parse(storage.session.get("cfList"));
      var requestcontent = storage.session.get("requestcontent"); //JSON.stringify(storage.session.get("requestcontent"));

      var date = new Date();
      function pad2(n) {
        return n < 10 ? "0" + n : n;
      }
      var time =
        date.getFullYear().toString() +
        pad2(date.getMonth() + 1) +
        pad2(date.getDate()) +
        pad2(date.getHours()) +
        pad2(date.getMinutes()) +
        pad2(date.getSeconds());
      var data = {
        order_type: "DiagPay", //RegPay=挂号支付 DiagPay=诊间支付 InHospPay=住院支付
        appid: "wx5342950e076a0d26", //微信分配的公众账号 ID
        mch_id: "**********", //微信支付分配的商户号
        openid: user.accountCode, //openid 和 sub_openid 可以选传其中之一，如果选择传 sub_openid,则必须传 sub_appid。
        hosp_out_trade_no: cfList[0].orderid, //第三方服务商平台自动生成的一个订单号
        hospital_name: "东莞市桥头医院", //医院名称
        total_fee: parseInt(cfList[0].payAmount), //总共需要支付现金金额
        cash_fee: 0, //现金需要支付的金额
        allow_fee_change: 1, //是否允许预结算费用发生变化
        spbill_create_ip: "**************", //"**************",//用户端 ip
        notify_url: "https://qtyywx-api.qtyy.com/HisNotify/YBregPatPayNotifyUrl", //回调 url  API
        //notify_url: "http://michole.wicp.net/HisNotify/YBregPatPayNotifyUrl",//回调 url  API
        body: "处方缴费", //this.listDatastr,//商品描述
        return_url: "https://qtyywx.qtyy.com/ybchargeSuccess", //支付后回跳的页面，不论成功或者失败均会回跳
        pay_type: 2, //支付方式
        city_id: "441901",
        consume_type: 0, //医保部分扣费类型(正式环境)
        //consume_type: 2,//医保部分扣费类型(测试环境)
        insurance_fee: parseInt(cfList[0].payAmount), //医保支付金额
        user_card_type: 1, //证件类型
        user_card_no: patcard.patIdCard, //证件号码
        user_name: patcard.patName, //真实姓名
        serial_no: cfList[0].orderid, //医院 HIS 系统订单号
        org_no: "712010", //医疗机构编码（医保局分配给机构）
        gmt_out_create: time, //医院下单时间
        channel_no: "AAEVuXphB5vVP6b9SJ0ItMAX", //正式渠道号
        // channel_no: "AAHAu_Ba3W8RQBbw2T-SLLHb", //测试渠道号
        request_content: requestcontent, //参考医保结构体（医疗机构透传医保）
        is_dept: "4" //医保电子凭证支付；
      };

      // alert(JSON.stringify(data));
      // console.log(JSON.stringify(data));
      // toolsUtils.alert(JSON.stringify(data));
      // return

      //下单,跳转支付接口
      ajax
        .post(apiUrls.unifiedOrder, data)
        .then(r => {
          //toolsUtils.alert(JSON.stringify(r.data))
          if (r.data.success == true) {
            if (r.data.returnData.result_code == "FAIL") {
              alert(JSON.stringify(r.data.returnData.err_code_des));
              alert(JSON.stringify("系统错误，请重新生成订单"));
              this.$router.go(-2);
              return;
            }
            // toolsUtils.alert(r.data.returnData.med_trans_id)
            // this.yborderid = r.data.returnData.med_trans_id
            // alert(JSON.stringify(r.data.returnData.pay_url));
            // alert(JSON.stringify(r.data.returnData));
            window.location.href = r.data.returnData.pay_url;
          } else {
            toolsUtils.alert("生成支付订单失败");
            return;
          }
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          return;
        });
    },
    cancelFun() {
      var data = {
        orderid: this.orderid
      };
      if (
        !confirm(
          "请注意，已经支付的订单不要点击取消，会导致支付订单失效！是否确认取消？"
        )
      ) {
        return;
      }
      //debugger
      ajax.post(apiUrls.cfCancelRegOrders, data).then(r => {
        var r = r.data;
        if (r.success != true) {
          toolsUtils.alert(r.returnMsg);
          return;
        }
        toolsUtils.alert("取消订单成功！");
        this.$router.push("cfregisterRecord");
      });
    },
    //成功后调用
    paysuccess() {
      alert("正在处理订单...");
      storage.session.delete("drInfo");
      this.$router.push({
        path: "/chargeSuccess",
        query: { money: this.paymoney / 100 }
      });
      return;
      // alert("正在查询订单...");
      // var data={orderno:this.orderid};
      // ajax.post(baseData.apiHost.replace('/api','/')+"HisNotify/regPatPayNotifyUrl",data).then(r=>
      // {
      //    r=r.data;
      //  if(!r.success)
      //  {
      //       alert(r.returnMsg);
      //       this.$router.replace({path:"/cfregisterRecord"});
      //       return;
      //  }
      //   storage.session.delete('drInfo');
      //   this.$router.push({path:"/chargeSuccess",query:{money:this.paymoney/100}})
      // }).catch(e=>{alert(e.toString());return;})
    },
    closefun() {
      //this.YbPay();
      //return
      // this.paysuccess();
      this.$router.go(-1);
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 80px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
  position: fixed;
  bottom: 10px;
  width: 95%;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #000;
  border: 1px solid #24bab8;
}
.bottom-btn button:first-child {
  background: #fff;
  color: #000;
  margin-right: 0.1rem;
}
.bottom-btn button:last-child {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  margin-left: 0.1rem;
}
.pay-methodImg {
  height: 0.48rem;
}
</style>
