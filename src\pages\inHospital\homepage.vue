<template>
  <!--首页-->
  <div class="hospital-page">
    <!-- 病人信息 -->
    <Vheader ref="headddd" @changeParent="changeAdmissionCard"></Vheader>
    <!-- tab -->
    <div class="tab">
      <div :class="TabChange('hospitalization')">
        <router-link to="/homepage">住院信息</router-link>
      </div>
      <div :class="TabChange('payment')">
        <router-link to="/payment">缴费信息</router-link>
      </div>
      <div :class="TabChange('detailedlist')">
        <router-link to="/detailedlist">一日清单</router-link>
      </div>
    </div>
    <!-- tab视图 -->
    <div class="tab-view">
      <transition :name="transitionName">
        <router-view name="chargeView"></router-view>
      </transition>
    </div>

    <go-toIndex class="back-index-btn"></go-toIndex>
  </div>
</template>

<script>
import Vheader from "@/components/inHospital/header";
import GoToIndex from "@/components/inHospital/goBackIndex";
import { storage, toolsUtils } from "../../common";
export default {
  name: "index",
  components: {
    Vheader,
    GoToIndex
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      checkNum: 1,
      transitionName: "",
      account: "",
      admissionCard: ""
    };
  },
  created() {
    this.checkCard();
    this.getSessionData();
  },

  methods: {
    //   tab切换高亮效果
    TabChange(val) {
      if (this.$route.name == val) {
        return "active";
      }
      if (this.$route.name == val) {
        return "active";
      }
      if (this.$route.name == val) {
        return "active";
      }
      return "";
    },
    //检查是否有注册和住院号
    checkCard() {
      if (storage.session.get("user") == null) {
        toolsUtils.alert("请先绑定信息");
        this.$router.push("/oauth?type=register");
        return;
      }
      //判断是否有住院信息
      if (storage.session.get("admissionCard") == null) {
        toolsUtils.alert("没有住院信息，请先绑定住院卡!");
        this.$router.replace("/addPatient");
        return;
      }
    },
    getPaymentMoney(val) {
      this.$refs.headddd.getPaymentMoney(val);
    },
    //获取session的user和卡信息
    getSessionData() {
      this.account = JSON.parse(storage.session.get("user")) || {};
      this.admissionCard =
        JSON.parse(storage.session.get("admissionCard")) || {};
    },
    //切换卡信息改变session的admissionCard值
    changeAdmissionCard(val) {
      this.admissionCard = val;
    }
  },
  computed: {
    //
  },
  watch: {
    $route(to, from) {
      if (to.meta.index > from.meta.index) {
        this.transitionName = "slide-left";
      } else {
        this.transitionName = "slide-right";
      }
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.hospital-page {
  display: flex;
  flex-flow: column;
  height: 100%;
  width: 100%;
}

.hospital-page .tab {
  height: 1.08rem;
  width: 100%;
  display: flex;
  padding-bottom: 0.16rem;
  background: #22b8b6;
  align-items: center;
  position: relative;
  z-index: 2;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.3);
}
.hospital-page .tab div {
  flex-grow: 1;
  flex-basis: 0;
  text-align: center;
  position: relative;
  height: 100%;
}
.hospital-page .tab div a {
  font-size: 0.32rem;
  color: #fff;
  display: block;
  height: 100%;
  line-height: 0.92rem;
}
.hospital-page .tab .active a {
  color: #f1efb3 !important;
}
.hospital-page .tab .active::after {
  display: block;
  content: "";
  width: 0.56rem;
  height: 2px;
  background: #f1efb3;
  position: absolute;
  bottom: 5px;
  left: 50%;
  margin-left: -0.28rem;
}
.hospital-page .tab-view {
  flex-grow: 1;
  overflow: auto;
  flex-basis: 0;
  background: #f0eff6;
}
.hs-news {
  width: 100%;
  padding: 0.24rem;
}
/*定义页面tab切换的动画效果*/
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform, opacity;
  transition: all 400ms ease-out;
  /* position: absolute; */
}
.slide-right-enter {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}
.slide-right-leave-active {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}
.slide-left-enter {
  opacity: 0;
  transform: translate3d(100%, 0, 0);
}
.slide-left-leave-active {
  opacity: 0;
  transform: translate3d(-100%, 0, 0);
}
.back-index-btn {
  position: fixed;
  bottom: 0.24rem;
  left: 0.24rem;
  opacity: 0.7;
}
</style>
