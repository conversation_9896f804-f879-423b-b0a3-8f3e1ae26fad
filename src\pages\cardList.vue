<template>
  <!--就诊人管理-->
  <div class="patientManage-page">
    <div class="head-btn" @click="linkPage()">
      <img src="../assets/Add.png" alt />
      <span>添加健康卡</span>
      <img src="../assets/right.png" alt />
    </div>
    <div class="patient-wrap">
      <ul>
        <li
          :key="index"
          v-for="(item,index) in cardList"
          @click="choiceUserFun(item.patName,index)"
          :class="choiceIndex===index?'li-active':''"
        >
          <span @click="seeData(item)">{{item.patName}} - {{item.patIdCard}}</span>
        </li>
      </ul>
    </div>
    <div class="addBtn-wrap">
      <button @click="btnNew">查看二维码</button>
    </div>
  </div>
</template>

<script>
import { ajax, toolsUtils, storage } from "../common";
import { Qrcode } from "vux";
import JsBarcode from "jsbarcode";
import apiUrls from "../config/apiUrls";
import index from "../store";
export default {
  name: "index",
  components: {
      Qrcode
  },
  data() {
    return {
      title: "首页",
      //   病人列表数据
      id_card: "",
      patientData: [],
      cardList: [],
      btnShow: "",
      choiceUser: {
        name: ""
      },
      choiceIndex: "",
      patNameNow:'',
      patIdCardNow:'',
    };
  },
  methods: {
    btnNew(){
      // console.log(this.patCode);
      if(this.patNameNow === ''){
        confirm("请选择就诊人")
        return
      }
      this.$router.push({
        path:'/personal',
        query:{
          type:0,
          patIdCard:this.patIdCard
        }
      })
    },
    seeData(item){
      // console.log('item',item);
      setTimeout(function(){
                JsBarcode(".barcode").init();
            },1000)
      this.patNameNow = item.patName;
      this.patIdCard = item.patIdCard;
            console.log(item)
    },
    obtain() {
      let data = {
          wxid: storage.session.get("openid"),
      }
      ajax
        .post(apiUrls.getcardList, data)
        .then(r => {
            console.log(r.data.returnData);
            this.cardList =  r.data.returnData;
            return;
        });
      
    },
    //跳转添加就诊人
    linkPage() {
      let data = {
        wxid: storage.session.get("openid"),
      }
      ajax
        .post(apiUrls.registerHealthCard,data)
        .then(r=>{
          if(!r.data.success){
            if(r.data.returnMsg!=""){
                toolsUtils.alert(r.data.returnMsg);
                return
            }
            if(r.data.returnData!=""){
                var url = r.data.returnData;
                // console.log(url);
                window.location.href = url ;
                return
            }
          }
        })
    },
    //选中就诊人
    choiceUserFun(name, index) {
      this.choiceUser.name = name;
      this.choiceIndex = index;
    },
  },
  computed: {
    activeFun: function() {
      return this.btnShow;
    },
  },
  created() {
    this.obtain();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.patientManage-page {
  height: 100%;
  width: 100%;
  background: #ffffff;
  overflow: auto;
}
.patientManage-page h3 {
  font-size: 0.32rem;
  color: #354052;
  height: 1.16rem;
  line-height: 1.16rem;
  background: #fdfbfb;
  padding: 0 0.4rem;
  font-weight: normal;
}
.patient-wrap {
  margin-bottom: 0.8rem;
  overflow: hidden;
}
.patient-wrap ul li {
  display: flex;
  align-items: center;
  padding: 0 0.4rem;
  height: 1.16rem;
  line-height: 1.16rem;
  border-bottom: 1px solid #dfe3e9;
  position: relative;
}
.patient-wrap ul li span {
  flex-grow: 1;
  font-size: 0.32rem;
  color: #354052;
}
.moreImg-div {
  height: 100%;
  display: flex;
  align-items: center;
  width: 0.5rem;
}
.patient-wrap ul li img {
  width: 100%;
  display: block;
}
.addBtn-wrap {
  /* padding: 0 0.4rem; */
  width: 100%;
  height: 0.96rem;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
/* .addBtnR */
.addBtn-wrap button {
  display: block;
  height: 100%;
  width: 80%;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  color: #fff;
  font-size: 0.32rem;
}
.addBtn-wrap button span {
  display: inline-block;
  height: 0.4rem;
  width: 0.4rem;
  border-radius: 100%;
  border: 1px solid #fff;
  margin-right: 0.16rem;
}
.btn-wrap {
  position: absolute;
  right: -100%;
  height: 100%;
  transition: right 300ms ease-in;
}
.btn-wrap button {
  width: 1.44rem;
  height: 100%;
  display: block;
  float: left;
  border: none;
  color: #fff;
  font-size: 0.32rem;
}
.btn-wrap .cancel-btn {
  background: #bfbfbf;
}
.btn-wrap .edit-btn {
  background: #6895ff;
}
.btn-wrap .delete-btn {
  background: #fd7070;
}
.btn-show {
  right: 0 !important;
  transition: right 300ms ease-out;
}
.head-btn {
  height: 1.16rem;
  background: #fdfbfb;
  border-bottom: 1px solid #dfe3e9;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
}
.head-btn span {
  font-size: 0.32rem;
  color: #24bab8;
  flex: 1;
}
.head-btn img {
  height: 0.4rem;
}
.li-active {
  background: #ccc;
}

/* 条形码弹窗 */
.popuper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1200;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.32rem;
  color: #4a4a4a;
}
.popuper .popupDiv {
  width: 80%;
  min-height: 50%;
  background: white;
  border-radius: 8px;
}
.popupNew {
  width: 100%;
  height: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
}
.popupL {
  width: 26%;
  height: 100%;
  display: flex;
  justify-self: start;
  align-items: center;
  margin-left: 4%;
}
.popupR {
  width: 60%;
  height: 100%;
  display: flex;
  justify-self: start;
  align-items: center;
}
.barCodeNew {
  width: 100%;
  min-height: 1.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
}
.popupDiv .bgBtn {
  width: 100%;
  height: 1.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bgBtnL {
  width: 85%;
  height: 0.8rem;
  background: #24bab8;
  color: white;
  border: 1px solid #24bab8;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.2rem;
}
.barcode{
  width: 95%;
}
.main-qrcode{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    height:90%;
    font-size:.4rem;
}
</style>

