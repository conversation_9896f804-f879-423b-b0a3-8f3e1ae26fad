<template>
<!--清单详情页-->
    <div class="detail-page">
        <div>
      <x-table :cell-bordered="false" style="background-color:#fff;">
        <thead>
           <tr>
            <th>项目名称</th>
            <th>规格</th>
            <th>单位</th>
            <th>数量</th>
            <th>金额</th>
           </tr>
          </thead>
          <tbody>
            <tr :key="index" v-for="(i,index) in detailsData">
             <td>{{i.projectName}}</td>
             <td>{{i.spec}}</td>
             <td>{{i.unit}}</td>
             <td>{{i.quantity}}</td>
             <td>{{strtofloat(i.amout)}}</td>
            </tr>
          </tbody>
        </x-table>
      </div>
      <div class="footer-wrap">
          <div>{{costTypeName}}</div>
          <div>￥<span>{{strtofloat(money)}}</span> 共<span>{{num}}</span>项</div>
      </div>
    </div>
</template>

<script>
import { XTable,TransferDomDirective as TransferDom } from "vux";
import {ajax, toolsUtils} from '../../common'
import apiUrls from '../../config/apiUrls'
export default {
  name: "index",
    directives: {
    TransferDom
  },
  components: {
    XTable
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      money:0,
      num:0,
      costTypeName:"",
      detailsData:[]
    };
  },
  methods: {
    detail(){
      var data=this.$route.query.data;
      this.money=data.money;
      console.log(data)
      ajax.post(apiUrls.GetPerBedFeeDetailList,data).then(r=>{
        console.log(r)
        var r= r.data;
        if(r.success==false){
          toolsUtils.alert("请返回上一层");
          return
        }
        this.detailsData=r.returnData;
        console.log(this.detailsData);
        this.num=this.detailsData.length;
        this.costTypeName=this.detailsData[0].costTypeName;
      }).catch(msg=>{
        console.log(msg);
        toolsUtils.toast("网络异常");
      })
    },
    //字符转数字
    strtofloat(str){
       var fval=parseFloat(str);
       fval=fval/100;
       return (fval).toFixed(2);
    }
  },
  computed: {
  },
  created(){
    this.detail();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.detail-page {
  background: #fff;
  height: 100%;
  padding-bottom:1.08rem;
  overflow: auto;
}
.detail-page table {
  line-height: inherit;
}
.detail-page table tr {
  height: 1.08rem;
  line-height: auto;
}
.detail-page table th,
.detail-page table td {
  text-align: left;
  padding: 0.1rem;
}
.detail-page table th {
  font-size: 0.32rem;
  color: #354052;
}
.detail-page table th:nth-child(1) {
  width: 2rem;
}
.detail-page table th:nth-child(2) {
  width: 2rem;
}
.detail-page table td {
  font-size: 0.28rem;
  color: #354052;
}
.footer-wrap{
    height:1.08rem;
    width:100%;
    background: #24BAB8;
    box-shadow: 0 1px 2px -2px rgba(0,0,0,0.50);
    font-size: 18px;
    color: #F1EFB3;
    display: flex;
    align-items: center;
    padding:0 0.28rem;
    position: fixed;
    bottom: 0;
    left:0;
}
.footer-wrap div:last-child{
    flex-grow: 1;
    text-align: right;
    
}
</style>

