<template>
  <!--报告明细页面-->
  <div class="report-detail">
    <header>
      <img src="../../assets/report_3x.png" alt="" />
      <div class="date-div">
        <p>报告单号：{{ Laboratory.VisitNO }}</p>
        <p>报告日期：{{ datetostring(Laboratory.ReportDate) }}</p>
      </div>
      <div class="user-info">
        <span>被检查人</span>
        <p>{{ Laboratory.PatientName }}</p>
      </div>
      <div class="user-img">
        <img src="../../assets/user_face.png" alt="" />
      </div>
    </header>
    <div class="title-div">
      <img src="../../assets/mingxi_3x.png" alt="" />
      <p>检验项目明细</p>
      <span>共{{ Laboratory.LaboratoryItems.laboratoryItems.length }}项</span>
    </div>
    <div class="doc-wrap">
      <div class="doc-img"><img src="../../assets/Oval3x.png" alt="" /></div>
      <div class="doc-info">
        <p>{{ Laboratory.ReportDrName }}医生</p>
        <span>{{ Laboratory.PatientName }}，祝您身体健康!</span>
      </div>
    </div>
    <!-- 报告列表 -->
    <div class="list-wrap">
      <ul>
        <li :key="index" v-for="(lis, index) in Laboratory.LaboratoryItems">
          <div class="project">
            <img src="../../assets/Combined Shape3x.png" alt="" />{{
              Laboratory.TestName
            }}
          </div>
          <div class="detail-wrap">
            <div class="detail-tittle" @click="slideFun(0)">
              <span>项目结果明细</span
              ><img class="down-img" src="../../assets/down2.png" alt="" />
            </div>
            <div class="detail-tittle" @click="getpdf()">
              <span>下载报告</span
              ><img class="down-img" src="../../assets/down2.png" alt="" />
            </div>
            <div class="tab-div">
              <table>
                <thead>
                  <tr>
                    <th>项目名称</th>
                    <th>检查结果</th>
                    <th>单位</th>
                    <th>参考值</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    :key="index2"
                    v-for="(lis2, index2) in lis"
                    v-bind:class="{
                      change: (lis2.NormalFlag || '正常') != '正常'
                    }"
                  >
                    <td>{{ lis2.Name }}</td>
                    <td>{{ lis2.Result }}</td>
                    <td>{{ lis2.Unit }}</td>
                    <td>{{ lis2.Reference }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import $ from "jquery";
import { ajax, storage, toolsUtils, dataUtils } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  data() {
    return {
      title: "",
      visitNo: "",
      Laboratory: { LaboratoryItems: { laboratoryItems: [] } }
    };
  },
  methods: {
    //列表显示
    slideFun(index) {
      console.log(index);
      $(".tab-div")
        .eq(index)
        .slideToggle(300);
      if (
        $(".down-img")
          .eq(index)
          .hasClass("img-rotate")
      ) {
        $(".down-img")
          .eq(index)
          .removeClass("img-rotate");
      } else {
        $(".down-img")
          .eq(index)
          .addClass("img-rotate");
      }
    },
    getpdf() {
      // console.log(this.Laboratory.PDF_where);
      // return
      var pdf = "https://pdfwx.qtyy.com/" + this.Laboratory.PDF_where;
      console.log(pdf);
      if (
        this.Laboratory.PDF_where == null ||
        this.Laboratory.PDF_where == undefined ||
        this.Laboratory.PDF_where == ""
      ) {
        toolsUtils.alert("报告未出，请耐心等待");
      } else {
        window.location.href = pdf;
      }
    },
    getLaboratory() {
      var data = {
        visitNo: this.visitNo
      };
      // console.log(data);
      ajax
        .post(apiUrls.getLaboratory, data)
        .then(r => {
          // console.log(r);
          var r = r.data;
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            return;
          }
          for (
            var i = 0;
            i < r.returnData.LaboratoryItems.laboratoryItems.length;
            i++
          ) {
            //除了null
            if (r.returnData.LaboratoryItems.laboratoryItems[i] == null) {
              r.returnData.LaboratoryItems.laboratoryItems.splice(i, 1);
            }
          }
          this.Laboratory = r.returnData;
          // console.log(this.Laboratory);
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    datetostring(str) {
      if (str != undefined) {
        str = str.replace(/-/g, "/");
      }
      var date = new Date(str);
      return dataUtils.dateToString(date);
    }
  },
  computed: {},
  created() {
    this.visitNo = this.$route.query.visitNo;
    console.log(this.visitNo);
    this.getLaboratory();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.report-detail {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f7fafa;
}
.report-detail header {
  height: 1.28rem;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
  border-bottom: 1px solid rgba(204, 204, 204, 0.5);
  background: #fff;
  margin-bottom: 0.2rem;
}
.report-detail header > img {
  height: 0.48rem;
  display: block;
  margin-right: 0.24rem;
}
.report-detail .date-div {
  font-size: 0.28rem;
  color: #4d4d4d;
  flex: 1;
}
.report-detail .date-div p {
  line-height: 0.4rem;
}
.report-detail .user-info {
  margin: 0 0.1rem;
  text-align: right;
}
.report-detail .user-info span {
  font-size: 12px;
  color: #999999;
  display: block;
}
.report-detail .user-info p {
  font-size: 0.28rem;
  color: #4d4d4d;
}
.report-detail .user-img {
  border-radius: 100%;
  overflow: hidden;
  width: 0.8rem;
  height: 0.8rem;
}
.report-detail .user-img img {
  height: 100%;
  display: block;
}
.title-div {
  border-top: 1px solid rgba(204, 204, 204, 0.5);
  height: 0.88rem;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
  background: #fff;
}
.title-div img {
  height: 0.36rem;
  display: block;
  margin-right: 0.16rem;
}
.title-div p {
  font-size: 0.28rem;
  color: #4d4d4d;
  flex: 1;
}
.title-div span {
  font-size: 0.26rem;
  color: #808080;
}
.doc-wrap {
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
  background: url(../../assets/beijing3x.png) no-repeat center;
  background-size: 100% 100%;
}
.doc-wrap .doc-img {
  height: 0.98rem;
  width: 0.98rem;
  border-radius: 100%;
  overflow: hidden;
  margin: 0 0.24rem 0 0;
}
.doc-wrap .doc-img img {
  height: 100%;
  display: block;
}
.doc-wrap .doc-info p {
  font-size: 0.28rem;
  color: #ffffff;
}
.doc-wrap .doc-info span {
  font-size: 12px;
  color: #ffffff;
  display: block;
}
.list-wrap ul li {
  padding: 0.24rem 0 0.24rem 0.24rem;
  background: #fff;
}
.list-wrap .project {
  display: flex;
  align-items: center;
  font-size: 0.26rem;
  color: #373c40;
  margin-bottom: 0.24rem;
}
.list-wrap .project img {
  height: 0.36rem;
  display: block;
  margin-right: 0.16rem;
}
.list-wrap .detail-wrap {
  background: #f2f2f2;
}
.list-wrap .detail-wrap .detail-tittle {
  height: 0.72rem;
  display: flex;
  align-items: center;
  padding: 0 0.16rem;
  font-size: 0.2rem;
  color: #4d4d4d;
}
.list-wrap .detail-wrap .detail-tittle span {
  flex: 1;
}
.list-wrap .detail-wrap .detail-tittle img {
  width: 0.24rem;
  display: block;
}
.tab-div {
  overflow: hidden;
  padding: 0.08rem 0.24rem 0;
  border-top: 1px solid #dfdfdf;
  display: none;
}
.tab-div table {
  width: 100%;
  text-align: center;
  font-size: 0.2rem;
}
.tab-div table tr {
  min-height: 0.34rem;
  line-height: 0.34rem;
}
.tab-div table thead th {
  color: #000;
  font-weight: normal;
}
.tab-div table tbody {
  color: #646a6f;
}
.tab-div table tr th:first-child,
.tab-div table tr td:first-child {
  text-align: left;
  width: 40%;
}
.tab-div table tr th,
.tab-div table tr td {
  width: 20%;
  padding-right: 2%;
  text-align: center;
}
.tab-div table tr th:last-child,
.tab-div table tr td:last-child {
  padding: 0;
}
.tab-div table tr i {
  font-style: normal;
}
.tab-div table .change {
  color: red;
}
.img-rotate {
  transform: rotate(180deg);
}
</style>
