<template>
  <!--注册页-->
  <div class="register-page">
    <!-- head -->
    <div class="head-logo">
      <img src="../assets/icon/LOGO02.png" alt />
      <span>东莞市桥头医院</span>
    </div>
    <!-- 注册表单 -->
    <div class="form-wrap">
      <form action>
        <div class="input-wrap">
          <label for>
            <input type="text" v-model="accmodel.name" placeholder="姓名" />
          </label>
          <label for>
            <input
              type="text"
              v-model="accmodel.idcard"
              maxlength="18"
              placeholder="身份证号/护照号"
            />
          </label>
          <label for>
            <input
              type="text"
              v-model="accmodel.tel"
              maxlength="11"
              placeholder="手机"
              @input="onInput"
            />
          </label>

          <!-- <label class="last-label">
					<input type="text" v-model="accmodel.yzm" maxlength="6" placeholder="验证码"/>
					</label>
          <button
							type="button"
							:disabled ="canYzms"
							:class="{ 'btn-disabled': canYzm }"
							@click="getYzm"
							class="btn"
						>{{yzmNum}}</button> -->
        </div>

        <input type="button" @click="registerbtn()" value="注册" />
      </form>
    </div>
  </div>
</template>

<script>
import { Cell, Group, Clocker, TransferDomDirective as TransferDom } from "vux"; //测试倒计时
import { dataUtils, ajax, storage } from "../common";
import { toolsUtils } from "../common";
import apiUrls from "../config/apiUrls";

export default {
  directives: {
    TransferDom
  },
  components: {
    // HeaderBar
    Cell,
    Group,
    Clocker
  },
  data() {
    return {
      title: "注册",
      IsBack: false,
      canYzm: false,
      canYzms: false,
      timeWait: 60,
      baseTime: 60,
      yzmNum: "获取验证码",
      accmodel: {
        name: "",
        idcard: "",
        tel: "",
        yzm: ""
      },
      endTime: "" //存储时间用于测试倒计时
    };
  },
  methods: {
    // 根据焦点离开长度判断验证码样式
    onInput() {
      if (this.accmodel.tel.length === 11) {
        this.canYzm = true;
      } else {
        this.canYzm = false;
      }
    },
    registerbtn() {
      if (storage.session.get("openid") == null) {
        this.$router.replace({ path: "/oauth?type=register" });
        return;
      }
      //姓名验证
      if (dataUtils.isName(this.accmodel.name) != true) {
        toolsUtils.toast(dataUtils.isName(this.accmodel.name));
        return;
      }
      //身份证验证
      if (dataUtils.isCard(this.accmodel.idcard) != true) {
        toolsUtils.toast(dataUtils.isCard(this.accmodel.idcard));
        return;
      }
      //手机号验证
      if (dataUtils.isTel(this.accmodel.tel) != true) {
        toolsUtils.toast("请输入正确的手机号");
        return;
      }
      // //验证码认证
      // if (this.accmodel.yzm.length < 5) {
      // 	toolsUtils.toast('请输入正确的验证码');
      // 	return;
      // }

      //直接post数据注册
      var data = {
        name: this.accmodel.name,
        id_card: this.accmodel.idcard,
        tel: this.accmodel.tel,
        openid: storage.session.get("openid") || "",
        smscode: "888888"
        // smscode: this.accmodel.yzm
      };
      if (data.openid == "" || data.id_card == "") {
        toolsUtils.toast("页面加载错误，请刷新页面后重新注册");
        window.location.reload();
      }
      ajax.post(apiUrls.AddUsers, data).then(r => {
        r = r.data;
        if (!r.success) {
          toolsUtils.alert(r.returnMsg);
          return;
        }
        this.$router.replace({ path: "/usercenter" });
        return;
      });
    },
    //获取验证码
    getYzm() {
      if (dataUtils.isTel(this.accmodel.tel) != true) {
        toolsUtils.toast("请输入正确的手机号");
        return;
      }
      //添加获取短信的api
      ajax
        .get(apiUrls.GetVerifyCode + "?phoneNumber=" + this.accmodel.tel)
        .then(r => {
          r = r.data;
          if (!r.success) {
            toolsUtils.toast(r.returnMsg);

            return;
          }
          toolsUtils.toast(r.returnData);
          this.yzmtime();
          this.canYzms = true;
          this.canYzm = false;
        });
    },

    //验证码定时
    yzmtime() {
      let _that = this;
      if (this.timeWait <= 1) {
        this.timeWait = this.baseTime;
        this.yzmNum = "获取验证码";
        this.canYzm = false;
        return;
      } else {
        this.timeWait--;
        this.yzmNum = this.timeWait + "s";
      }
      setTimeout(function() {
        _that.yzmtime();
      }, 1000);
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.register-page {
  height: 100%;
  width: 100%;
  overflow: auto;
}
.head-logo {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 3.28rem;
  color: #fff;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #fff;
  background-image: url("../assets/icon/loginbg.png");
  background-repeat: no-repeat;
  background-size: cover;
}
.head-logo img {
  display: block;
  width: 1.6rem;
  height: 1.6rem;
  margin: 0 auto 0.2rem;
}
.head-logo span {
  font-size: 0.4rem;
  color: #ffffff;
  letter-spacing: -0.02px;
  /* text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5); */
  text-shadow: 1px -2px 1px rgba(0, 0, 0, 0.9);
  text-align: center;
}
.form-wrap {
  padding: 0.48rem;
  /* box-shadow: 0 -1px 5px -1px rgba(0, 0, 0, 0.5); */
}
.form-wrap form > div {
  width: 100%;
  height: 6rem;
  background: #ffffff;
  /* box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.35),
    0 2px 6px 0 rgba(36, 186, 184, 0.4), 0 4px 10px 0 rgba(0, 0, 0, 0.06); */
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  padding: 0.52rem 0.4rem;
  margin-bottom: 0.42rem;
}
.input-wrap {
  display: flex;
  flex-wrap: wrap;
}
.input-wrap label:first-child {
  background-image: url(../assets/userName.png);
  background-repeat: no-repeat;
  background-size: 0.4rem 0.4rem;
}
.input-wrap label:nth-child(2) {
  background-image: url(../assets/IDcard.png);
  background-repeat: no-repeat;
  background-size: 0.4rem 0.28rem;
}
.input-wrap label:nth-child(3) {
  background-image: url(../assets/phone.png);
  background-repeat: no-repeat;
  background-size: 0.28rem 0.4rem;
}
.input-wrap label:nth-child(4) {
  background-image: url(../assets/Code.png);
  background-repeat: no-repeat;
  border-radius: 5px;
  background-size: 0.4rem 0.4rem;
  margin-right: 2.5rem;
}

.input-wrap label {
  height: 0.96rem;
  background: #f0eff6;
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  margin-bottom: 0.28rem;
  flex-basis: 100%;
  align-items: center;
  background-size: 0.4rem;
  background-repeat: no-repeat;
  background-position: center left 0.28rem;
  padding-left: 0.96rem;
}

.input-wrap .last-label {
  position: relative;
}
.input-wrap label input {
  flex: 1;
  border: none;
  background: transparent;
  height: 100%;
  outline: none;
  font-size: 0.32rem;
}
.white {
  background: white;
  height: 0.96rem;
  width: 0.5rem;
}
.input-wrap .last-label input {
  flex: 0;
  width: 130px;
  height: 0.96rem;
}
.input-wrap button {
  position: relative;
  left: 3.7rem;
  top: -1.26rem;
  /* float: left; */
  z-index: 2;
  height: 0.96rem;
  width: 2rem;
  background: #c9c8c9;
  border: 1px solid #b7b7b7;
  border-radius: 5px;
  font-size: 0.32rem;
  color: #ffffff;
  letter-spacing: -0.02px;
}
.form-wrap form input[type="button"] {
  width: 100%;
  display: block;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 0.96rem;
  font-size: 0.32rem;
  color: #ffffff;
}
.btn-disabled {
  background: #3399ea !important;
  color: white !important;
  border-color: #ccc !important;
  font-size: 0.32rem;
  letter-spacing: -0.02px;
}
/* 改变 placeholder 样式*/
.input-wrap input::-webkit-input-placeholder {
  font-size: 0.32rem;
  color: #c9c8c9;
  letter-spacing: -0.02px;
}
.input-wrap input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  font-size: 0.32rem;
  color: #c9c8c9;
  letter-spacing: -0.02px;
}
.input-wrap input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  font-size: 0.32rem;
  color: #c9c8c9;
  letter-spacing: -0.02px;
}
.input-wrap input:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-size: 0.32rem;
  color: #c9c8c9;
  letter-spacing: -0.02px;
}
</style>
