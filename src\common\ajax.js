import axios from 'axios';
import NProgress from 'nprogress'
import { storage } from '../common';
import 'nprogress/nprogress.css'
import { router } from '../router';
import Vue from 'vue';

const defaults = {
  loadingText: '努力加载中...'
};

const settings = {
  baseURL: '',
  token: '',
  requestCount: 0,
  headers: { 'Content-Type': 'application/json' }
};

const closeLoading = () => {
  settings.requestCount--;
  if (settings.requestCount <= 0) {
    // Vue.$vux.loading.hide()
    NProgress.done(); 
  }
};

const request = (method, url, data, options = {}) => {
  options = Object.assign({}, defaults, options, {
    url,
    method,
    data,
    baseURL: options.baseURL || settings.baseURL,
    headers: options.headers || settings.headers,
    crossDomain: true,
    withCredentials: options.withCredentials !== false
  });
  var result = JSON.parse(storage.session.get("token"));
  if(result){
    ajax.setToken(result.access_token);
  }
  if (settings.token) {
    options.headers['Authorization'] = "Bearer " + settings.token;
  }
  settings.requestCount++;
  if (!options.noLoading) {
    // Vue.$vux.loading.show({
    //   text: options.loadingText
    //  })
    NProgress.start();
  }
  return axios.request(options)
    .then(res => {
      closeLoading();
      return res;
    })
    .catch(err => {
      closeLoading();
      if (err.response.status && err.response.status === 401) {
        console.log('登录状态过期,即将跳转登录页');
        storage.session.clear();
        setTimeout(function () {
          location.href = '/oauth';
        }, 1000);
      }
      let errMessage = err.response.data.ErrorMessage;
      if (!options.noErrorTip) {
        console.log(errMessage);
        // messageBox.error(errMessage);
      }
      return Promise.reject(err);
    });
};

export const ajax = {
  setBaseUrl(baseUrl) {
    settings.baseURL = baseUrl;
  },
  setToken(token) {
    settings.token = token;
  },
  get(url, options) {
    return request('get', url, null, options);
  },
  delete(url, data, options) {
    return request('delete', url, data, options);
  },
  post(url, data, options) {
    return request('post', url, data, options);
  },
  put(url, data, options) {
    return request('put', url, data, options);
  }
};
