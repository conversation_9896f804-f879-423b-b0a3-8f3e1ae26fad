<template>
  <div class="dingdanxinxi">
    <div class="list-group">
      <group class="cell-group">
        <cell title="订单号">{{ cflist.orderid }}</cell>
        <cell title="订单状态">
          <div>
            <span style="color: #24BAB8;">{{
              cflist.status | capitalize
            }}</span>
          </div>
        </cell>
      </group>
    </div>

    <div class="list-group">
      <group class="cell-group">
        <cell title="就诊人">{{ cflist.patName }}</cell>
        <cell title="诊疗卡号">{{ cflistPatCode }}</cell>
        <cell title="订单类型" value="处方缴费"></cell>
        <cell title="医院" value="东莞市桥头医院"></cell>
        <cell title="科室">{{ cflist.deptName }}</cell>
        <cell title="医生">{{ cflist.doctorName }}</cell>
        <!-- <cell title="就诊地址" value="医院" ></cell> -->
        <!-- <cell title="就诊时间" >{{cflist.visitNo}}</cell> -->
        <cell title="缴费金额">
          <div>
            <span style="color: #FD7070;">￥{{ cflist.payAmount / 100 }}</span>
          </div>
        </cell>
      </group>
    </div>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
import { toolsUtils, storage, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "index",
  components: {
    Cell,
    Group
  },
  data() {
    return {
      title: "header",
      cflist: {},
      cflistPatCode: ""
    };
  },
  filters: {},
  methods: {
    getCfList() {
      var cfData = JSON.parse(storage.session.get("cfList"));
      var cfPatCode = JSON.parse(storage.session.get("patcard"));
      if (cfData != null) {
        this.cflist = cfData[0];
        this.cflistPatCode = cfPatCode.patCode;
      }
    }
  },
  created() {
    this.getCfList();
    var that = this;
    setTimeout(function() {
      if (that.cfData == null && that.cfData == undefined) {
        that.getCfList();
      }
    }, 2000);
  },
  computed: {},
  filters: {
    capitalize: function(value) {
      if (value == 1) {
        return "预约中(待支付)";
      }
      if (value == 0) {
        return "订单超时(已取消)";
      }
      if (value == 2) {
        return "预约成功(已支付)";
      }
      if (value == 3) {
        return "订单取消(已退款)";
      }
      if (value == 4) {
        return "订单取消";
      }
      if (value == 6) {
        return "异常订单";
      }
      return "未支付";
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dingdanxinxi {
  width: 100%;
}
.dingdanxinxi .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
</style>
