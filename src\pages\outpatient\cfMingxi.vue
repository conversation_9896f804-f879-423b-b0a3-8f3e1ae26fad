<template>
  <!--清单详情页-->
  <div class="about-time">
    <chufangInfo></chufangInfo>
    <div class="list-group">
      <!-- <ul class="list-ul">
             <li :key="index" v-for="(list,index) in cfListData">
                <div class="item-1" @click="showFun(index)">
                    <span>{{list.detailFee+':'+list.detailName}}</span>
                    <img src="../../assets/down.png" alt="" :class="list.show?'img-rotate':''">
                </div>
                <div class="item-2" :class="list.show?'':'item-2_toggle'">
                    <div v-if="list.detailSpec!=null">规格:<span>{{list.detailSpec}}</span></div>
                    <div>单价:<span>{{String(list.detailPrice/100).replace(/^(.*\..{4}).*$/,"$1")}}</span></div>
                    <div>数量:<span>{{list.detailCount}}</span></div>
                    <div>单位:<span>{{list.detailUnit}}</span></div>
                </div>
             </li>
           </ul> -->
    </div>
    <div class="list-group">
      <group class="cell-group">
        <cell title="支付方式" value="微信支付">
          <img
            slot="icon"
            class="pay-methodImg"
            style="display:block;margin-right:5px;"
            src="../../assets/weixinzhifu.png"
          />
        </cell>
      </group>
    </div>
    <div class="bottom-btn">
      <button @click="submitFun()" v-dbClick>提交订单</button>
    </div>
  </div>
</template>

<script>
import { dataUtils, toolsUtils, ajax, storage } from "../../common";
import apiUrls from "../../config/apiUrls";
import { Cell, Group } from "vux";
import chufangInfo from "../../components/outpatient/chufangInfo";
export default {
  name: "index",
  components: {
    Cell,
    Group,
    chufangInfo
  },
  data() {
    return {
      title: "选择科室",
      show: [],
      cfListData: []
    };
  },
  methods: {
    submitFun() {
      var cfList = JSON.parse(storage.session.get("cfList"))[0],
        openid = storage.session.get("openid"),
        patcard = JSON.parse(storage.session.get("patcard")),
        user = JSON.parse(storage.session.get("user")),
        ListDatastr = JSON.stringify(this.cfListData),
        data = {
          openid: openid,
          id_card: user.idCard,
          patCardNo: patcard.patCode,
          orderid: cfList.orderid,
          payAmout: cfList.payAmount,
          visitId: cfList.visitId,
          deptName: cfList.deptName,
          deptId: cfList.deptId,
          doctorId: cfList.doctorId,
          doctorName: cfList.doctorName,
          patName: cfList.patName,
          patTel: user.telePhone,
          detailId: this.$route.query.recipeId,
          listDatastr: ListDatastr
        };
      console.log(data);
      ajax
        .post(apiUrls.addRegPatPreOrders, data)
        .then(r => {
          // console.log(r.data.returnData);
          r = r.data;
          storage.session.set("orderid", r.returnData);
          if (r.success == false) {
            toolsUtils.alert(r.returnMsg);
            if (r.returnMsg == "您有未支付订单，请取消或支付上个处方订单") {
              this.$router.push({
                path: "/cfregisterRecord"
              });
            }
            return;
          }
          this.$router.push({
            path: "/cfnowPay",
            query: {
              orderid: r.returnData,
              detailId: this.$route.query.recipeId
            }
          });
        })
        .catch(e => {
          toolsUtils.alert("程序异常:" + JSON.stringify(e));
        });
    },
    showFun(index) {
      this.cfListData[index].show = !this.cfListData[index].show;
    },
    getNoPayDetailInfo() {
      var data = {
        //patCardNo: this.$route.query.code,
        hospitalId: "",
        visitId: this.$route.query.recipeId,
        deptId: this.$route.query.deptId,
        doctorId: this.$route.query.doctorId
      };
      if (
        data.visitId == null ||
        data.deptId == null ||
        data.doctorId == null
      ) {
        toolsUtils.alert("系统异常，请返回上个页面");
      }
      console.log(data);
      ajax
        .post(apiUrls.getNoPayDetailInfo, data)
        .then(r => {
          console.log(r);
          var r = r.data;
          this.listData = [];
          if (r.success == false) {
            if (
              r.returnMsg ==
              "his接口异常连接数据库失败,错误信息:该患者无未缴费信息"
            ) {
              return;
            }

            toolsUtils.alert(r.returnMsg);
            return;
          }
          if (r.returnData.length == 0) {
            this.$vux.alert.show({
              title: "温馨提示",
              content: "🙈暂无订单~~"
            });
            return;
          }
          this.cfListData = r.returnData;
          console.log(r.returnData.length);
          var listLength = this.cfListData.length;
          for (var i = 0; i < listLength; i++) {
            this.show.push(true);
          }
          console.log(this.show);
        })
        .catch(e => {
          toolsUtils.alert("网络异常");
          console.log(e);
        });
    }
  },
  computed: {},
  created() {
    //  this.getNoPayDetailInfo();
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem 0.24rem 1.56rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 23px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0.24rem;
  background: #f0eff6;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #fff;
  border: 1px solid #24bab8;
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
}
.pay-methodImg {
  height: 0.48rem;
}
.list-ul .item-1 {
  background: #fff;
  display: flex;
  align-items: center;
  height: 0.96rem;
  border-bottom: 1px solid #f5f5f5;
  padding: 0 0.24rem;
}
.list-ul li:last-child .item-1 {
  border: none;
}
.list-ul .item-1 span {
  flex: 1;
}
.list-ul .item-1 img {
  height: 0.4rem;
}
.list-ul .item-2 {
  background: #f5f5f5;
}
.list-ul .item-2 div {
  height: 0.8rem;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
}
.list-ul .item-2 div span {
  flex: 1;
  text-align: right;
}
.list-ul .item-2_toggle {
  display: none;
}
.list-ul .item-1 .img-rotate {
  transform: rotate(180deg);
}
</style>
