<template>
<!--首页-->
    <div class="index-page">
        <swiper loop auto :list="swlists" :index="swindex" class="swiper-wrap" height="100%"></swiper>
        <div class="index-content">
            <!-- switch -->
            <div class="switch-wrap">
                <div>
                   <router-link to="/outpatientPage">
                      <img src="../assets/icon/menzhen.png" alt="">
                      <div>门诊</div>
                   </router-link>
                </div>
                <div>
                   <router-link to="/homepage">
                      <img src="../assets/icon/zhuyuan.png" alt="">
                      <div>住院</div>
                   </router-link>
                </div>
                <div>
                   <!-- <router-link  to="" @click.native="jumptoOutUrl('http://qtyywx-tj.qtyy.com')"> -->
                   <router-link  to="/">
                      <img src="../assets/icon/tijian.png" alt="">
                      <div>体检</div>
                   </router-link>
                </div>
                <div>
                   <router-link to="/userCenter">
                      <img src="../assets/icon/yonghuzhongxin.png" alt="">
                      <div>用户中心</div>
                   </router-link>
                </div>
            </div>
              <section>
                <div class="ToBtter">
                  <span>常用服务</span>
               
                  <router-link to="/outpatientPage">
                  <div class="OnBtter">更多<img src="../assets/more.png" alt=""></div>
                  </router-link>
                
                </div>
                <div class="dor-wrap">
                    <router-link :to="items.link" class="every-dor" v-for="(items,index) in dorDatas" :key="index" >
                        <div class="dor-img">
                            <img :src='items.img' alt="">
                        </div>
                        <div class="dor-info">
                            <h4>{{items.name}}</h4>
                            <p>提示：{{items.tips}}</p>
                            <div>特点：<span v-for="(itemBest,best_index) in items.best" :key="best_index">{{itemBest}}</span></div>
                        </div>
                    </router-link>
                </div>
            </section>
            
            <!-- <section>
                <h3><span>推荐医生</span><span>更多<img src="../assets/more.png" alt=""></span></h3>
                <div class="dor-wrap">
                    <router-link to="" class="every-dor" :key="index" v-for="(items,index) in dorData">
                        <div class="dor-img">
                            <img src="../assets/clinic.png" alt="">
                        </div>
                        <div class="dor-info">
                            <h4>{{items.name}}</h4>
                            <p>医龄：{{items.dorAge}}年<span v-for="(dep,dep_index) in items.depRoom" :key="dep_index">{{dep}}</span></p>
                            <div>擅长：<span v-for="(itemBest,best_index) in items.best" :key="best_index">{{itemBest}}</span></div>
                        </div>
                    </router-link>

                </div>
            </section> -->
 
            <!-- <section>
                <h3><span>热议话题</span><span>更多<img src="../assets/more.png" alt=""></span></h3>
                <div class="topic-wrap">
                    <div class="every-topic" :key="index" v-for="(items,index) in topicData">
                        <h5>{{items.title}}</h5>
                        <p>{{items.content}}</p>
                        <div>#{{items.type}}<span><img src="../assets/read.png" alt="">{{items.read}}</span><span><img src="../assets/comment.png" alt="">{{items.comment}}</span><span><img src="../assets/like.png" alt="">{{items.like}}</span></div>
                    </div>
                    
                </div>
            </section> -->
        </div>
    </div>
</template>

<script>
import { Box, Icon } from "vux";
import { Swiper } from "vux";
export default {
  name: "index",
  components: {
    // HeaderBar
    Swiper,
    Box,
    Icon
  },
  data() {
    return {
      title: "首页",
      IsBack: false,
      //   dorData
      // dorData: [
      //   {
      //     name: "林超",
      //     dorAge: "20",
      //     depRoom: ["妇科", "脚科", "腿科"],
      //     best: ["支气管哮喘", "不孕症"]
      //   },
      //   {
      //     name: "李雪刚",
      //     dorAge: "20",
      //     depRoom: ["妇科", "脚科", "腿科"],
      //     best: ["多囊卵巢", "不孕症"]
      //   },
      //   {
      //     name: "陈离郝",
      //     dorAge: "20",
      //     depRoom: ["脚科", "腿科"],
      //     best: ["椎间盘突出", "腰椎管狭窄症"]
      //   }
      // ],

      //  dorDatas
      dorDatas: [
        // {
        //   name: "预约挂号",
        //   img: require('../assets/icon/yuyueguahao.png'),
        //   link:'/choiceMethod',
        //   tips: '请确保就诊人信息正确。',
        //   best: ["提前预约,避免排队。"]
        // },
        // {
        //   name: "当天挂号",
        //   img:require('../assets/icon/dangtianguahao.png'),
        //   link:'/choiceDepartment?method=0',
        //   tips: '请确保就诊人信息正确。',
        //   best: ["即时预约，方便快捷。"]
        // },
        {
          name: "报告查询",
          img:require('../assets/icon/baogaochaxun.png'),
          link:'/seeReport',
          tips: '请勿将信息透露给他人',
          best: ["在线查看，保护隐私。"]
        }
      ],

      //topicData
      topicData: [
        {
          title: "体检注意事项事项知识",
          content: "基本上每家公司都会安排员工进行每年的体检",
          type: "体检",
          read: "654",
          comment: "11",
          like: "888"
        },
        {
          title: "体检注意事项事项知识",
          content: "基本上每家公司都会安排员工进行每年的体检",
          type: "体检",
          read: "654",
          comment: "11",
          like: "888"
        },
        {
          title: "体检注意事项事项知识",
          content: "基本上每家公司都会安排员工进行每年的体检",
          type: "体检",
          read: "654",
          comment: "11",
          like: "888"
        },
      ],
      swlists: [
        {
          url: "javascript:",
          img: require("../assets/banner01.png")
        },
        {
          url: "javascript:",
          img:  require("../assets/banner02.png")
        }
      ],
      swindex:0
    };
  },
  methods: {
    jumptoOutUrl(url){
        window.location.href=url;
        return;
    }
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.index-page {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #f0eff6;
}
.index-page .swiper-wrap {
  width: 100%;
  height: 4.08rem;
}
.index-content {
  padding: 0 0.4rem;
  margin-top: -1.12rem;
}
.switch-wrap {
  height: 2.24rem;
  width: 100%;
  background: #fff;
  border-radius: 5px;
  display: flex;
  text-align: center;
  align-items: center;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.15),
    0 2px 6px 0 rgba(36, 186, 184, 0.2), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  margin-bottom: 0.28rem;
  position: relative;
  z-index: 2;
}
.switch-wrap > div {
  flex-grow: 1;
}
.switch-wrap > div div {
  font-size: 0.32rem;
  color: #7f8fa4;
}
.switch-wrap img {
  height: 0.84rem;
  width: 0.84rem;
  display: block;
  margin: 0 auto 0.16rem;
}
.index-content section {
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.15),
    0 2px 6px 0 rgba(36, 186, 184, 0.2), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  background: #fff;
  margin-bottom: 0.3rem;
}
.ToBtter{
  display: flex;
    font-weight: normal;
    height: 1.16rem;
    background: #fdfbfb;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    padding: 0 0.28rem;
    font-size: .32rem;
    justify-content: space-between;
    color: #354052;
}
.index-content section h3 span:first-child {
  flex-grow: 1;
  font-size: 0.32rem;
  color: #354052;
}
.index-content section h3 span:last-child {
  font-size: 0.28rem;
  color: #7f8fa4;
}
.OnBtter{
  color: #7f8fa4;
}
.OnBtter img {
  height: 0.3rem;
  vertical-align: middle;
  margin-left: 0.1rem;
  margin-top: -0.04rem;
}
.every-dor {
  display: flex;
  padding: 0.24rem 0.28rem;
  background: #fff;
  align-items: center;
  position: relative;
}
.dor-img {
  height: 1.28rem;
  width: 1.28rem;
  overflow: hidden;
  margin-right: 0.24rem;
  border: 1px solid #dfe3e9;
  border-radius: 5px;
}
.dor-img img {
  display: block;
  height: 100%;
}
.dor-info {
  flex-grow: 1;
  flex-basis: 0;
}
.dor-info::after {
  display: block;
  content: "";
  height: 1px;
  position: absolute;
  bottom: 0;
  right: 0;
  background: #dfe3e9;
  width: calc(100% - 1.76rem);
}
.every-dor:last-child .dor-info::after {
  height: 0;
}
.dor-info h4 {
  font-weight: normal;
  font-size: 0.32rem;
  color: #354052;
}
.dor-info p {
  font-size: 0.28rem;
  color: #7f8fa4;
}
.dor-info p span {
  margin-left: 0.4rem;
}
.dor-info div {
  font-size: 0.28rem;
  color: #24bab8;
}
.dor-info div span {
  margin-right: 0.15rem;
}
.every-topic {
  padding: 0.24rem 0.28rem;
  border-bottom: 1px solid #dfe3e9;
}
.every-topic h5 {
  font-size: 0.32rem;
  color: #24bab8;
}
.every-topic p {
  font-size: 0.28rem;
  color: #7f8fa4;
}
.every-topic div {
  font-size: 0.28rem;
  color: #b9cbe2;
}
.every-topic div span {
  margin-left: 0.4rem;
}
.every-topic div span img {
  height: 0.28rem;
  vertical-align: middle;
  margin-top: -3px;
  margin-right: 0.08rem;
}

</style>

