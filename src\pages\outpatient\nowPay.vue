<template>
<!--清单详情页-->
    <div class="about-time">

        <dingdanxinxi   @orderid='getorderid' @money='getmoney' @timeoutdate='gettimeoutdate'></dingdanxinxi>
        <div class="list-group">
           <group class="cell-group">
             <cell title="支付方式" value="微信支付">
                <img slot="icon" class="pay-methodImg" style="display:block;margin-right:5px;" src="../../assets/weixinzhifu.png">
             </cell>
           </group>
        </div>
        <p><img src="../../assets/warn.png" alt="">温馨提示请在
          <clocker :time="timeoutdate" format="%M 分 %S 秒" @on-finish="lq()"></clocker>内完成支付，逾期号将会作废，需重新挂号
        </p>
        <!-- <div class="bottom-btn"><button @click="cancelFun()" v-dbClick>取消挂号</button><button @click="WxPay()" v-dbClick>立即支付</button></div> -->
        <div class="bottom-btn">
          <button @click="cancelFun()" v-dbClick>取消挂号</button>
          <button @click="noWxPay()" v-dbClick>立即挂号</button>
        </div>
    </div>
</template>

<script>
import { Cell, Group,Clocker } from "vux";
import { ajax,storage,toolsUtils,dataUtils} from "../../common";
import apiUrls from '../../config/apiUrls';
import dingdanxinxi from '../../components/outpatient/dingdanxinxi';
import baseData from '../../config/baseData';
export default {
  name: "index",
  components: {
    Cell,
    Group,
    dingdanxinxi,
    Clocker
  },
  //LQ
  data() {
    return {
      title: "选择科室",
      orderid:'',
      payamout:'',
      timeoutdate:'',
 
    };
  },

  created(){
    // this.tt();
  },
  mounted(){
    // this.getinfo();
    this.getdate();
    // this.WxConfig();
    var that=this;
    setTimeout(function(){
      that.noWxPay();
    },1000)
 
  },
  methods: {
    lq(){
      alert("订单超时，请重新预约!")
      WeixinJSBridge.call('closeWindow');
    },
    getorderid(val){
      this.orderid=val.orderid;
      // this.payamout=JSON.parse(storage.session.get('drinfo').treatFee);
      // console.log(JSON.parse(storage.session.get('drinfo')))
    },
    getmoney(val){
      this.payamout=val.val;
    },
    gettimeoutdate(val){
      console.log(val);
      //   if (val.val.search("T")!=-1)
      //   {
      //     this.timeoutdate=val.val.split("T")[0]+" "+val.val.split("T")[1];
      //     return;
      //   }
      if(val==undefined)
        {
          this.timeoutdate = "";
          
        }
        this.timeoutdate = val.val.replace("T00:00:00",' ').replace('T',' ').replace("+08:00","");

    },
    getdate(){
        if ( this.$route.query.orderid== null) {
          var date=new Date(new Date().getTime()+1000*60*14);
          var outtime= dataUtils.changedatetype(date,"YYYY-MM-DD HH:mm:ss");
          this.timeoutdate=outtime;
          return;
        }
    },
    WxPay(){
       var money=this.payamout*100;
        // toolsUtils.alert(money);
       var user=JSON.parse(storage.session.get("user"));
       var that=this;
      //  if(this.dordata.payamout=="0")
      //  {
      //        toolsUtils.alert("请选择金额！")
      //        return
      //  }
       var data={
         openid:user.accountCode,
         orderid:this.orderid,

         payamout: parseInt(this.payamout*100),

       };
       if(data["orderid"]=='' || data["orderid"]==null || data["orderid"]==undefined)
       {
          alert("网络异常，请重新生成订单！");
          return;
       }
       console.log(data)
       ajax.post(apiUrls.UpdateRegOrders,data).then(r=>{
         r=r.data;
         console.log(r.returnData);
         if(!r.success)
         {
              toolsUtils.alert(r.returnMsg);
              return;
         }
         if(r.returnData==null)
         {
           alert("网络异常！"+r.returnMsg);
           return;
         }
        //  console.log(money);
        //jssdk 方式
        //  that.$wechat.chooseWXPay({
        //  timestamp:r.returnData.timeStamp,
        //  nonceStr:r.returnData.nonceStr,
        //  package:r.returnData.package,
        //  signType:'MD5',
        //  paySign:r.returnData.paySign,
        //  success:function(res){
        //    storage.session.delete('drInfo');
        //    that.$router.replace({path:"/chargeSuccess",query:{money:money/100}})
        //      return;
        //  }
        // })

          if (typeof WeixinJSBridge == "undefined"){
            alert("请在微信端打开!"+JSON.stringify(WeixinJSBridge));
            return;
          } 

          WeixinJSBridge.invoke('getBrandWCPayRequest', {
              "appId": r.returnData.appid, //公众号名称，由商户传入
              "timeStamp":r.returnData.timeStamp, //时间戳
              "nonceStr": r.returnData.nonceStr, //随机串
              "package": r.returnData.package,//扩展包
              "signType": "MD5", //微信签名方式:MD5
              "paySign": r.returnData.paySign //微信签名
          }, function (res) {
              switch(res.err_msg){
                  case 'get_brand_wcpay_request:cancel':   
                      alert("取消支付");                            
                      break; 
                  case 'get_brand_wcpay_request:fail': 
                      alert("支付失败，可能的原因：签名错误、未注册APPID、项目设置APPID不正确、注册的APPID与设置的不匹配、其他异常等。"); 
                      break; 
                  case 'get_brand_wcpay_request:ok': 
                      that.paysuccess();     
                      break; 
              }                                    
          });

       }).catch(e=>{
         toolsUtils.alert(e.toString()
         )
       })
    },
    
    //添加不需要支付预约
    noWxPay()
    {
       var user=JSON.parse(storage.session.get("user"));
       var drInfo=JSON.parse(storage.session.get("drInfo"));
       var that=this;
       var data={
         openid:user.accountCode,
         orderid:this.orderid,
       };
       if(data["orderid"]=='' || data["orderid"]==null || data["orderid"]==undefined)
       {
          alert("网络异常，请重新生成订单！");
          return;
       }

       ajax.post(apiUrls.noWxPayRegOrders,data).then(r=>{
         r=r.data;
         console.log(r.returnData);
         if(!r.success)
         {
              toolsUtils.alert(r.returnMsg);
              return;
         }
         if(drInfo.deptId=='1110')
         {
          this.$router.replace({
            path: "/cfList"
          });
          return;
         }

//          if(drInfo.deptId=='1110')
//          {
//             setTimeout(function(){
//               window.location.href="/appointment";
//             },3000)
//             toolsUtils.alert("核酸检测前，请先填写相关个人资料。");
//          }
          storage.session.delete('drInfo');
          that.$router.replace({path:"/PaySuccess",query:{orderid:that.orderid}})
          return;    
       }).catch(e=>{
         toolsUtils.alert(e.toString()
         )
       })

    },
    cancelFun(data){
      var data={
        orderid:this.orderid
      };
      var that=this;
      ajax.post(apiUrls.CancelRegOrders,data).then(r=>{
        var r=r.data;
        if(r.success!=true)
        {
          toolsUtils.alert(r.returnMsg);
          return;
        }
        toolsUtils.alert('取消订单成功！');
        // window.location.href='/registerRecord';
        setTimeout(function(){
          that.$router.replace('registerRecord');
        },1500)
      })

    },
    getinfo(){
    console.log(JSON.parse(storage.session.get("drInfo")));
    },
    //成功后调用
    paysuccess()
    {    
      alert("正在处理订单...");
      storage.session.delete('drInfo');
      this.$router.push({path:"/chargeSuccess",query:{money:this.payamout/100}})
      return;
        // alert("正在查询订单...");
        // var data={orderno:this.orderid};
        // ajax.post(baseData.apiHost.replace('/api','/')+"HisNotify/RegPayNotifyUrl",data).then(r=>
        // {
        //    r=r.data;
        //  if(!r.success)
        //  {
        //       alert(r.returnMsg);
        //       this.$router.replace({path:"/registerRecord"});
        //       return;
        //  }     
        //   var money=this.payamout*100;
        //   storage.session.delete('drInfo');
        //   this.$router.replace({path:"/chargeSuccess",query:{money:money/100}});
        // }).catch(e=>{alert(e.toString());return;})
    }
  },
  computed: {},
  created() {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

.about-time {
  height: 100%;
  width: 100%;
  overflow: auto;
  background: #f0eff6;
  padding: 0.24rem;
}
.about-time .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.weui-cell:first-child::before {
  border: none;
}
.weui-cell {
  height: 0.96rem;
  padding: 0 0.3rem;
}
.about-time > p {
  font-size: 14px;
  color: #b5b5b5;
  margin: 9px 0 80px;
}
.about-time > p img {
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.bottom-btn {
  display: flex;
  position: fixed;
    bottom: 10px;
    width: 95%;
}
.bottom-btn button {
  border-radius: 5px;
  height: 1.08rem;
  flex-grow: 1;
  font-size: 0.36rem;
  display: block;
  color: #fff;
  border: 1px solid #24bab8;
}
.bottom-btn button:first-child {
  background:#fff;
  color: #24BAB8;
  margin-right:0.1rem;
}
.bottom-btn button:last-child {
  background-image: linear-gradient(-180deg, #3ad1cf 0%, #22b8b6 100%);
  margin-left:0.1rem;
}
.pay-methodImg{
  height:0.48rem;
}
</style>