<template>
  <!--添加就诊人-->
  <div class="register-page">
    <van-notice-bar
      color="#24bab8"
      background="#fffdf4" 
      left-icon="volume-o"
      text="护照证件类型和暂无身份证的人员请到线下挂号窗口建卡。"
    />
    <!-- 注册表单 -->
    <div class="form-wrap">
      <div class="input-wrap">
        <h3>请填写就诊人信息</h3>
        <!-- <div class="sex-wrap"> -->

        <!-- <label for="" class=""><input type="text" placeholder="姓名" v-model="accmodel.name"></label> -->

        <!-- </div> -->
        <label for class>
          <input type="text" placeholder="请输入姓名" v-model="accmodel.name" maxlength="20" @blur="getbirthdayandsex(0)"/>
        </label>
        <!-- <div class="radioFlex">
          <div class="radioList">
            <input
              type="radio"
              id="cardFalse"
              name="card"
              @click="setCard(1)"
              value="cardFalse"
              checked
            />
            <label for="cardFalse">无本院诊疗卡</label>
          </div>

          <div class="radioList">
            <input type="radio" id="cardTrue" name="card" @click="setCard(2)" value="cardTrue" />
            <label for="cardTrue">院内已有诊疗卡</label>
          </div>
        </div> -->
        <!-- <label for=""><input type="text" placeholder="有效证件号码" v-model="accmodel.id_card"></label> -->
        <label for>
          <input
            type="text"
            :maxlength="maxNow"
            :placeholder="(num=='1'?numVlaue:'请输入住院号')"
            v-model="accmodel.admissionCardNo"
            @blur="getbirthdayandsex(1)"
          />
        </label>

        <div class="radioFlex">
          <div :class="[checkedMan?'radioList':'radioListN']">
            <input
              type="radio"
              id="man"
              name="drone"
              @click="setSex(1)"
              value="man"
              :checked="checkedMan"
              :disabled="readonlyMan"
            />
            <label for="man">男</label>
          </div>

          <div :class="[checkedWoman?'radioList':'radioListN']">
            <input
              type="radio"
              id="woman"
              name="drone"
              @click="setSex(2)"
              value="woman"
              :checked="checkedWoman"
              :disabled="readonlyWoman"
            />
            <label for="woman">女</label>
          </div>
        </div>
        <label for>
          <input
            type="text"
            placeholder="请选择出生日期"
            v-model="accmodel.bday"
            readonly
            @click="getnewdate"
          />
        </label>
        <label for>
          <select v-model="accmodel.relation" @change="getSecVal()">
            <option
              :value="secList.num"
              v-for="(secList,index) in relation"
              :key="index"
            >{{secList.relationName}}</option>
          </select>
        </label>



        <label for>
          <select v-model="accmodel.patCardNo">
            <option
              :value="secList.val"
              v-for="(secList,index) in hiscardlist"
              :key="index"
            >{{secList.name}}</option>
          </select>
        </label>



        <label for>
          <input type="text" placeholder="请输入手机号码" v-model="accmodel.tel" maxlength="11" />
        </label>
        <label for class="last-label">
          <input type="text" placeholder="请输入手机验证码" v-model="accmodel.yzm" maxlength="6" />
          <button
            type="button"
            :disabled="canYzm"
            :class="{ 'btn-disabled': canYzm }"
            @click="getYzm"
            class="btn"
          >{{yzmNum}}</button>
        </label>
        <!-- <button type="button" class="btn-disabled" disabled>获取验证码</button> -->
      </div>
      <input type="submit" value="保存" @click="patientAdd()" />
    </div>

    <!-- <div class="prompt-content">
      弘康大医汇综合门诊部温馨提醒您：如遇绑卡失败问题，可致电
      <a href="tel:020-83227888">020-83227888</a>。您的健康就是我们最大的幸福！
    </div>-->
  </div>
</template>

<script>
import { ajax, storage, toolsUtils, dataUtils } from "../common";
import apiUrls from "../config/apiUrls";
import { PopupRadio } from "vux";

export default {
  name: "index",
  components: {
    PopupRadio,
  },
  data() {
    return {
      checkedMan: false,
      checkedWoman: false,
      readonlyMan: true,
      readonlyWoman: true,
      optionsValue: 1,
      numVlaue: "请输入身份证",
      maxNow: "18",
      title: "添加就诊人",
      canYzm: false,
      timeWait: 60,
      num: "",
      baseTime: 60,
      yzmNum: "获取验证码",
      accmodel: {
        name: "",
        tel: "",
        yzm: "",
        admissionCardNo: "",
        relation: "0",
        type: "2",
        patCardNo: "0",
        bday: "",
        sex: "",
      },
      hiscardlist:[{val:0,name:'就诊卡'}],
      //关系下拉框
      relation: [
        {
          num: 0,
          relationName: "自己",
        },
        {
          num: 1,
          relationName: "父母",
        },
        {
          num: 2,
          relationName: "配偶",
        },
        {
          num: 3,
          relationName: "子女",
        },
        {
          num: 4,
          relationName: "其他",
        },
      ],
      secVal: 1,
    };
  },
  created() {
    this.getLinkData();
  },
  methods: {
    //获取传过来的数据
    getLinkData() {
      // console.log(this.$route.query.num);
      this.num = this.$route.query.num;
    },

    getnewdate() {
      switch (this.optionsValue) {
        case 1:
          return;
          break;
        case 2:
          var that = this;
          let nowDate = new Date();
          let nowYear = nowDate.getFullYear();
          let nowMonth = nowDate.getMonth();
          let nowDay = nowDate.getDate();
          let endDate = `${nowYear}-${nowMonth + 1}-${nowDay}`;
          this.$vux.datetime.show({
            cancelText: "取消",
            confirmText: "确定",
            startDate: "1900-01-01",
            endDate: endDate,
            format: "YYYY-MM-DD",
            onConfirm(val) {
              that.accmodel.bday = val;
            },
            onShow() {},
            onHide() {},
          });
          break;
        default:
          break;
      }
    },

    //添加家人
    patientAdd() {
      var userInfo = JSON.parse(storage.session.get("user"));
      if (storage.session.get("openid") == null) {
        this.$router.push({ path: "/oauth?type=register" });
        return;
      }
      //姓名验证
      if (dataUtils.isPatName(this.accmodel.name) != true) {
        // toolsUtils.alert(dataUtils.isPatName(this.accmodel.name));
        alert(dataUtils.isPatName(this.accmodel.name));
        return;
      }

      // //如果选择身份证就进行身份证验证
      // if (this.optionsValue == "A") {
      //   if (dataUtils.isCardID(this.accmodel.admissionCardNo) != true) {
      //     toolsUtils.toast(dataUtils.isCardID(this.accmodel.admissionCardNo));
      //     return;
      //   }
      // }

      //手机号验证
      if (dataUtils.isTel(this.accmodel.tel) != true) {
        // toolsUtils.alert("请输入正确的手机号");
        alert("请输入正确的手机号");
        return;
      }
      //验证码认证
      if (this.accmodel.yzm.length < 5) {
        // toolsUtils.alert("请输入正确的验证码");
        alert("请输入正确的手机号");
        return;
      }
      //验证卡号
      if (
        this.accmodel.admissionCardNo == null ||
        this.accmodel.admissionCardNo.length < 5
      ) {
        // toolsUtils.alert("请输入大于5位数的卡号");
        alert("请输入大于5位数的卡号");
        return;
      }

      //验证性别
      if (this.accmodel.sex == "" || this.accmodel.sex == null) {
        // toolsUtils.alert("请选择性别");
        alert("请选择性别");
        return;
      }
      //验证出生日期
      if (this.accmodel.bday == "" || this.accmodel.bday == null) {
        // toolsUtils.alert("请选择出生日期");
        alert("出生日期");
        return;
      }

      if (this.num == "1") {
        var data = {
          name: this.accmodel.name,
          id_card: userInfo.idCard,
          tel: this.accmodel.tel,
          smscode: this.accmodel.yzm,
          patCardNo: (this.accmodel.patCardNo!='0'?this.accmodel.patCardNo:this.accmodel.admissionCardNo),
          relation: this.accmodel.relation,
          type: "1",
          sex: this.accmodel.sex,
          patBirth: this.accmodel.bday,
        };
        console.log(data);
        ajax
          .post(apiUrls.AddPatFamilyType, data)
          .then((r) => {
            console.log(r);
            r = r.data;
            if (!r.success) {
              if (r.returnMsg == "当前卡信息错误！") {
                // toolsUtils.alert(
                //   "您输入就诊卡信息与院内不一致，请联系客服咨询"
                // );
                alert("您输入就诊卡信息与院内不一致，请联系客服咨询");
              } else {
                // toolsUtils.alert(r.returnMsg);
                alert(r.returnMsg);
              }
              return;
            }
            this.$router.back(-1);
            return;
          })
          .catch((e) => {
            // toolsUtils.alert("网络异常");
            alert("网络异常");
            console.log(e);
          });
      } else {
        //直接post数据注册
        var data = {
          name: this.accmodel.name,
          id_card: userInfo.idCard,
          tel: this.accmodel.tel,
          smscode: this.accmodel.yzm,
          admissionCardNo: this.accmodel.admissionCardNo,
          relation: this.accmodel.relation,
          type: "2",
          patCardNo: "",
        };
        // console.log(data);
        ajax
          .post(apiUrls.BindAdmissionCard, data)
          .then((r) => {
            console.log(r);
            r = r.data;
            if (!r.success) {
              // toolsUtils.alert(r.returnMsg);
              alert(r.returnMsg);
              return;
            }
            if (storage.session.get("admissionCard") == null) {
              storage.session.set(
                "admissionCard",
                JSON.stringify(r.returnData)
              );
            }
            this.$router.back(-1);
            return;
          })
          .catch((e) => {
            // toolsUtils.alert("网络异常");
            alert("网络异常");
            console.log(e);
          });
      }
    },
 
    //选择卡号
    getSecVal() {
      console.log(this.accmodel.relation);
    },

    setSex(val) {
      this.accmodel.sex = val;
      if (val === 1) {
        this.checkedMan = true;
        this.checkedWoman = false;
      } else {
        this.checkedWoman = true;
        this.checkedMan = false;
      }
      console.log("sex", val);
    },
    setCard(val) {
      console.log("setCard", val);

      this.optionsValue = val;
      this.checkedMan = false;
      this.checkedWoman = false;
      if (val === 1) {
        this.numVlaue = "请输入身份证";
        this.maxNow = "18";
        this.accmodel.admissionCardNo = "";
        this.accmodel.bday = "";
        this.accmodel.sex = "";
        this.readonlyWoman = true;
        this.readonlyMan = true;
      } else {
        this.numVlaue = "请输入诊疗卡号";
        this.maxNow = "";
        this.accmodel.admissionCardNo = "";
        this.accmodel.bday = "";
        this.accmodel.sex = "";
        this.readonlyWoman = false;
        this.readonlyMan = false;
      }
    },
    //LQ通过身份证获取用户的出生年月和性别
    getbirthdayandsex(val) {

      if(val==0&&(this.accmodel.admissionCardNo==""||this.accmodel.admissionCardNo==null))
      {
        return;
      }

      if (this.optionsValue == 1) {
        if (dataUtils.isCardID(this.accmodel.admissionCardNo) != true) {
          toolsUtils.toast(dataUtils.isCardID(this.accmodel.admissionCardNo));
          // alert(dataUtils.isCardID(val));
          return;
        }
        let sex = this.accmodel.admissionCardNo.substring(16, 17);
        let brithday = this.accmodel.admissionCardNo.substring(6, 14);
        //性别判断
        //女
        if (sex % 2 == 0) {
          this.setSex(2);
        }
        //男
        else {
          this.setSex(1);
        }
        //生日获取
        this.accmodel.bday =
          brithday.substring(0, 4) +
          "-" +
          brithday.substring(4, 6) +
          "-" +
          brithday.substring(6);
        console.log("性别：" + this.accmodel.sex);
        console.log("生日：" + this.accmodel.bday);
        //将性别和生日选项改为只读
        switch (this.accmodel.sex) {
          case 1:
            this.checkedMan = true;
            this.checkedWoman = false;
            break;
          case 2:
            this.checkedMan = false;
            this.checkedWoman = true;
            break;
          default:
            break;
        }
        this.GetUserCard();
      }
    },

    //获取用户所有门诊卡
    GetUserCard(){
      if(!this.accmodel.name)
      {
        toolsUtils.toast("请输入姓名");
        return;
      }
      if(!this.accmodel.admissionCardNo)
      {
        alert("请输入证件号");
        return;
      }
      var data = {
          patName: this.accmodel.name,
          id_card: this.accmodel.admissionCardNo,
        };
        var that=this;
      ajax
        .post(apiUrls.getUserCard,data)
        .then((r) => {
          console.log(r);
          r = r.data;
          storage.session.set(
                "UserCard",
                JSON.stringify(r.returnData)
              );
          if (!r.success) {
            // toolsUtils.alert(r.returnMsg);
            alert(r.returnMsg);
            return;
          }
          if(r.returnData.length>0)
          {
            that.hiscardlist=r.returnData;
            that.accmodel.patCardNo=that.hiscardlist[0].val;
          }
 
          // toolsUtils.alert(r.returnData);
        });
    },

    //获取验证码
    getYzm() {
      if (dataUtils.isTel(this.accmodel.tel) != true) {
        // toolsUtils.alert("请输入正确的手机号");
        alert("请输入正确的手机号");
        return;
      }
      console.log("点击验证码进来了");
      this.canYzm = true;

      //添加获取短信的api
      ajax
        .get(apiUrls.GetVerifyCode + "?phoneNumber=" + this.accmodel.tel)
        .then((r) => {
          console.log(r);
          r = r.data;
          if (!r.success) {
            // toolsUtils.alert(r.returnMsg);
            alert(r.returnMsg);
            return;
          }
          // toolsUtils.alert(r.returnData);
          alert(r.returnData);
          this.yzmtime();
        });
    },

    //验证码定时
    yzmtime() {
      let _that = this;
      if (this.timeWait <= 1) {
        this.timeWait = this.baseTime;
        this.yzmNum = "获取验证码";
        this.canYzm = false;
        return;
      } else {
        this.timeWait--;
        this.yzmNum = this.timeWait + "s";
      }
      setTimeout(function () {
        _that.yzmtime();
      }, 1000);
    },
  },
  computed: {},
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.register-page {
  height: 100%;
  width: 100%;
  overflow: auto;
}
.form-wrap {
  padding: 0.48rem;
}
.form-wrap > div {
  width: 100%;
  background: #ffffff;
  border-radius: 5px;
  padding: 0 0.4rem 0.52rem;
  margin-bottom: 0.42rem;
  box-shadow: 0 2px 4px 0 rgba(241, 239, 179, 0.35),
    0 2px 6px 0 rgba(36, 186, 184, 0.4), 0 4px 10px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}
.input-wrap {
  display: flex;
  flex-wrap: wrap;
}
.input-wrap .last-label {
  position: relative;
}
.input-wrap label {
  /* height: 1.2rem;
  background: #f6f6f6;
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  margin-bottom: 0.28rem;
  flex-basis: 100%;
  align-items: center;
  background-size: 0.4rem;
  background-repeat: no-repeat;
  background-position: center left 0.28rem; */
  height: 0.8rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  border-bottom: 1px solid #d4d4d4;
  margin-bottom: 0.28rem;
  -webkit-flex-basis: 100%;
  flex-basis: 100%;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  background-size: 0.4rem;
  background-repeat: no-repeat;
  background-position: center left 0.28rem;
}
.input-wrap .last-label input {
  width: 100%;
  padding-right: 1.66rem;
}

.input-wrap label input {
  flex-grow: 1;
  border: none;
  background: transparent;
  height: 100%;
  outline: none;
  padding: 0 0.28rem;
  font-size: 0.3rem !important;
}
.input-wrap label input::-webkit-input-placeholder {
  color: #a5a5a5;
  font-weight: 600;
}
.input-wrap label select {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  padding: 0 0.28rem;
  font-size: 0.3rem !important;
}
.input-wrap .last-label button {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
  height: 0.6rem;
  width: 2rem;
  background: #f1efb3;
  border: 1px solid #edea82;
  border-radius: 5px;
  font-size: 0.28rem;
  color: #24bab8;
}
.form-wrap input[type="submit"] {
  width: 100%;
  display: block;
  background-image: linear-gradient(-180deg, #75d2d1 0%, #24bab8 100%);
  border: 1px solid #24bab8;
  border-radius: 5px;
  height: 0.96rem;
  font-size: 0.3rem;
  color: #ffffff;
}
.btn-disabled {
  background: #ccc !important;
  color: #fff !important;
  border-color: #ccc !important;
}
/********************/
.input-wrap h3 {
  font-size: 0.32rem;
  color: #354052;
  height: 1.16rem;
  flex-basis: 100%;
  flex-grow: 1;
  background: #fdfbfb;
  margin: 0 -0.4rem 0.3rem;
  line-height: 1.16rem;
  padding: 0 0.4rem;
}
.sex-wrap {
  display: flex;
  flex-grow: 1;
}
/* 单选框样式 */
.radioFlex {
  display: flex;
  height: 0.96rem;
  flex-grow: 1;
  font-size: 0.32rem;
  color: #7f8fa4;
}
.radioList {
  width: 100%;
  height: 24px;
}
.radioList input[type="radio"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
}
.radioList input[type="radio"] + label::before {
  content: "";
  background: #ffffff;
  border: 1px solid #c2c7cb;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  float: left;
  margin-right: 6px;
  margin-top: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.radioListN {
  width: 100%;
  height: 24px;
}
.radioListN input[type="radio"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
}
.radioListN input[type="radio"] + label::before {
  content: "";
  background: #ffffff;
  border: 1px solid #c2c7cb;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  float: left;
  margin-right: 6px;
  margin-top: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.radioList input[type="radio"]:checked + label::before {
  background-color: #24bab8;
  background-clip: content-box;
  padding: 3px;
  margin-top: 1px;
}

/* .checkbox-wrap {
  font-size: 0.32rem;
  color: #7f8fa4;
  display: flex;
  align-items: center;
  height: 0.96rem;
  flex-grow: 1;
} */
/* .checkbox-wrap div {
  flex-grow: 1;
}
.checkbox-wrap div input {
  display: none;
}
.checkbox-wrap div label {
  margin-bottom: 0;
  background: none;
  box-shadow: none;
}
.checkbox-wrap div label::before {
  display: block;
  content: "";
  height: 0.4rem;
  width: 0.4rem;
  border-radius: 100%;
  border: 1px solid #ccc;
  margin-right: 0.1rem;
} */
/* .checkbox-wrap div input:checked ~ label::before {
  background: #24bab8;
} */
.prompt-content {
  padding: 0.28rem;
  text-indent: 1.6em;
  font-size: 0.28rem;
  color: #9b9b9b;
}
input::-webkit-input-placeholder {
  /* WebKit browsers */
  font-size: 0.3rem;
}
input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  font-size: 0.3rem;
}
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  font-size: 0.3rem;
}
input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  font-size: 0.3rem;
}
</style>
<style>
.weui-cell {
  width: 100% !important;
  font-size: 0.3rem !important;
}
</style>

