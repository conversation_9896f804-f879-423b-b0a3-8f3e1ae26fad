import { store } from '../store';
import { ajax, storage } from '../common';
import baseData from '../config/baseData'
import $ from 'jquery';

let needCheck = true;

const Base64_Encode=(str)=>{
  var c1, c2, c3;
  var base64EncodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
  var i = 0, len = str.length, string = '';

  while (i < len) {
      c1 = str.charCodeAt(i++) & 0xff;
      if (i === len) {
          string += base64EncodeChars.charAt(c1 >> 2);
          string += base64EncodeChars.charAt((c1 & 0x3) << 4);
          string += "==";
          break;
      }
      c2 = str.charCodeAt(i++);
      if (i === len) {
          string += base64EncodeChars.charAt(c1 >> 2);
          string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
          string += base64EncodeChars.charAt((c2 & 0xF) << 2);
          string += "=";
          break;
      }
      c3 = str.charCodeAt(i++);
      string += base64EncodeChars.charAt(c1 >> 2);
      string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
      string += base64EncodeChars.charAt(((c2 & 0xF) << 2) | ((c3 & 0xC0) >> 6));
      string += base64EncodeChars.charAt(c3 & 0x3F);
  }
  return string;
}

const toWeChatOauth=(type)=>{
  let wxurl= baseData.authorize_url+'appid='+baseData.appid+'&redirect_uri='+
  baseData.redirect_uri+'&response_type=code&scope=snsapi_base&state='+type+'&connect_redirect=1#wechat_redirect';
  return wxurl;
};

const getToken=(code,pwd)=>{
    var authBasic = Base64_Encode("hospital:1234");
    var headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Basic ' + authBasic
    };
    var data = {
        grant_type: 'password',
        username: code,
        password: pwd
    }
    return new Promise((resolve, reject) => {
    $.ajax({
        type: "POST",
        //url:baseData.apiHost.replace('api','Token'),
        url:"https://qtyywx-api.qtyy.com/Token",
        // url:"http://localhost:50631/Token",
        data: data,
        headers:headers,
        success: function (data) {
            console.log('授权完成');
            var result={};
            result.access_token=data['access_token'];
            result.expiresUtc=data['.expires'];
            result.issuedUtc=data['.issued'];
            result.refreshToken=data['refresh_token'];
            result.expires_in=data['expires_in'];
            result.timestamp= (result.expires_in+parseInt((new Date().valueOf()/1000).toString()));
            result.userName=data['userName'];
            storage.session.set("token", JSON.stringify(result));
            ajax.setToken(result.access_token);
            resolve(result.access_token);
        },
        error:function(e){
          console.log(e);
          resolve('err');
        }
    });
    });
};

export const authService = {
    Base64_Encode,
    toWeChatOauth,
    getToken
};
