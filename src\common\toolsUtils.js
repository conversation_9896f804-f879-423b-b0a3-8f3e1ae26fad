//基础工具
import Vue from 'vue'


export const toolsUtils = {

    //暂停线程时间
    sleep(numberMillis) {
        var now = new Date();
        var exitTime = now.getTime() + numberMillis;
        while (true) {
            now = new Date();
            if (now.getTime() > exitTime)
                return;
        }
    },
    alert(msg) {
        Vue.$vux.alert.show({
            title: '提示',
            content: msg
        })
    },
    getNow(){
      var now=new Date();
      var data=now.getFullYear()+"-"+(now.getMonth()+1)+"-"+now.getDate()+"  "+now.getHours()+":"+now.getMinutes()
      return data;
    },
    test(){
      var now=new Date();
      var data=now.getFullYear()+"-"+(now.getMonth()+1)+"-"+now.getDate()+"  "+now.getHours()+":"+(now.getMinutes()+15)
      return data;
    },
    // 显示
    toast(msg) {
        // 显示
        Vue.$vux.toast.show({
            text: msg,
            type:'text',
            time:3000,
            position:'middle'
        })
    },
    /**
   * 函数防抖 (只执行最后一次点击)
   * @param fn
   * @param delay
   * @returns {Function}
   * @constructor
   */
  Debounce(fn, t) {
    let delay = t || 500;
    var that = this;
    let args = arguments;
    if (that.Debouncetimer) {
      clearTimeout(that.Debouncetimer);
    }
    that.Debouncetimer = setTimeout(() => {
      that.Debouncetimer = null;
      fn.apply(this, args);
    }, delay);
  },
}
