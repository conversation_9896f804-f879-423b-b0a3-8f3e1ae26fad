<template>
  <div class="dingdanxinxi">
    <div class="list-group">
      <group class="cell-group">
        <cell title="订单号">{{ cflist.orderNo }}</cell>
        <cell title="订单状态">
          <div>
            <span style="color: #24BAB8;">已支付</span>
          </div>
        </cell>
      </group>
    </div>
    <div class="list-group">
      <group
        v-show="xy != []"
        :key="'x' + index"
        v-for="(item, index) in xy"
        class="cell-group"
      >
        <cell style="color: #9a0000;" title="西药取药地址：">
          <span style="color: #9a0000;">{{ item.xyPoint }}</span>
        </cell>
        <cell title="处方号">
          <span>{{ item.hisOrderId }}</span>
        </cell>
        <svg
          class="barcode"
          :jsbarcode-value="item.hisOrderId"
          jsbarcode-width="4"
        ></svg>
      </group>
      <group
        v-show="zy != []"
        :key="'z' + index"
        v-for="(item, index) in zy"
        class="cell-group"
      >
        <cell style="color: #9a0000;" title="中药取药地址：">
          <span style="color: #9a0000;">{{ item.zyPoint }}</span>
        </cell>
        <cell title="处方号">
          <span>{{ item.hisOrderId }}</span>
        </cell>
        <svg
          class="barcode"
          :jsbarcode-value="item.hisOrderId"
          jsbarcode-width="4"
        ></svg>
      </group>
    </div>
    <div
      v-show="jc != []"
      :key="'j' + index"
      v-for="(item, index) in jc"
      class="list-group2"
    >
      <group class="cell-group ">
        <cell style="color: #9a0000;" title="检查地址：">
          <span style="color: #9a0000;">{{ item.jcPoint }}</span>
        </cell>
        <svg
          class="barcode"
          :jsbarcode-value="item.hisOrderId"
          jsbarcode-width="4"
        ></svg>
        <cell
          :key="index + 'item'"
          v-for="(item2, index) in itemsfn(item.items)"
          >{{ item2 }}</cell
        >
      </group>
      <!-- <group v-show="xy!=[]" :key="'q'+index" v-for="(item,index) in qt"   class="cell-group">
              <cell style="color: red;" title="其他信息：">
                  <span style="color: red;">{{item.qtPoint}}</span>
              </cell>
              <svg class="barcode" :jsbarcode-value="item.hisOrderId" jsbarcode-width='4' > </svg>
           </group> -->
    </div>
    <div
      v-show="jy != []"
      :key="'j' + index"
      v-for="(item, index) in jy"
      class="list-group2"
    >
      <group class="cell-group ">
        <cell style="color: #9a0000;" title="检验地址：">
          <span style="color: #9a0000;">{{ item.jyPoint }}</span>
        </cell>
        <svg
          class="barcode"
          :jsbarcode-value="item.hisOrderId"
          jsbarcode-width="4"
        ></svg>
        <cell
          :key="index + 'item'"
          v-for="(item2, index) in itemsfn(item.items)"
          >{{ item2 }}</cell
        >
      </group>
    </div>
    <div class="list-group">
      <group class="cell-group">
        <cell title="就诊人">{{ cflist.patName }}</cell>
        <cell title="诊疗卡号">{{ cflist.patCardNo }}</cell>
        <cell title="订单类型" :value="getValueBasedOnPayMode()"></cell>
        <cell title="医院" value="东莞市桥头医院"></cell>
        <cell title="科室">{{ cflist.deptName }}</cell>
        <cell title="医生">{{ cflist.doctorName }}</cell>
        <cell title="支付时间">{{ cflist.payTime }}</cell>
        <cell
          title="结算号"
          v-if="cflist.payMode != '0' && cflist.payMode != null"
          >{{ cflist.hisPayOrderNo }}</cell
        >
        <cell title="缴费金额">
          <div>
            <span style="color: #FD7070;">￥{{ cflist.payAmout / 100 }}</span>
          </div>
        </cell>
        <cell title="现金支付" v-if="cflist.payMode == 'YBPAY'">
          <div>
            <span style="color: #FD7070;">￥{{ cflist.xjfy / 100 }}</span>
          </div>
        </cell>
        <cell title="统筹支付" v-if="cflist.payMode == 'YBPAY'">
          <div>
            <span style="color: #FD7070;">￥{{ cflist.tcfy / 100 }}</span>
          </div>
        </cell>
        <cell title="个账支付" v-if="cflist.payMode == 'YBPAY'">
          <div>
            <span style="color: #FD7070;">￥{{ cflist.grfy / 100 }}</span>
          </div>
        </cell>
      </group>
    </div>
    <div class="list-group">
      <ul class="list-ul">
        <li :key="index" v-for="(list, index) in cfListData">
          <div class="item-1" @click="showFun(index)">
            <span>{{ list.detailFee + ":" + list.detailName }}</span>
            <img
              src="../../assets/down.png"
              alt=""
              :class="list.show ? 'img-rotate' : ''"
            />
          </div>
          <div class="item-2" :class="list.show ? '' : 'item-2_toggle'">
            <div v-if="list.detailSpec != null">
              规格:<span>{{ list.detailSpec }}</span>
            </div>
            <div>
              单价:<span>{{
                String(list.detailPrice / 100).replace(/^(.*\..{4}).*$/, "$1")
              }}</span>
            </div>
            <div>
              数量:<span>{{ list.detailCount }}</span>
            </div>
            <div>
              单位:<span>{{ list.detailUnit }}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { Cell, Group } from "vux";
import JsBarcode from "jsbarcode";
import { toolsUtils, storage, ajax } from "../../common";
import apiUrls from "../../config/apiUrls";
export default {
  name: "index",
  components: {
    Cell,
    Group
  },
  data() {
    return {
      title: "header",
      cflist: {},
      cflistPatCode: "",
      xy: [],
      zy: [],
      jc: [],
      qt: [],
      jy: [],
      cfListData: []
    };
  },
  filters: {},
  methods: {
    getCfOrderInfo() {
      if (this.$route.query.orderid != null) {
        //toolsUtils.alert('查看订单');
        var data = {
          orderid: this.$route.query.orderid
        };
        ajax
          .post(apiUrls.GetpatientOrderInfo, data)
          .then(r => {
            data = r.data;
            if (data.returnData == null) {
              toolsUtils.alert(data.returnMsg);
            }
            console.log(data.returnData);
            this.cflist = data.returnData;
            this.cflist.payTime = this.cflist.payTime.split("T")[0];
            try {
              this.cfListData = JSON.parse(this.cflist.listDatastr);
            } catch (error) {
              this.cfListData = [];
            }
            try {
              this.xy = JSON.parse(this.cflist.xyStr);
            } catch (error) {
              this.xy = [];
            }
            try {
              this.jc = JSON.parse(this.cflist.jcStr);
            } catch (error) {
              this.jc = [];
            }
            try {
              this.zy = JSON.parse(this.cflist.zyStr);
            } catch (error) {
              this.zy = [];
            }
            try {
              this.jy = JSON.parse(this.cflist.jyStr);
            } catch (error) {
              this.jy = [];
            }
            try {
              this.qt = JSON.parse(this.cflist.qtStr);
            } catch (error) {
              this.xy = [];
            }
            setTimeout(function() {
              JsBarcode(".barcode").init();
            }, 1000);

            // JsBarcode("#barcode",this.cflist.hisPayOrderNo,{ width: 4 });
          })
          .catch(e => {
            toolsUtils.alert("网络异常");
          });
        return;
      }
    },
    getValueBasedOnPayMode() {
      return this.cflist.payMode === "WECHAT" ? "处方缴费" : "医保支付";
    },
    itemsfn(items) {
      if (items == null || items == undefined) {
        return [];
      }
      var strarr = items.split(",");
      strarr.pop();
      return strarr;
    },
    showFun(index) {
      this.cfListData[index].show = !this.cfListData[index].show;
    }
  },
  created() {
    this.getCfOrderInfo();
  },
  computed: {}
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.dingdanxinxi {
  width: 100%;
}
.dingdanxinxi .list-group {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.barcode {
  width: 95%;
}

.list-group2 {
  background: #fff;
  border-radius: 5px;
  font-size: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.24rem;
}
.list-ul .item-1 {
  background: #fff;
  display: flex;
  align-items: center;
  height: 0.96rem;
  border-bottom: 1px solid #f5f5f5;
  padding: 0 0.24rem;
}
.list-ul li:last-child .item-1 {
  border: none;
}
.list-ul .item-1 span {
  flex: 1;
}
.list-ul .item-1 img {
  height: 0.4rem;
}
.list-ul .item-2 {
  background: #f5f5f5;
}
.list-ul .item-2 div {
  height: 0.8rem;
  display: flex;
  align-items: center;
  padding: 0 0.24rem;
}
.list-ul .item-2 div span {
  flex: 1;
  text-align: right;
}
.list-ul .item-2_toggle {
  display: none;
}
.list-ul .item-1 .img-rotate {
  transform: rotate(180deg);
}
</style>
